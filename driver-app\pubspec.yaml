name: driver
description: A new Flutter project.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  dio: ^5.7.0
  http: ^1.2.2
  intl: ^0.19.0
  image: ^4.2.0
  get_it: ^8.0.0
  jiffy: ^6.3.1
  lottie: ^3.1.2
  shimmer: ^3.0.0
  auto_route: ^9.1.0
  workmanager: ^0.5.1
  flutter_svg: ^2.0.10+1
  card_swiper: ^3.0.1
  image_picker: ^1.1.2
  sms_autofill: ^2.2.0
  google_fonts: ^6.2.1
  url_launcher: ^6.1.14
  flutter_remix: ^0.0.3
  path_provider: ^2.0.11
  google_sign_in: ^6.2.1
  pull_to_refresh: ^2.0.0
  flutter_riverpod: ^2.1.3
  flutter_slidable: ^3.1.1
  connectivity_plus: ^6.0.4
  percent_indicator: ^4.2.2
  freezed_annotation: ^2.2.0
  flutter_rating_bar: ^4.0.1
  flutter_screenutil: ^5.9.0
  shared_preferences: ^2.0.16
  cached_network_image: ^3.3.0
  calendar_date_picker2: ^1.1.5
  top_snackbar_flutter: ^3.0.0+1
  flutter_native_splash: ^2.2.16
  flutter_advanced_switch: ^3.0.1
  flutter_polyline_points: ^2.1.0
  map_launcher: ^3.3.1
  auto_size_text: ^3.0.0
  intl_phone_field: ^3.2.0

  #maps:
  geolocator: ^13.0.1
  google_maps_flutter: ^2.9.0


  #firebase:
  firebase_core: ^3.4.0
  firebase_auth: ^5.2.0
  firebase_messaging: ^15.0.4

  charts_flutter:
    git:
      url: https://github.com/Muhammadyunusxon/charts.git
      path: charts_flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  freezed: ^2.5.2
  build_runner: ^2.4.9
  flutter_lints: ^5.0.0
  auto_route_generator: ^9.0.0
  flutter_launcher_icons: ^0.14.1

flutter:
  uses-material-design: true
  assets:
    - assets/image/
    - assets/svg/
    - assets/lottie/

flutter_native_splash:
  background_image: 'assets/image/splash.png'
  android_12:
  fullscreen: true

flutter_icons:
  android: "launcher_icon"
  ios: true
  min_sdk_android: 23
  remove_alpha_ios: true
  image_path: 'assets/image/delivery.png'