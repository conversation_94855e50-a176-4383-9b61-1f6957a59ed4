// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_edit_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProfileEditState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isEmailEditable => throw _privateConstructorUsedError;
  bool get isPhoneEditable => throw _privateConstructorUsedError;
  bool get isFirstnameError => throw _privateConstructorUsedError;
  bool get isLastnameError => throw _privateConstructorUsedError;
  bool get isPasswordError => throw _privateConstructorUsedError;
  bool get isConfirmPasswordError => throw _privateConstructorUsedError;
  bool get showPassword => throw _privateConstructorUsedError;
  bool get showConfirmPassword => throw _privateConstructorUsedError;
  String get firstname => throw _privateConstructorUsedError;
  String get lastname => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  String get confirmPassword => throw _privateConstructorUsedError;

  /// Create a copy of ProfileEditState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileEditStateCopyWith<ProfileEditState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileEditStateCopyWith<$Res> {
  factory $ProfileEditStateCopyWith(
          ProfileEditState value, $Res Function(ProfileEditState) then) =
      _$ProfileEditStateCopyWithImpl<$Res, ProfileEditState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isEmailEditable,
      bool isPhoneEditable,
      bool isFirstnameError,
      bool isLastnameError,
      bool isPasswordError,
      bool isConfirmPasswordError,
      bool showPassword,
      bool showConfirmPassword,
      String firstname,
      String lastname,
      String phone,
      String email,
      String password,
      String confirmPassword});
}

/// @nodoc
class _$ProfileEditStateCopyWithImpl<$Res, $Val extends ProfileEditState>
    implements $ProfileEditStateCopyWith<$Res> {
  _$ProfileEditStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileEditState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isEmailEditable = null,
    Object? isPhoneEditable = null,
    Object? isFirstnameError = null,
    Object? isLastnameError = null,
    Object? isPasswordError = null,
    Object? isConfirmPasswordError = null,
    Object? showPassword = null,
    Object? showConfirmPassword = null,
    Object? firstname = null,
    Object? lastname = null,
    Object? phone = null,
    Object? email = null,
    Object? password = null,
    Object? confirmPassword = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailEditable: null == isEmailEditable
          ? _value.isEmailEditable
          : isEmailEditable // ignore: cast_nullable_to_non_nullable
              as bool,
      isPhoneEditable: null == isPhoneEditable
          ? _value.isPhoneEditable
          : isPhoneEditable // ignore: cast_nullable_to_non_nullable
              as bool,
      isFirstnameError: null == isFirstnameError
          ? _value.isFirstnameError
          : isFirstnameError // ignore: cast_nullable_to_non_nullable
              as bool,
      isLastnameError: null == isLastnameError
          ? _value.isLastnameError
          : isLastnameError // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordError: null == isPasswordError
          ? _value.isPasswordError
          : isPasswordError // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirmPasswordError: null == isConfirmPasswordError
          ? _value.isConfirmPasswordError
          : isConfirmPasswordError // ignore: cast_nullable_to_non_nullable
              as bool,
      showPassword: null == showPassword
          ? _value.showPassword
          : showPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      showConfirmPassword: null == showConfirmPassword
          ? _value.showConfirmPassword
          : showConfirmPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      firstname: null == firstname
          ? _value.firstname
          : firstname // ignore: cast_nullable_to_non_nullable
              as String,
      lastname: null == lastname
          ? _value.lastname
          : lastname // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      confirmPassword: null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileEditStateImplCopyWith<$Res>
    implements $ProfileEditStateCopyWith<$Res> {
  factory _$$ProfileEditStateImplCopyWith(_$ProfileEditStateImpl value,
          $Res Function(_$ProfileEditStateImpl) then) =
      __$$ProfileEditStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isEmailEditable,
      bool isPhoneEditable,
      bool isFirstnameError,
      bool isLastnameError,
      bool isPasswordError,
      bool isConfirmPasswordError,
      bool showPassword,
      bool showConfirmPassword,
      String firstname,
      String lastname,
      String phone,
      String email,
      String password,
      String confirmPassword});
}

/// @nodoc
class __$$ProfileEditStateImplCopyWithImpl<$Res>
    extends _$ProfileEditStateCopyWithImpl<$Res, _$ProfileEditStateImpl>
    implements _$$ProfileEditStateImplCopyWith<$Res> {
  __$$ProfileEditStateImplCopyWithImpl(_$ProfileEditStateImpl _value,
      $Res Function(_$ProfileEditStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileEditState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isEmailEditable = null,
    Object? isPhoneEditable = null,
    Object? isFirstnameError = null,
    Object? isLastnameError = null,
    Object? isPasswordError = null,
    Object? isConfirmPasswordError = null,
    Object? showPassword = null,
    Object? showConfirmPassword = null,
    Object? firstname = null,
    Object? lastname = null,
    Object? phone = null,
    Object? email = null,
    Object? password = null,
    Object? confirmPassword = null,
  }) {
    return _then(_$ProfileEditStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailEditable: null == isEmailEditable
          ? _value.isEmailEditable
          : isEmailEditable // ignore: cast_nullable_to_non_nullable
              as bool,
      isPhoneEditable: null == isPhoneEditable
          ? _value.isPhoneEditable
          : isPhoneEditable // ignore: cast_nullable_to_non_nullable
              as bool,
      isFirstnameError: null == isFirstnameError
          ? _value.isFirstnameError
          : isFirstnameError // ignore: cast_nullable_to_non_nullable
              as bool,
      isLastnameError: null == isLastnameError
          ? _value.isLastnameError
          : isLastnameError // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordError: null == isPasswordError
          ? _value.isPasswordError
          : isPasswordError // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirmPasswordError: null == isConfirmPasswordError
          ? _value.isConfirmPasswordError
          : isConfirmPasswordError // ignore: cast_nullable_to_non_nullable
              as bool,
      showPassword: null == showPassword
          ? _value.showPassword
          : showPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      showConfirmPassword: null == showConfirmPassword
          ? _value.showConfirmPassword
          : showConfirmPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      firstname: null == firstname
          ? _value.firstname
          : firstname // ignore: cast_nullable_to_non_nullable
              as String,
      lastname: null == lastname
          ? _value.lastname
          : lastname // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      confirmPassword: null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ProfileEditStateImpl extends _ProfileEditState {
  const _$ProfileEditStateImpl(
      {this.isLoading = false,
      this.isEmailEditable = true,
      this.isPhoneEditable = true,
      this.isFirstnameError = false,
      this.isLastnameError = false,
      this.isPasswordError = false,
      this.isConfirmPasswordError = false,
      this.showPassword = false,
      this.showConfirmPassword = false,
      this.firstname = '',
      this.lastname = '',
      this.phone = '',
      this.email = '',
      this.password = '',
      this.confirmPassword = ''})
      : super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isEmailEditable;
  @override
  @JsonKey()
  final bool isPhoneEditable;
  @override
  @JsonKey()
  final bool isFirstnameError;
  @override
  @JsonKey()
  final bool isLastnameError;
  @override
  @JsonKey()
  final bool isPasswordError;
  @override
  @JsonKey()
  final bool isConfirmPasswordError;
  @override
  @JsonKey()
  final bool showPassword;
  @override
  @JsonKey()
  final bool showConfirmPassword;
  @override
  @JsonKey()
  final String firstname;
  @override
  @JsonKey()
  final String lastname;
  @override
  @JsonKey()
  final String phone;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final String password;
  @override
  @JsonKey()
  final String confirmPassword;

  @override
  String toString() {
    return 'ProfileEditState(isLoading: $isLoading, isEmailEditable: $isEmailEditable, isPhoneEditable: $isPhoneEditable, isFirstnameError: $isFirstnameError, isLastnameError: $isLastnameError, isPasswordError: $isPasswordError, isConfirmPasswordError: $isConfirmPasswordError, showPassword: $showPassword, showConfirmPassword: $showConfirmPassword, firstname: $firstname, lastname: $lastname, phone: $phone, email: $email, password: $password, confirmPassword: $confirmPassword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileEditStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isEmailEditable, isEmailEditable) ||
                other.isEmailEditable == isEmailEditable) &&
            (identical(other.isPhoneEditable, isPhoneEditable) ||
                other.isPhoneEditable == isPhoneEditable) &&
            (identical(other.isFirstnameError, isFirstnameError) ||
                other.isFirstnameError == isFirstnameError) &&
            (identical(other.isLastnameError, isLastnameError) ||
                other.isLastnameError == isLastnameError) &&
            (identical(other.isPasswordError, isPasswordError) ||
                other.isPasswordError == isPasswordError) &&
            (identical(other.isConfirmPasswordError, isConfirmPasswordError) ||
                other.isConfirmPasswordError == isConfirmPasswordError) &&
            (identical(other.showPassword, showPassword) ||
                other.showPassword == showPassword) &&
            (identical(other.showConfirmPassword, showConfirmPassword) ||
                other.showConfirmPassword == showConfirmPassword) &&
            (identical(other.firstname, firstname) ||
                other.firstname == firstname) &&
            (identical(other.lastname, lastname) ||
                other.lastname == lastname) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.confirmPassword, confirmPassword) ||
                other.confirmPassword == confirmPassword));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isEmailEditable,
      isPhoneEditable,
      isFirstnameError,
      isLastnameError,
      isPasswordError,
      isConfirmPasswordError,
      showPassword,
      showConfirmPassword,
      firstname,
      lastname,
      phone,
      email,
      password,
      confirmPassword);

  /// Create a copy of ProfileEditState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileEditStateImplCopyWith<_$ProfileEditStateImpl> get copyWith =>
      __$$ProfileEditStateImplCopyWithImpl<_$ProfileEditStateImpl>(
          this, _$identity);
}

abstract class _ProfileEditState extends ProfileEditState {
  const factory _ProfileEditState(
      {final bool isLoading,
      final bool isEmailEditable,
      final bool isPhoneEditable,
      final bool isFirstnameError,
      final bool isLastnameError,
      final bool isPasswordError,
      final bool isConfirmPasswordError,
      final bool showPassword,
      final bool showConfirmPassword,
      final String firstname,
      final String lastname,
      final String phone,
      final String email,
      final String password,
      final String confirmPassword}) = _$ProfileEditStateImpl;
  const _ProfileEditState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get isEmailEditable;
  @override
  bool get isPhoneEditable;
  @override
  bool get isFirstnameError;
  @override
  bool get isLastnameError;
  @override
  bool get isPasswordError;
  @override
  bool get isConfirmPasswordError;
  @override
  bool get showPassword;
  @override
  bool get showConfirmPassword;
  @override
  String get firstname;
  @override
  String get lastname;
  @override
  String get phone;
  @override
  String get email;
  @override
  String get password;
  @override
  String get confirmPassword;

  /// Create a copy of ProfileEditState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileEditStateImplCopyWith<_$ProfileEditStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
