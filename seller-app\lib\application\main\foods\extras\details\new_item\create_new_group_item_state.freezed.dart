// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_new_group_item_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreateNewGroupItemState {
  bool get isLoading => throw _privateConstructorUsedError;

  /// Create a copy of CreateNewGroupItemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateNewGroupItemStateCopyWith<CreateNewGroupItemState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateNewGroupItemStateCopyWith<$Res> {
  factory $CreateNewGroupItemStateCopyWith(CreateNewGroupItemState value,
          $Res Function(CreateNewGroupItemState) then) =
      _$CreateNewGroupItemStateCopyWithImpl<$Res, CreateNewGroupItemState>;
  @useResult
  $Res call({bool isLoading});
}

/// @nodoc
class _$CreateNewGroupItemStateCopyWithImpl<$Res,
        $Val extends CreateNewGroupItemState>
    implements $CreateNewGroupItemStateCopyWith<$Res> {
  _$CreateNewGroupItemStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateNewGroupItemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateNewGroupItemStateImplCopyWith<$Res>
    implements $CreateNewGroupItemStateCopyWith<$Res> {
  factory _$$CreateNewGroupItemStateImplCopyWith(
          _$CreateNewGroupItemStateImpl value,
          $Res Function(_$CreateNewGroupItemStateImpl) then) =
      __$$CreateNewGroupItemStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isLoading});
}

/// @nodoc
class __$$CreateNewGroupItemStateImplCopyWithImpl<$Res>
    extends _$CreateNewGroupItemStateCopyWithImpl<$Res,
        _$CreateNewGroupItemStateImpl>
    implements _$$CreateNewGroupItemStateImplCopyWith<$Res> {
  __$$CreateNewGroupItemStateImplCopyWithImpl(
      _$CreateNewGroupItemStateImpl _value,
      $Res Function(_$CreateNewGroupItemStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateNewGroupItemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
  }) {
    return _then(_$CreateNewGroupItemStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CreateNewGroupItemStateImpl extends _CreateNewGroupItemState {
  const _$CreateNewGroupItemStateImpl({this.isLoading = false}) : super._();

  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'CreateNewGroupItemState(isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateNewGroupItemStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading);

  /// Create a copy of CreateNewGroupItemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateNewGroupItemStateImplCopyWith<_$CreateNewGroupItemStateImpl>
      get copyWith => __$$CreateNewGroupItemStateImplCopyWithImpl<
          _$CreateNewGroupItemStateImpl>(this, _$identity);
}

abstract class _CreateNewGroupItemState extends CreateNewGroupItemState {
  const factory _CreateNewGroupItemState({final bool isLoading}) =
      _$CreateNewGroupItemStateImpl;
  const _CreateNewGroupItemState._() : super._();

  @override
  bool get isLoading;

  /// Create a copy of CreateNewGroupItemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateNewGroupItemStateImplCopyWith<_$CreateNewGroupItemStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
