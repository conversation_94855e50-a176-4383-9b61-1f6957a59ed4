// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'foods_filter_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FoodsFilterState {
// @Default(null) FilterModel? filterModel,
  bool get checked => throw _privateConstructorUsedError;
  int get shopCount => throw _privateConstructorUsedError;
  double get endPrice => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isTagLoading => throw _privateConstructorUsedError;
  bool get isShopLoading => throw _privateConstructorUsedError;
  bool get isRestaurantLoading => throw _privateConstructorUsedError;
  RangeValues get rangeValues =>
      throw _privateConstructorUsedError; // @Default([]) List<ShopData> shops,
  List<String> get tags => throw _privateConstructorUsedError;
  List<int> get prices => throw _privateConstructorUsedError;

  /// Create a copy of FoodsFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FoodsFilterStateCopyWith<FoodsFilterState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FoodsFilterStateCopyWith<$Res> {
  factory $FoodsFilterStateCopyWith(
          FoodsFilterState value, $Res Function(FoodsFilterState) then) =
      _$FoodsFilterStateCopyWithImpl<$Res, FoodsFilterState>;
  @useResult
  $Res call(
      {bool checked,
      int shopCount,
      double endPrice,
      bool isLoading,
      bool isTagLoading,
      bool isShopLoading,
      bool isRestaurantLoading,
      RangeValues rangeValues,
      List<String> tags,
      List<int> prices});
}

/// @nodoc
class _$FoodsFilterStateCopyWithImpl<$Res, $Val extends FoodsFilterState>
    implements $FoodsFilterStateCopyWith<$Res> {
  _$FoodsFilterStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FoodsFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? checked = null,
    Object? shopCount = null,
    Object? endPrice = null,
    Object? isLoading = null,
    Object? isTagLoading = null,
    Object? isShopLoading = null,
    Object? isRestaurantLoading = null,
    Object? rangeValues = null,
    Object? tags = null,
    Object? prices = null,
  }) {
    return _then(_value.copyWith(
      checked: null == checked
          ? _value.checked
          : checked // ignore: cast_nullable_to_non_nullable
              as bool,
      shopCount: null == shopCount
          ? _value.shopCount
          : shopCount // ignore: cast_nullable_to_non_nullable
              as int,
      endPrice: null == endPrice
          ? _value.endPrice
          : endPrice // ignore: cast_nullable_to_non_nullable
              as double,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isTagLoading: null == isTagLoading
          ? _value.isTagLoading
          : isTagLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShopLoading: null == isShopLoading
          ? _value.isShopLoading
          : isShopLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRestaurantLoading: null == isRestaurantLoading
          ? _value.isRestaurantLoading
          : isRestaurantLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      rangeValues: null == rangeValues
          ? _value.rangeValues
          : rangeValues // ignore: cast_nullable_to_non_nullable
              as RangeValues,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      prices: null == prices
          ? _value.prices
          : prices // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FoodsFilterStateImplCopyWith<$Res>
    implements $FoodsFilterStateCopyWith<$Res> {
  factory _$$FoodsFilterStateImplCopyWith(_$FoodsFilterStateImpl value,
          $Res Function(_$FoodsFilterStateImpl) then) =
      __$$FoodsFilterStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool checked,
      int shopCount,
      double endPrice,
      bool isLoading,
      bool isTagLoading,
      bool isShopLoading,
      bool isRestaurantLoading,
      RangeValues rangeValues,
      List<String> tags,
      List<int> prices});
}

/// @nodoc
class __$$FoodsFilterStateImplCopyWithImpl<$Res>
    extends _$FoodsFilterStateCopyWithImpl<$Res, _$FoodsFilterStateImpl>
    implements _$$FoodsFilterStateImplCopyWith<$Res> {
  __$$FoodsFilterStateImplCopyWithImpl(_$FoodsFilterStateImpl _value,
      $Res Function(_$FoodsFilterStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FoodsFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? checked = null,
    Object? shopCount = null,
    Object? endPrice = null,
    Object? isLoading = null,
    Object? isTagLoading = null,
    Object? isShopLoading = null,
    Object? isRestaurantLoading = null,
    Object? rangeValues = null,
    Object? tags = null,
    Object? prices = null,
  }) {
    return _then(_$FoodsFilterStateImpl(
      checked: null == checked
          ? _value.checked
          : checked // ignore: cast_nullable_to_non_nullable
              as bool,
      shopCount: null == shopCount
          ? _value.shopCount
          : shopCount // ignore: cast_nullable_to_non_nullable
              as int,
      endPrice: null == endPrice
          ? _value.endPrice
          : endPrice // ignore: cast_nullable_to_non_nullable
              as double,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isTagLoading: null == isTagLoading
          ? _value.isTagLoading
          : isTagLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShopLoading: null == isShopLoading
          ? _value.isShopLoading
          : isShopLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRestaurantLoading: null == isRestaurantLoading
          ? _value.isRestaurantLoading
          : isRestaurantLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      rangeValues: null == rangeValues
          ? _value.rangeValues
          : rangeValues // ignore: cast_nullable_to_non_nullable
              as RangeValues,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      prices: null == prices
          ? _value._prices
          : prices // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ));
  }
}

/// @nodoc

class _$FoodsFilterStateImpl extends _FoodsFilterState {
  const _$FoodsFilterStateImpl(
      {this.checked = false,
      this.shopCount = 0,
      this.endPrice = 100,
      this.isLoading = false,
      this.isTagLoading = false,
      this.isShopLoading = true,
      this.isRestaurantLoading = true,
      this.rangeValues = const RangeValues(1, 100),
      final List<String> tags = const [],
      final List<int> prices = const []})
      : _tags = tags,
        _prices = prices,
        super._();

// @Default(null) FilterModel? filterModel,
  @override
  @JsonKey()
  final bool checked;
  @override
  @JsonKey()
  final int shopCount;
  @override
  @JsonKey()
  final double endPrice;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isTagLoading;
  @override
  @JsonKey()
  final bool isShopLoading;
  @override
  @JsonKey()
  final bool isRestaurantLoading;
  @override
  @JsonKey()
  final RangeValues rangeValues;
// @Default([]) List<ShopData> shops,
  final List<String> _tags;
// @Default([]) List<ShopData> shops,
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  final List<int> _prices;
  @override
  @JsonKey()
  List<int> get prices {
    if (_prices is EqualUnmodifiableListView) return _prices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prices);
  }

  @override
  String toString() {
    return 'FoodsFilterState(checked: $checked, shopCount: $shopCount, endPrice: $endPrice, isLoading: $isLoading, isTagLoading: $isTagLoading, isShopLoading: $isShopLoading, isRestaurantLoading: $isRestaurantLoading, rangeValues: $rangeValues, tags: $tags, prices: $prices)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FoodsFilterStateImpl &&
            (identical(other.checked, checked) || other.checked == checked) &&
            (identical(other.shopCount, shopCount) ||
                other.shopCount == shopCount) &&
            (identical(other.endPrice, endPrice) ||
                other.endPrice == endPrice) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isTagLoading, isTagLoading) ||
                other.isTagLoading == isTagLoading) &&
            (identical(other.isShopLoading, isShopLoading) ||
                other.isShopLoading == isShopLoading) &&
            (identical(other.isRestaurantLoading, isRestaurantLoading) ||
                other.isRestaurantLoading == isRestaurantLoading) &&
            (identical(other.rangeValues, rangeValues) ||
                other.rangeValues == rangeValues) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            const DeepCollectionEquality().equals(other._prices, _prices));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      checked,
      shopCount,
      endPrice,
      isLoading,
      isTagLoading,
      isShopLoading,
      isRestaurantLoading,
      rangeValues,
      const DeepCollectionEquality().hash(_tags),
      const DeepCollectionEquality().hash(_prices));

  /// Create a copy of FoodsFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FoodsFilterStateImplCopyWith<_$FoodsFilterStateImpl> get copyWith =>
      __$$FoodsFilterStateImplCopyWithImpl<_$FoodsFilterStateImpl>(
          this, _$identity);
}

abstract class _FoodsFilterState extends FoodsFilterState {
  const factory _FoodsFilterState(
      {final bool checked,
      final int shopCount,
      final double endPrice,
      final bool isLoading,
      final bool isTagLoading,
      final bool isShopLoading,
      final bool isRestaurantLoading,
      final RangeValues rangeValues,
      final List<String> tags,
      final List<int> prices}) = _$FoodsFilterStateImpl;
  const _FoodsFilterState._() : super._();

// @Default(null) FilterModel? filterModel,
  @override
  bool get checked;
  @override
  int get shopCount;
  @override
  double get endPrice;
  @override
  bool get isLoading;
  @override
  bool get isTagLoading;
  @override
  bool get isShopLoading;
  @override
  bool get isRestaurantLoading;
  @override
  RangeValues get rangeValues; // @Default([]) List<ShopData> shops,
  @override
  List<String> get tags;
  @override
  List<int> get prices;

  /// Create a copy of FoodsFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FoodsFilterStateImplCopyWith<_$FoodsFilterStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
