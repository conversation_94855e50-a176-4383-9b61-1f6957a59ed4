abstract class StorageKeys {
  StorageKeys._();

  /// shared preferences keys
  static const String keyLangSelected = 'keyLangSelected';
  static const String keyUser = 'keyUser';
  static const String keyAddressSelected = 'keyAddressSelected';
  static const String keyToken = 'keyToken';
  static const String keySelectedCurrency = 'keySelectedCurrency';
  static const String keyAppThemeMode = 'keyAppThemeMode';
  static const String keyGlobalSettings = 'keyGlobalSettings';
  static const String keyTranslations = 'keyTranslations';
  static const String keyLanguageData = 'keyLanguageData';
  static const String keyLangLtr = 'keyLangLtr';
  static const String keyWallet = 'keyWallet';
  static const String keyShop = 'keyShop';
  static const String keySystemLanguage = 'keySystemLanguage';
  static const String keyWalletData = 'keyWalletData';
}

