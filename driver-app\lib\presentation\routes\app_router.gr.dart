// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i15;
import 'package:driver/presentation/pages/auth/become_driver/become_driver.dart'
    as _i1;
import 'package:driver/presentation/pages/auth/login/login_page.dart' as _i5;
import 'package:driver/presentation/pages/home/<USER>' as _i3;
import 'package:driver/presentation/pages/income/income_page.dart' as _i4;
import 'package:driver/presentation/pages/initial/no_connection_page.dart'
    as _i6;
import 'package:driver/presentation/pages/initial/splash_page.dart' as _i13;
import 'package:driver/presentation/pages/order_history/order_history.dart'
    as _i8;
import 'package:driver/presentation/pages/orders/orders_page.dart' as _i9;
import 'package:driver/presentation/pages/parcel/parcels_page.dart' as _i11;
import 'package:driver/presentation/pages/parcels_history/parcel_history.dart'
    as _i10;
import 'package:driver/presentation/pages/profile/delivery_zone/delivery_zone_page.dart'
    as _i2;
import 'package:driver/presentation/pages/profile/notification_list_page.dart'
    as _i7;
import 'package:driver/presentation/pages/profile/profile_page.dart' as _i12;
import 'package:driver/presentation/pages/stores/story_page.dart' as _i14;

/// generated route for
/// [_i1.BecomeDriverPage]
class BecomeDriverRoute extends _i15.PageRouteInfo<void> {
  const BecomeDriverRoute({List<_i15.PageRouteInfo>? children})
      : super(
          BecomeDriverRoute.name,
          initialChildren: children,
        );

  static const String name = 'BecomeDriverRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i1.BecomeDriverPage();
    },
  );
}

/// generated route for
/// [_i2.DeliveryZonePage]
class DeliveryZoneRoute extends _i15.PageRouteInfo<void> {
  const DeliveryZoneRoute({List<_i15.PageRouteInfo>? children})
      : super(
          DeliveryZoneRoute.name,
          initialChildren: children,
        );

  static const String name = 'DeliveryZoneRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i2.DeliveryZonePage();
    },
  );
}

/// generated route for
/// [_i3.HomePage]
class HomeRoute extends _i15.PageRouteInfo<void> {
  const HomeRoute({List<_i15.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i3.HomePage();
    },
  );
}

/// generated route for
/// [_i4.IncomePage]
class IncomeRoute extends _i15.PageRouteInfo<void> {
  const IncomeRoute({List<_i15.PageRouteInfo>? children})
      : super(
          IncomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'IncomeRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i4.IncomePage();
    },
  );
}

/// generated route for
/// [_i5.LoginPage]
class LoginRoute extends _i15.PageRouteInfo<void> {
  const LoginRoute({List<_i15.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i5.LoginPage();
    },
  );
}

/// generated route for
/// [_i6.NoConnectionPage]
class NoConnectionRoute extends _i15.PageRouteInfo<void> {
  const NoConnectionRoute({List<_i15.PageRouteInfo>? children})
      : super(
          NoConnectionRoute.name,
          initialChildren: children,
        );

  static const String name = 'NoConnectionRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i6.NoConnectionPage();
    },
  );
}

/// generated route for
/// [_i7.NotificationListPage]
class NotificationListRoute extends _i15.PageRouteInfo<void> {
  const NotificationListRoute({List<_i15.PageRouteInfo>? children})
      : super(
          NotificationListRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationListRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i7.NotificationListPage();
    },
  );
}

/// generated route for
/// [_i8.OrderHistoryPage]
class OrderHistoryRoute extends _i15.PageRouteInfo<void> {
  const OrderHistoryRoute({List<_i15.PageRouteInfo>? children})
      : super(
          OrderHistoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'OrderHistoryRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i8.OrderHistoryPage();
    },
  );
}

/// generated route for
/// [_i9.OrdersPage]
class OrdersRoute extends _i15.PageRouteInfo<void> {
  const OrdersRoute({List<_i15.PageRouteInfo>? children})
      : super(
          OrdersRoute.name,
          initialChildren: children,
        );

  static const String name = 'OrdersRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i9.OrdersPage();
    },
  );
}

/// generated route for
/// [_i10.ParcelHistoryPage]
class ParcelHistoryRoute extends _i15.PageRouteInfo<void> {
  const ParcelHistoryRoute({List<_i15.PageRouteInfo>? children})
      : super(
          ParcelHistoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'ParcelHistoryRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i10.ParcelHistoryPage();
    },
  );
}

/// generated route for
/// [_i11.ParcelsPage]
class ParcelsRoute extends _i15.PageRouteInfo<void> {
  const ParcelsRoute({List<_i15.PageRouteInfo>? children})
      : super(
          ParcelsRoute.name,
          initialChildren: children,
        );

  static const String name = 'ParcelsRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i11.ParcelsPage();
    },
  );
}

/// generated route for
/// [_i12.ProfilePage]
class ProfileRoute extends _i15.PageRouteInfo<void> {
  const ProfileRoute({List<_i15.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i12.ProfilePage();
    },
  );
}

/// generated route for
/// [_i13.SplashPage]
class SplashRoute extends _i15.PageRouteInfo<void> {
  const SplashRoute({List<_i15.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i13.SplashPage();
    },
  );
}

/// generated route for
/// [_i14.StoryPage]
class StoryRoute extends _i15.PageRouteInfo<void> {
  const StoryRoute({List<_i15.PageRouteInfo>? children})
      : super(
          StoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'StoryRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i14.StoryPage();
    },
  );
}
