// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_confirmation_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RegisterConfirmationState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isSuccess => throw _privateConstructorUsedError;
  bool get isResetPasswordSuccess => throw _privateConstructorUsedError;
  bool get isResending => throw _privateConstructorUsedError;
  bool get isTimeExpired => throw _privateConstructorUsedError;
  bool get isCodeError => throw _privateConstructorUsedError;
  bool get isConfirm => throw _privateConstructorUsedError;
  String get confirmCode => throw _privateConstructorUsedError;
  String get verificationCode => throw _privateConstructorUsedError;
  String get timerText => throw _privateConstructorUsedError;

  /// Create a copy of RegisterConfirmationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RegisterConfirmationStateCopyWith<RegisterConfirmationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterConfirmationStateCopyWith<$Res> {
  factory $RegisterConfirmationStateCopyWith(RegisterConfirmationState value,
          $Res Function(RegisterConfirmationState) then) =
      _$RegisterConfirmationStateCopyWithImpl<$Res, RegisterConfirmationState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      bool isResetPasswordSuccess,
      bool isResending,
      bool isTimeExpired,
      bool isCodeError,
      bool isConfirm,
      String confirmCode,
      String verificationCode,
      String timerText});
}

/// @nodoc
class _$RegisterConfirmationStateCopyWithImpl<$Res,
        $Val extends RegisterConfirmationState>
    implements $RegisterConfirmationStateCopyWith<$Res> {
  _$RegisterConfirmationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegisterConfirmationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? isResetPasswordSuccess = null,
    Object? isResending = null,
    Object? isTimeExpired = null,
    Object? isCodeError = null,
    Object? isConfirm = null,
    Object? confirmCode = null,
    Object? verificationCode = null,
    Object? timerText = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isResetPasswordSuccess: null == isResetPasswordSuccess
          ? _value.isResetPasswordSuccess
          : isResetPasswordSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isResending: null == isResending
          ? _value.isResending
          : isResending // ignore: cast_nullable_to_non_nullable
              as bool,
      isTimeExpired: null == isTimeExpired
          ? _value.isTimeExpired
          : isTimeExpired // ignore: cast_nullable_to_non_nullable
              as bool,
      isCodeError: null == isCodeError
          ? _value.isCodeError
          : isCodeError // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirm: null == isConfirm
          ? _value.isConfirm
          : isConfirm // ignore: cast_nullable_to_non_nullable
              as bool,
      confirmCode: null == confirmCode
          ? _value.confirmCode
          : confirmCode // ignore: cast_nullable_to_non_nullable
              as String,
      verificationCode: null == verificationCode
          ? _value.verificationCode
          : verificationCode // ignore: cast_nullable_to_non_nullable
              as String,
      timerText: null == timerText
          ? _value.timerText
          : timerText // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegisterConfirmationStateImplCopyWith<$Res>
    implements $RegisterConfirmationStateCopyWith<$Res> {
  factory _$$RegisterConfirmationStateImplCopyWith(
          _$RegisterConfirmationStateImpl value,
          $Res Function(_$RegisterConfirmationStateImpl) then) =
      __$$RegisterConfirmationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      bool isResetPasswordSuccess,
      bool isResending,
      bool isTimeExpired,
      bool isCodeError,
      bool isConfirm,
      String confirmCode,
      String verificationCode,
      String timerText});
}

/// @nodoc
class __$$RegisterConfirmationStateImplCopyWithImpl<$Res>
    extends _$RegisterConfirmationStateCopyWithImpl<$Res,
        _$RegisterConfirmationStateImpl>
    implements _$$RegisterConfirmationStateImplCopyWith<$Res> {
  __$$RegisterConfirmationStateImplCopyWithImpl(
      _$RegisterConfirmationStateImpl _value,
      $Res Function(_$RegisterConfirmationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegisterConfirmationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? isResetPasswordSuccess = null,
    Object? isResending = null,
    Object? isTimeExpired = null,
    Object? isCodeError = null,
    Object? isConfirm = null,
    Object? confirmCode = null,
    Object? verificationCode = null,
    Object? timerText = null,
  }) {
    return _then(_$RegisterConfirmationStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isResetPasswordSuccess: null == isResetPasswordSuccess
          ? _value.isResetPasswordSuccess
          : isResetPasswordSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isResending: null == isResending
          ? _value.isResending
          : isResending // ignore: cast_nullable_to_non_nullable
              as bool,
      isTimeExpired: null == isTimeExpired
          ? _value.isTimeExpired
          : isTimeExpired // ignore: cast_nullable_to_non_nullable
              as bool,
      isCodeError: null == isCodeError
          ? _value.isCodeError
          : isCodeError // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirm: null == isConfirm
          ? _value.isConfirm
          : isConfirm // ignore: cast_nullable_to_non_nullable
              as bool,
      confirmCode: null == confirmCode
          ? _value.confirmCode
          : confirmCode // ignore: cast_nullable_to_non_nullable
              as String,
      verificationCode: null == verificationCode
          ? _value.verificationCode
          : verificationCode // ignore: cast_nullable_to_non_nullable
              as String,
      timerText: null == timerText
          ? _value.timerText
          : timerText // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RegisterConfirmationStateImpl extends _RegisterConfirmationState {
  const _$RegisterConfirmationStateImpl(
      {this.isLoading = false,
      this.isSuccess = false,
      this.isResetPasswordSuccess = false,
      this.isResending = false,
      this.isTimeExpired = false,
      this.isCodeError = false,
      this.isConfirm = false,
      this.confirmCode = '',
      this.verificationCode = '',
      this.timerText = '05:00'})
      : super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSuccess;
  @override
  @JsonKey()
  final bool isResetPasswordSuccess;
  @override
  @JsonKey()
  final bool isResending;
  @override
  @JsonKey()
  final bool isTimeExpired;
  @override
  @JsonKey()
  final bool isCodeError;
  @override
  @JsonKey()
  final bool isConfirm;
  @override
  @JsonKey()
  final String confirmCode;
  @override
  @JsonKey()
  final String verificationCode;
  @override
  @JsonKey()
  final String timerText;

  @override
  String toString() {
    return 'RegisterConfirmationState(isLoading: $isLoading, isSuccess: $isSuccess, isResetPasswordSuccess: $isResetPasswordSuccess, isResending: $isResending, isTimeExpired: $isTimeExpired, isCodeError: $isCodeError, isConfirm: $isConfirm, confirmCode: $confirmCode, verificationCode: $verificationCode, timerText: $timerText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterConfirmationStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSuccess, isSuccess) ||
                other.isSuccess == isSuccess) &&
            (identical(other.isResetPasswordSuccess, isResetPasswordSuccess) ||
                other.isResetPasswordSuccess == isResetPasswordSuccess) &&
            (identical(other.isResending, isResending) ||
                other.isResending == isResending) &&
            (identical(other.isTimeExpired, isTimeExpired) ||
                other.isTimeExpired == isTimeExpired) &&
            (identical(other.isCodeError, isCodeError) ||
                other.isCodeError == isCodeError) &&
            (identical(other.isConfirm, isConfirm) ||
                other.isConfirm == isConfirm) &&
            (identical(other.confirmCode, confirmCode) ||
                other.confirmCode == confirmCode) &&
            (identical(other.verificationCode, verificationCode) ||
                other.verificationCode == verificationCode) &&
            (identical(other.timerText, timerText) ||
                other.timerText == timerText));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isSuccess,
      isResetPasswordSuccess,
      isResending,
      isTimeExpired,
      isCodeError,
      isConfirm,
      confirmCode,
      verificationCode,
      timerText);

  /// Create a copy of RegisterConfirmationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterConfirmationStateImplCopyWith<_$RegisterConfirmationStateImpl>
      get copyWith => __$$RegisterConfirmationStateImplCopyWithImpl<
          _$RegisterConfirmationStateImpl>(this, _$identity);
}

abstract class _RegisterConfirmationState extends RegisterConfirmationState {
  const factory _RegisterConfirmationState(
      {final bool isLoading,
      final bool isSuccess,
      final bool isResetPasswordSuccess,
      final bool isResending,
      final bool isTimeExpired,
      final bool isCodeError,
      final bool isConfirm,
      final String confirmCode,
      final String verificationCode,
      final String timerText}) = _$RegisterConfirmationStateImpl;
  const _RegisterConfirmationState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get isSuccess;
  @override
  bool get isResetPasswordSuccess;
  @override
  bool get isResending;
  @override
  bool get isTimeExpired;
  @override
  bool get isCodeError;
  @override
  bool get isConfirm;
  @override
  String get confirmCode;
  @override
  String get verificationCode;
  @override
  String get timerText;

  /// Create a copy of RegisterConfirmationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterConfirmationStateImplCopyWith<_$RegisterConfirmationStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
