// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HomeState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isGoUser => throw _privateConstructorUsedError;
  bool get isGoRestaurant => throw _privateConstructorUsedError;
  bool get isScrolling => throw _privateConstructorUsedError;
  List<LatLng> get polylineCoordinates => throw _privateConstructorUsedError;
  List<LatLng> get endPolylineCoordinates => throw _privateConstructorUsedError;
  Set<Marker> get markers => throw _privateConstructorUsedError;
  OrderDetailData? get orderDetail => throw _privateConstructorUsedError;
  ParcelOrder? get parcelDetail => throw _privateConstructorUsedError;
  Set<Polygon> get polygon => throw _privateConstructorUsedError;
  List<LatLng> get deliveryZone => throw _privateConstructorUsedError;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeStateCopyWith<HomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeStateCopyWith<$Res> {
  factory $HomeStateCopyWith(HomeState value, $Res Function(HomeState) then) =
      _$HomeStateCopyWithImpl<$Res, HomeState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isGoUser,
      bool isGoRestaurant,
      bool isScrolling,
      List<LatLng> polylineCoordinates,
      List<LatLng> endPolylineCoordinates,
      Set<Marker> markers,
      OrderDetailData? orderDetail,
      ParcelOrder? parcelDetail,
      Set<Polygon> polygon,
      List<LatLng> deliveryZone});
}

/// @nodoc
class _$HomeStateCopyWithImpl<$Res, $Val extends HomeState>
    implements $HomeStateCopyWith<$Res> {
  _$HomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isGoUser = null,
    Object? isGoRestaurant = null,
    Object? isScrolling = null,
    Object? polylineCoordinates = null,
    Object? endPolylineCoordinates = null,
    Object? markers = null,
    Object? orderDetail = freezed,
    Object? parcelDetail = freezed,
    Object? polygon = null,
    Object? deliveryZone = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isGoUser: null == isGoUser
          ? _value.isGoUser
          : isGoUser // ignore: cast_nullable_to_non_nullable
              as bool,
      isGoRestaurant: null == isGoRestaurant
          ? _value.isGoRestaurant
          : isGoRestaurant // ignore: cast_nullable_to_non_nullable
              as bool,
      isScrolling: null == isScrolling
          ? _value.isScrolling
          : isScrolling // ignore: cast_nullable_to_non_nullable
              as bool,
      polylineCoordinates: null == polylineCoordinates
          ? _value.polylineCoordinates
          : polylineCoordinates // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
      endPolylineCoordinates: null == endPolylineCoordinates
          ? _value.endPolylineCoordinates
          : endPolylineCoordinates // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
      markers: null == markers
          ? _value.markers
          : markers // ignore: cast_nullable_to_non_nullable
              as Set<Marker>,
      orderDetail: freezed == orderDetail
          ? _value.orderDetail
          : orderDetail // ignore: cast_nullable_to_non_nullable
              as OrderDetailData?,
      parcelDetail: freezed == parcelDetail
          ? _value.parcelDetail
          : parcelDetail // ignore: cast_nullable_to_non_nullable
              as ParcelOrder?,
      polygon: null == polygon
          ? _value.polygon
          : polygon // ignore: cast_nullable_to_non_nullable
              as Set<Polygon>,
      deliveryZone: null == deliveryZone
          ? _value.deliveryZone
          : deliveryZone // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HomeStateImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeStateImplCopyWith(
          _$HomeStateImpl value, $Res Function(_$HomeStateImpl) then) =
      __$$HomeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isGoUser,
      bool isGoRestaurant,
      bool isScrolling,
      List<LatLng> polylineCoordinates,
      List<LatLng> endPolylineCoordinates,
      Set<Marker> markers,
      OrderDetailData? orderDetail,
      ParcelOrder? parcelDetail,
      Set<Polygon> polygon,
      List<LatLng> deliveryZone});
}

/// @nodoc
class __$$HomeStateImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeStateImpl>
    implements _$$HomeStateImplCopyWith<$Res> {
  __$$HomeStateImplCopyWithImpl(
      _$HomeStateImpl _value, $Res Function(_$HomeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isGoUser = null,
    Object? isGoRestaurant = null,
    Object? isScrolling = null,
    Object? polylineCoordinates = null,
    Object? endPolylineCoordinates = null,
    Object? markers = null,
    Object? orderDetail = freezed,
    Object? parcelDetail = freezed,
    Object? polygon = null,
    Object? deliveryZone = null,
  }) {
    return _then(_$HomeStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isGoUser: null == isGoUser
          ? _value.isGoUser
          : isGoUser // ignore: cast_nullable_to_non_nullable
              as bool,
      isGoRestaurant: null == isGoRestaurant
          ? _value.isGoRestaurant
          : isGoRestaurant // ignore: cast_nullable_to_non_nullable
              as bool,
      isScrolling: null == isScrolling
          ? _value.isScrolling
          : isScrolling // ignore: cast_nullable_to_non_nullable
              as bool,
      polylineCoordinates: null == polylineCoordinates
          ? _value._polylineCoordinates
          : polylineCoordinates // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
      endPolylineCoordinates: null == endPolylineCoordinates
          ? _value._endPolylineCoordinates
          : endPolylineCoordinates // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
      markers: null == markers
          ? _value._markers
          : markers // ignore: cast_nullable_to_non_nullable
              as Set<Marker>,
      orderDetail: freezed == orderDetail
          ? _value.orderDetail
          : orderDetail // ignore: cast_nullable_to_non_nullable
              as OrderDetailData?,
      parcelDetail: freezed == parcelDetail
          ? _value.parcelDetail
          : parcelDetail // ignore: cast_nullable_to_non_nullable
              as ParcelOrder?,
      polygon: null == polygon
          ? _value._polygon
          : polygon // ignore: cast_nullable_to_non_nullable
              as Set<Polygon>,
      deliveryZone: null == deliveryZone
          ? _value._deliveryZone
          : deliveryZone // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
    ));
  }
}

/// @nodoc

class _$HomeStateImpl extends _HomeState {
  const _$HomeStateImpl(
      {this.isLoading = false,
      this.isGoUser = false,
      this.isGoRestaurant = false,
      this.isScrolling = false,
      final List<LatLng> polylineCoordinates = const [],
      final List<LatLng> endPolylineCoordinates = const [],
      final Set<Marker> markers = const {},
      this.orderDetail = null,
      this.parcelDetail = null,
      final Set<Polygon> polygon = const {},
      final List<LatLng> deliveryZone = const []})
      : _polylineCoordinates = polylineCoordinates,
        _endPolylineCoordinates = endPolylineCoordinates,
        _markers = markers,
        _polygon = polygon,
        _deliveryZone = deliveryZone,
        super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isGoUser;
  @override
  @JsonKey()
  final bool isGoRestaurant;
  @override
  @JsonKey()
  final bool isScrolling;
  final List<LatLng> _polylineCoordinates;
  @override
  @JsonKey()
  List<LatLng> get polylineCoordinates {
    if (_polylineCoordinates is EqualUnmodifiableListView)
      return _polylineCoordinates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_polylineCoordinates);
  }

  final List<LatLng> _endPolylineCoordinates;
  @override
  @JsonKey()
  List<LatLng> get endPolylineCoordinates {
    if (_endPolylineCoordinates is EqualUnmodifiableListView)
      return _endPolylineCoordinates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_endPolylineCoordinates);
  }

  final Set<Marker> _markers;
  @override
  @JsonKey()
  Set<Marker> get markers {
    if (_markers is EqualUnmodifiableSetView) return _markers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_markers);
  }

  @override
  @JsonKey()
  final OrderDetailData? orderDetail;
  @override
  @JsonKey()
  final ParcelOrder? parcelDetail;
  final Set<Polygon> _polygon;
  @override
  @JsonKey()
  Set<Polygon> get polygon {
    if (_polygon is EqualUnmodifiableSetView) return _polygon;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_polygon);
  }

  final List<LatLng> _deliveryZone;
  @override
  @JsonKey()
  List<LatLng> get deliveryZone {
    if (_deliveryZone is EqualUnmodifiableListView) return _deliveryZone;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_deliveryZone);
  }

  @override
  String toString() {
    return 'HomeState(isLoading: $isLoading, isGoUser: $isGoUser, isGoRestaurant: $isGoRestaurant, isScrolling: $isScrolling, polylineCoordinates: $polylineCoordinates, endPolylineCoordinates: $endPolylineCoordinates, markers: $markers, orderDetail: $orderDetail, parcelDetail: $parcelDetail, polygon: $polygon, deliveryZone: $deliveryZone)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isGoUser, isGoUser) ||
                other.isGoUser == isGoUser) &&
            (identical(other.isGoRestaurant, isGoRestaurant) ||
                other.isGoRestaurant == isGoRestaurant) &&
            (identical(other.isScrolling, isScrolling) ||
                other.isScrolling == isScrolling) &&
            const DeepCollectionEquality()
                .equals(other._polylineCoordinates, _polylineCoordinates) &&
            const DeepCollectionEquality().equals(
                other._endPolylineCoordinates, _endPolylineCoordinates) &&
            const DeepCollectionEquality().equals(other._markers, _markers) &&
            (identical(other.orderDetail, orderDetail) ||
                other.orderDetail == orderDetail) &&
            (identical(other.parcelDetail, parcelDetail) ||
                other.parcelDetail == parcelDetail) &&
            const DeepCollectionEquality().equals(other._polygon, _polygon) &&
            const DeepCollectionEquality()
                .equals(other._deliveryZone, _deliveryZone));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isGoUser,
      isGoRestaurant,
      isScrolling,
      const DeepCollectionEquality().hash(_polylineCoordinates),
      const DeepCollectionEquality().hash(_endPolylineCoordinates),
      const DeepCollectionEquality().hash(_markers),
      orderDetail,
      parcelDetail,
      const DeepCollectionEquality().hash(_polygon),
      const DeepCollectionEquality().hash(_deliveryZone));

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      __$$HomeStateImplCopyWithImpl<_$HomeStateImpl>(this, _$identity);
}

abstract class _HomeState extends HomeState {
  const factory _HomeState(
      {final bool isLoading,
      final bool isGoUser,
      final bool isGoRestaurant,
      final bool isScrolling,
      final List<LatLng> polylineCoordinates,
      final List<LatLng> endPolylineCoordinates,
      final Set<Marker> markers,
      final OrderDetailData? orderDetail,
      final ParcelOrder? parcelDetail,
      final Set<Polygon> polygon,
      final List<LatLng> deliveryZone}) = _$HomeStateImpl;
  const _HomeState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get isGoUser;
  @override
  bool get isGoRestaurant;
  @override
  bool get isScrolling;
  @override
  List<LatLng> get polylineCoordinates;
  @override
  List<LatLng> get endPolylineCoordinates;
  @override
  Set<Marker> get markers;
  @override
  OrderDetailData? get orderDetail;
  @override
  ParcelOrder? get parcelDetail;
  @override
  Set<Polygon> get polygon;
  @override
  List<LatLng> get deliveryZone;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
