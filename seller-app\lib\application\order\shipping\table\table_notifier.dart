import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:venderfoodyman/domain/di/dependency_manager.dart';

import 'table_state.dart';
import 'package:venderfoodyman/infrastructure/models/models.dart';

class TableNotifier extends StateNotifier<TableState> {
  String _query = '';
  bool _hasMore = true;
  int _page = 0;
  Timer? _timer;

  TableNotifier() : super(TableState(textController: TextEditingController()));

  void clearSelectTableInfo() {
    state = state.copyWith(selectTable: null, selectedIndex: 0);
    state.textController?.text = '';
  }

  void setSelectTable(int index) {
    final selectTable = state.tables[index];
    state = state.copyWith(selectedIndex: index, selectTable: selectTable);
    state.textController?.text = selectTable.name ?? '';
  }

  Future<void> _search({
    RefreshController? refreshController,
    required int? sectionId,
  }) async {
    refreshController?.resetNoData();
    _page = 0;
    _hasMore = true;
    state = state.copyWith(isLoading: true);
    final response = await tableRepository.getTables(
      query: _query.isEmpty ? null : _query.trim(),
      page: ++_page,
      shopSectionId: sectionId,
    );
    response.when(
      success: (data) {
        final List<TableData> tables = data.data ?? [];
        state = state.copyWith(tables: tables, isLoading: false);
        _hasMore = tables.length >= 14;
      },
      failure: (fail, status) {
        debugPrint('===> search table fail $fail');
        state = state.copyWith(isLoading: false);
      },
    );
  }

  void setQuery({
    RefreshController? refreshController,
    required String text,
    required int? sectionId,
  }) {
    if (text.trim() == _query) {
      return;
    }
    _query = text.trim();
    _timer?.cancel();
    _timer = Timer(
      const Duration(milliseconds: 300),
      () {
        _search(refreshController: refreshController,sectionId: sectionId);
      },
    );
  }

  Future<void> fetchMoreTables({
    RefreshController? refreshController,
    required int? sectionId,
  }) async {
    if (!_hasMore) {
      refreshController?.loadNoData();
      return;
    }
    final response = await tableRepository.getTables(
      query: _query.isEmpty ? null : _query.trim(),
      page: ++_page,
      shopSectionId: sectionId,
    );
    response.when(
      success: (data) {
        List<TableData> tables = List.from(state.tables);
        tables.addAll(data.data ?? []);
        _hasMore = (data.data?.length ?? 0) >= 14;
        state = state.copyWith(tables: tables);
        refreshController?.loadComplete();
      },
      failure: (fail, status) {
        refreshController?.loadFailed();
        debugPrint('===> fetch more tables fail $fail');
      },
    );
  }

  Future<void> refreshTables({
    RefreshController? refreshController,
    required int? sectionId,
  }) async {
    debugPrint('===> refresh tables function called');
    _page = 0;
    final response = await tableRepository.getTables(
      query: _query.isEmpty ? null : _query.trim(),
      page: ++_page,
      shopSectionId: sectionId,
    );
    response.when(
      success: (data) {
        final List<TableData> tables = data.data ?? [];
        state = state.copyWith(tables: tables);
        _hasMore = tables.length >= 14;
        refreshController?.refreshCompleted();
        refreshController?.resetNoData();
      },
      failure: (fail, status) {
        debugPrint('===> refresh tables fail $fail');
        refreshController?.refreshFailed();
      },
    );
  }

  Future<void> initialFetchTables({
    RefreshController? refreshController,
    int? sectionId,
  }) async {
    _query = '';
    if (state.tables.isNotEmpty) {
      if (state.selectTable == null) {
        final selectTable = state.tables[0];
        state = state.copyWith(selectedIndex: 0, selectTable: selectTable);
        state.textController?.text = selectTable.name ?? "";
      }
    }
    _hasMore = true;
    _page = 0;
    state = state.copyWith(isLoading: true);
    final response = await tableRepository.getTables(
      query: _query.isEmpty ? null : _query.trim(),
      shopSectionId: sectionId,
      page: ++_page,
    );
    response.when(
      success: (data) {
        final List<TableData> tables = data.data ?? [];
        TableData? selectTable;
        if (tables.isNotEmpty && state.selectTable == null) {
          selectTable = tables[0];
        } else {
          selectTable = state.selectTable;
        }
        state = state.copyWith(
          tables: tables,
          selectTable: selectTable,
          selectedIndex: 0,
          isLoading: false,
        );
        if (selectTable != null) {
          state.textController?.text = selectTable.name ?? '';
        }
        _hasMore = tables.length >= 14;
      },
      failure: (error, status) {
        debugPrint('====> fetch tables fail $error');
        state = state.copyWith(isLoading: false);
      },
    );
  }
}
