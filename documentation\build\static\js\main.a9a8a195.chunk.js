(this.webpackJsonpgihubit=this.webpackJsonpgihubit||[]).push([[0],{59:function(e,t,s){},65:function(e,t,s){"use strict";s.r(t);var a=s(2),c=s(20),n=s(1);function i(){const[e,t]=Object(a.useState)(!1);return Object(a.useEffect)((()=>{const e=()=>{window.pageYOffset>500?t(!0):t(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)}),[]),Object(n.jsx)(n.Fragment,{children:e&&Object(n.jsx)("div",{children:Object(n.jsx)("button",{className:"scroll-top ",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},children:Object(n.jsx)("img",{src:"../assets/img/chevron-w.png",alt:"icon"})})})})}var o=s(31),r=s.n(o),l=(s(48),s(49),s(50),s(22)),d=s(21),j=s(34);const b=Object(j.a)({apiKey:"AIzaSyCuDSljHjF4pKT9yJWtnJgsCXf6rxi40Tg",authDomain:"goshops-7c405.firebaseapp.com",projectId:"goshops-7c405",storageBucket:"goshops-7c405.appspot.com",messagingSenderId:"732738074097",appId:"1:732738074097:web:997669885a98e0396fc050",measurementId:"G-280HSPYKMM"}),p=Object(d.c)(b),m=Object(a.createContext)();function h(e){let{children:t}=e;const s=localStorage.getItem("theme"),[c,i]=Object(a.useState)(""===s);return Object(n.jsx)(m.Provider,{value:{googleSignIn:function(){const e=new d.b;return Object(d.d)(p,e)},facebookSignIn:function(){const e=new d.a;return Object(d.d)(p,e)},setDarkTheme:i,darkTheme:c},children:t})}s(53);var u=s(42),x=s(5);const O=[{iconName:"socicon-facebook",link:"https://www.facebook.com/githubit",className:"facebook"},{iconName:"socicon-linkedin",link:"https://linkedin.com/company/githubit",className:"linkedin-icon"}];var g=()=>Object(n.jsx)(n.Fragment,{children:O.map(((e,t)=>Object(n.jsx)("a",{className:"ptf-social-icon ptf-social-icon--style-3 ".concat(e.className),target:"_blank",rel:"noopener noreferrer",href:e.link,children:Object(n.jsx)("i",{className:e.iconName})},t)))});var f=()=>Object(n.jsxs)("div",{className:"row align-items-center justify-content-center",children:[Object(n.jsx)("div",{className:"col-12 col-md text-md-center text-lg-center",children:Object(n.jsxs)("p",{className:"ptf-footer-copyright has-black-color",children:["\xa9",(new Date).getFullYear()," ",Object(n.jsx)("span",{children:"Githubit"}),". All Rights Reserved."]})}),Object(n.jsx)("div",{className:"col-12 col-lg text-md-center text-lg-center mt-4",children:Object(n.jsx)("div",{className:"ptf-footer-socials has-black-color ",children:Object(n.jsx)(g,{})})})]}),y=(s(27),s(13)),v=s(15),N=s(7),w=s(26);const k=Object(w.b)({name:"auth",initialState:{user:null},reducers:{setUserData(e,t){const{payload:s}=t;e.user=s},clearUser(e){e.user=null}}}),{setUserData:A,clearUser:P}=k.actions;var S=k.reducer;const F=[{iconName:"socicon-linkedin",link:"https://www.linkedin.com/company/githubit",iconClass:"linkedin-icon"},{iconName:"socicon-facebook",link:"https://facebook.com/githubit",iconClass:"facebook"}];var T=e=>{let{closeMenu:t=(()=>{})}=e;const s=Object(v.c)(),a=Object(N.p)(),{user:c}=Object(v.d)((e=>e.auth),v.b);return Object(n.jsxs)("div",{className:"ptf-offcanvas-menu__socials align-items-center",children:[F.map(((e,t)=>Object(n.jsx)("a",{className:"ptf-social-icon ptf-social-icon--style-4 ".concat(e.iconClass),href:e.link,target:"_blank",rel:"noopener noreferrer",children:Object(n.jsx)("i",{className:e.iconName})},t))),c&&Object(n.jsx)("div",{className:"ptf-offcanvas-menu__header d-flex justify-content-end",style:{flexGrow:1},children:Object(n.jsx)("div",{className:"ptf-language-switcher",children:Object(n.jsx)("div",{className:"ptf-submit-button",children:Object(n.jsx)("a",{href:"/logout",onClick:e=>{e.preventDefault(),localStorage.removeItem("token"),s(P()),t(),a("/")},children:"Logout"})})})})]})};const I=[{name:"About us",routerPath:"/about-us"},{name:"Career",routerPath:"/career"}],C=[{name:"About us",routerPath:"/about-us"},{name:"Career",routerPath:"/career"}];var _=e=>{let{closeMenu:t}=e;const{user:s}=Object(v.d)((e=>e.auth),v.b);return Object(n.jsxs)(n.Fragment,{children:[Object(n.jsx)("div",{className:"ptf-offcanvas-menu__navigation",children:Object(n.jsx)(y.c,{children:Object(n.jsx)(y.d,{children:Object(n.jsx)(y.a,{className:"sidebar-menu_wrapper",children:(s?I:C).map(((e,t)=>{var s,a;return Object(n.jsx)("div",{children:(null===(s=e.dropDownItems)||void 0===s?void 0:s.length)>0?Object(n.jsx)(y.e,{title:e.name,children:null===(a=e.dropDownItems)||void 0===a?void 0:a.map(((e,t)=>Object(n.jsx)(y.b,{children:Object(n.jsx)(x.b,{to:e.routerPath,children:e.name})},t)))},t):Object(n.jsx)(y.b,{className:"sidebar-menu",children:Object(n.jsx)(x.b,{to:e.routerPath,children:e.name})})},t)}))})})})}),Object(n.jsxs)("div",{className:"ptf-offcanvas-menu__footer",children:[Object(n.jsxs)("p",{className:"ptf-offcanvas-menu__copyright",children:["@",(new Date).getFullYear()," ",Object(n.jsx)("span",{children:"Githubit"}),". All Rights Reserved."]}),Object(n.jsx)(T,{closeMenu:t})]})]})},R=s(38),z=s(37);var M=()=>{const{darkTheme:e,setDarkTheme:t}=Object(a.useContext)(m);return Object(a.useEffect)((()=>{e?(document.documentElement.setAttribute("data-theme","dark"),localStorage.setItem("theme","dark")):(document.documentElement.removeAttribute("data-theme"),localStorage.setItem("theme","light"))}),[e]),Object(n.jsx)("div",{children:void 0!==e&&Object(n.jsx)("button",{className:"dark-theme",onClick:()=>t(!e),children:e?Object(n.jsx)(R.a,{size:30,className:"text-white"}):Object(n.jsx)(z.a,{size:25,className:"text-white"})})})},E=s(39),Y=s(9);var D=()=>{const e=Object(v.c)(),[t,s]=Object(a.useState)(!1),{user:c}=Object(v.d)((e=>e.auth),v.b),i=()=>{s(!t),r(!1)},[o,r]=Object(a.useState)(!1);return Object(n.jsxs)(n.Fragment,{children:[Object(n.jsx)("header",{className:"ptf-header--style-5 ptf-header--opaque",children:Object(n.jsx)("div",{className:"ptf-navbar ptf-navbar--main ptf-navbar--sticky",children:Object(n.jsx)("div",{className:"container-xxl",children:Object(n.jsxs)("div",{className:"ptf-navbar-inner",children:[Object(n.jsx)(x.b,{className:"ptf-navbar-logo",to:"/",children:Object(n.jsx)("h2",{className:"text-Logo",children:"Githubit"})}),Object(n.jsxs)("span",{className:"d-flex justify-content-center align-items-center",children:[Object(n.jsx)("span",{children:Object(n.jsx)(M,{})}),Object(n.jsx)("div",{className:"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle bar right",onClick:i,children:Object(n.jsx)("i",{className:"lnir lnir-menu-alt-5"})})]})]})})})}),Object(n.jsxs)("div",{className:t?"ptf-offcanvas-menu is-open":"ptf-offcanvas-menu ",children:[Object(n.jsxs)("div",{className:"ptf-offcanvas-menu__header",children:[c?Object(n.jsxs)("div",{className:"user-container",children:[Object(n.jsx)("img",{src:"https://mdbcdn.b-cdn.net/img/Photos/Avatars/img (32).webp",className:"rounded-circle",height:"50",width:"50",alt:"Avatar",loading:"lazy",onClick:()=>r(!o)}),Object(n.jsxs)("div",{className:"user-dropdown-menu",style:{display:o?"block":"none"},children:[Object(n.jsxs)(x.b,{to:"/user-profile",className:"dropdown-item",children:[Object(n.jsx)(E.a,{className:"dropdown-icon"})," ",Object(n.jsx)("span",{children:"My profile"})]}),Object(n.jsxs)(x.b,{to:"/",className:"dropdown-item",onClick:()=>e(P()),children:[Object(n.jsx)(Y.b,{className:"dropdown-icon"}),Object(n.jsx)("span",{children:"Logout"})]})]})]}):null,Object(n.jsx)("span",{className:"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle",onClick:i,children:Object(n.jsx)("i",{className:"lnir lnir-close"})})]}),Object(n.jsx)(_,{closeMenu:i})]})]})};var B=()=>{const{user:e}=Object(v.d)((e=>e.auth),v.b);return Object(n.jsx)("ul",{children:(e?C:I).map(((e,t)=>Object(n.jsx)("li",{children:Object(n.jsx)(x.b,{to:e.routerPath,children:e.name})},t)))})};var U=()=>Object(n.jsxs)("div",{className:"row footer-back",children:[Object(n.jsx)("div",{className:"col-12 col-lg",children:Object(n.jsx)("div",{className:"ptf-animated-block","data-aos":"fade","data-aos-delay":"0",children:Object(n.jsx)(x.b,{to:"/",children:Object(n.jsx)("h2",{className:"text-Logo",children:"Githubit"})})})}),Object(n.jsx)("div",{className:"col-12 col-lg",children:Object(n.jsx)("div",{className:"ptf-animated-block","data-aos":"fade","data-aos-delay":"100",children:Object(n.jsx)("div",{className:"ptf-widget ptf-widget-links has-white-color",children:Object(n.jsx)(B,{})})})}),Object(n.jsx)("div",{className:"col-12 col-lg",children:Object(n.jsx)("div",{className:"ptf-animated-block","data-aos":"fade","data-aos-delay":"200",children:Object(n.jsxs)("div",{className:"ptf-widget ptf-widget-text",children:[Object(n.jsx)("a",{className:"fz-36 has-color",href:"mailto:<EMAIL>",children:"<EMAIL>"}),Object(n.jsx)("br",{}),Object(n.jsx)("a",{className:"fz-40 has-color",href:"tel:+12023401032",children:"****** 3401032"}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"0.625rem"}})]})})})]});var K=()=>Object(n.jsxs)("div",{children:[Object(n.jsx)(l.a,{children:Object(n.jsx)("title",{children:"GitHubit - 404 Page"})}),Object(n.jsx)(D,{}),Object(n.jsx)("div",{className:"ptf-main ptf-notFound",children:Object(n.jsx)("div",{className:"ptf-page ptf-page--404",children:Object(n.jsxs)("section",{className:"min-vh-100",children:[Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"14.375rem","--ptf-md":"7.1875rem"}}),Object(n.jsx)("div",{className:"container-xxl",children:Object(n.jsxs)("div",{className:"row",children:[Object(n.jsxs)("div",{className:"col-lg-5 offset-lg-1 order-lg-2 text-center",children:[Object(n.jsx)("div",{className:"ptf-animated-block","data-aos":"fade","data-aos-delay":"300",children:Object(n.jsx)("img",{src:"../assets/img/root/404-robot.png",alt:"robot",loading:"lazy"})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-lg":"5.625rem","--ptf-md":"2.8125rem"}})]}),Object(n.jsxs)("div",{className:"col-lg-6",children:[Object(n.jsx)("div",{className:"ptf-animated-block","data-aos":"fade","data-aos-delay":"0",children:Object(n.jsxs)("h1",{className:"large-heading text-white",children:["Opps...! ",Object(n.jsx)("br",{}),"Page not found"]})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem","--ptf-md":"1.875rem"}}),Object(n.jsx)("div",{className:"ptf-animated-block ","data-aos":"fade","data-aos-delay":"100",children:Object(n.jsx)("p",{children:"You seem can\u2019t to find the page you\u2019re looking for."})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"5rem","--ptf-md":"2.5rem"}}),Object(n.jsx)("div",{className:"ptf-animated-block ","data-aos":"fade","data-aos-delay":"200",children:Object(n.jsx)(x.b,{to:"/",className:"text-white",children:"Back to Home"})})]})]})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"14.375rem","--ptf-md":"7.1875rem"}})]})})}),Object(n.jsx)("footer",{className:"ptf-footer ptf-footer--style-3",children:Object(n.jsxs)("div",{className:"container-xxl",children:[Object(n.jsx)("div",{className:"ptf-footer__top",children:Object(n.jsx)(U,{})}),Object(n.jsx)("div",{className:"ptf-footer__bottom",children:Object(n.jsx)(f,{})})]})})]});function G(){const{pathname:e}=Object(N.n)();return Object(a.useEffect)((()=>{window.scrollTo(0,0)}),[e]),null}const W=[{name:"Introduction",dropDownItems:[{name:"Introduction",routerPath:"/sundaymart-documentation/introduction"},{name:"Server requirements",routerPath:"/sundaymart-documentation/recommendations"}]},{name:"Backend Api",dropDownItems:[{name:"Requirements",routerPath:"/sundaymart-documentation/server"},{name:"Installation",routerPath:"/sundaymart-documentation/mandatory-setup-backend"},{name:"Troubleshooting",routerPath:"/sundaymart-documentation/troubleshooting-backend"}]},{name:"Admin Panel",dropDownItems:[{name:"Requirements",routerPath:"/sundaymart-documentation/admin"},{name:"Installation",routerPath:"/sundaymart-documentation/install-on-server"},{name:"Troubleshooting",routerPath:"/sundaymart-documentation/troubleshooting-admin"}]},{name:"Customer Website",dropDownItems:[{name:"Requirements",routerPath:"/sundaymart-documentation/front"},{name:"Installation",routerPath:"/sundaymart-documentation/mandatory-setup-web"},{name:"Change page's static data",routerPath:"/sundaymart-documentation/change-static-data"}]},{name:"Customer App",dropDownItems:[{name:"Requirements",routerPath:"/sundaymart-documentation/mobile-app"},{name:"Installation",routerPath:"/sundaymart-documentation/mandatory-setup-customer"},{name:"Customization",routerPath:"/sundaymart-documentation/customization-customer"},{name:"App build & release",routerPath:"/sundaymart-documentation/customer-app-build-release"}]},{name:"Vendor App",dropDownItems:[{name:"Requirements",routerPath:"/sundaymart-documentation/vendor-app"},{name:"Installation",routerPath:"/sundaymart-documentation/mandatory-setup-vendor"},{name:"Customization",routerPath:"/sundaymart-documentation/customization-vendor"},{name:"App build & release",routerPath:"/sundaymart-documentation/vendor-app-build-release"}]},{name:"Driver App",dropDownItems:[{name:"Requirements",routerPath:"/sundaymart-documentation/driver-app"},{name:"Installation",routerPath:"/sundaymart-documentation/mandatory-setup-deliveryboy"},{name:"Customization",routerPath:"/sundaymart-documentation/customization-deliveryboy"},{name:"App build & release",routerPath:"/sundaymart-documentation/deliveryboy-app-build-release"}]},{name:"Pos App",dropDownItems:[{name:"Requirements",routerPath:"/sundaymart-documentation/pos-app"},{name:"Installation",routerPath:"/sundaymart-documentation/mandatory-setup-pos"},{name:"Customization",routerPath:"/sundaymart-documentation/customization-pos"},{name:"App build & release",routerPath:"/sundaymart-documentation/pos-app-build-release"}]},{name:"Firebase setup",routerPath:"/sundaymart-documentation/firebase"},{name:"Update",routerPath:"/sundaymart-documentation/update"}];var L=e=>{let{handleClickMenu:t,docMenu:s=W}=e;return Object(n.jsxs)(n.Fragment,{children:[Object(n.jsx)("div",{className:"ptf-offcanvas-menu__navigation",children:Object(n.jsx)(y.c,{children:Object(n.jsx)(y.d,{children:Object(n.jsx)(y.a,{className:"sidebar-menu_wrapper",children:s.map(((e,s)=>{var a,c;return Object(n.jsx)("div",{children:(null===(a=e.dropDownItems)||void 0===a?void 0:a.length)>0?Object(n.jsx)(y.e,{title:e.name,children:null===(c=e.dropDownItems)||void 0===c?void 0:c.map(((e,s)=>Object(n.jsx)(y.b,{children:Object(n.jsx)(x.b,{to:e.routerPath,onClick:t,children:e.name})},s)))},s):Object(n.jsx)(y.b,{className:"sidebar-menu",children:Object(n.jsx)(x.b,{to:e.routerPath,children:e.name})})},s)}))})})})}),Object(n.jsx)("div",{className:"ptf-offcanvas-menu__footer",children:Object(n.jsxs)("p",{className:"ptf-offcanvas-menu__copyright",children:["@",(new Date).getFullYear()," ",Object(n.jsx)("span",{children:"Githubit"}),". All Rights Reserved. ",Object(n.jsx)("br",{})]})})]})};var H=e=>{let{docMenu:t}=e;const[s,c]=Object(a.useState)(!1),i=()=>c(!s);return Object(n.jsxs)(n.Fragment,{children:[Object(n.jsx)("header",{className:"ptf-header--style-5 ptf-header--opaque",children:Object(n.jsx)("div",{className:"ptf-navbar ptf-navbar--main ptf-navbar--sticky",children:Object(n.jsx)("div",{className:"container-xxl",children:Object(n.jsxs)("div",{className:"ptf-navbar-inner",children:[Object(n.jsx)(x.b,{className:"ptf-navbar-logo",to:"/",children:Object(n.jsx)("h2",{className:"text-white",children:"Githubit"})}),Object(n.jsx)(M,{}),Object(n.jsx)("div",{className:"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle bar right mobile-document-menu",onClick:i,children:Object(n.jsx)("i",{className:"lnir lnir-menu-alt-5"})}),Object(n.jsx)("div",{className:"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle bar right",onClick:i})]})})})}),Object(n.jsxs)("div",{className:s?"ptf-offcanvas-menu is-open":"ptf-offcanvas-menu ",children:[Object(n.jsxs)("div",{className:"ptf-offcanvas-menu__header",children:[Object(n.jsx)("div",{className:"ptf-language-switcher"}),Object(n.jsx)("span",{className:"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle",onClick:i,children:Object(n.jsx)("i",{className:"lnir lnir-close"})})]}),Object(n.jsx)(L,{docMenu:t,handleClickMenu:i})]})]})};const q=[{name:"Introduction",dropDownItems:[{name:"Introduction",routerPath:"/foodyman-documentation/introduction"},{name:"Server requirements",routerPath:"/foodyman-documentation/recommendations"}]},{name:"Backend Api",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/server"},{name:"Installation",routerPath:"/foodyman-documentation/mandatory-setup-backend"},{name:"Payment installation",routerPath:"/foodyman-documentation/payment-installation"},{name:"Image settings",routerPath:"/foodyman-documentation/image-settings"},{name:"Troubleshooting",routerPath:"/foodyman-documentation/troubleshooting-backend"}]},{name:"Admin Panel",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/admin"},{name:"Installation",routerPath:"/foodyman-documentation/install-on-server"},{name:"Troubleshooting",routerPath:"/foodyman-documentation/troubleshooting-admin"}]},{name:"Customer Website",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/front"},{name:"Installation",routerPath:"/foodyman-documentation/mandatory-setup-web"}]},{name:"Qrcode Website",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/front-qr"},{name:"Installation",routerPath:"/foodyman-documentation/mandatory-setup-web-qr"}]},{name:"Customer App",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/mobile-app"},{name:"Installation",routerPath:"/foodyman-documentation/mandatory-setup-customer"},{name:"Customization",routerPath:"/foodyman-documentation/customization-customer"},{name:"App build & release",routerPath:"/foodyman-documentation/customer-app-build-release"}]},{name:"Vendor App",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/vendor-app"},{name:"Installation",routerPath:"/foodyman-documentation/mandatory-setup-vendor"},{name:"Customization",routerPath:"/foodyman-documentation/customization-vendor"},{name:"App build & release",routerPath:"/foodyman-documentation/vendor-app-build-release"}]},{name:"Driver App",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/driver-app"},{name:"Installation",routerPath:"/foodyman-documentation/mandatory-setup-deliveryboy"},{name:"Customization",routerPath:"/foodyman-documentation/customization-deliveryboy"},{name:"App build & release",routerPath:"/foodyman-documentation/deliveryboy-app-build-release"}]},{name:"Pos App",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/pos-app"},{name:"Installation",routerPath:"/foodyman-documentation/mandatory-setup-pos"},{name:"Customization",routerPath:"/foodyman-documentation/customization-pos"},{name:"App build & release",routerPath:"/foodyman-documentation/pos-app-build-release"}]},{name:"Kiosk App",dropDownItems:[{name:"Requirements",routerPath:"/foodyman-documentation/kiosk-app"},{name:"Installation",routerPath:"/foodyman-documentation/mandatory-setup-kiosk"},{name:"Customization",routerPath:"/foodyman-documentation/customization-kiosk"},{name:"App build & release",routerPath:"/foodyman-documentation/kiosk-app-build-release"}]},{name:"Firebase setup",routerPath:"/foodyman-documentation/firebase"},{name:"Update",routerPath:"/foodyman-documentation/update"}];var J=()=>Object(n.jsx)("div",{className:"docMenu",children:Object(n.jsx)(y.c,{className:"pro-sidebar",children:Object(n.jsx)(y.d,{children:Object(n.jsx)(y.a,{className:"sidebar-menu_wrapper",children:q.map(((e,t)=>{var s,a;return Object(n.jsx)("div",{children:(null===(s=e.dropDownItems)||void 0===s?void 0:s.length)>0?Object(n.jsx)(y.e,{title:e.name,defaultOpen:!0,children:null===(a=e.dropDownItems)||void 0===a?void 0:a.map(((e,t)=>Object(n.jsx)(y.b,{children:Object(n.jsx)(x.c,{className:"nav-link-active",to:e.routerPath,children:e.name})},t)))},t):Object(n.jsx)(y.b,{className:"sidebar-menu",children:Object(n.jsx)(x.c,{to:e.routerPath,className:"nav-link-active",children:e.name})})},t)}))})})})});var V=()=>Object(n.jsxs)("div",{className:"docContainer",children:[Object(n.jsx)(l.a,{children:Object(n.jsx)("title",{children:"GitHubit - Foodyman Documentation"})}),Object(n.jsx)(H,{docMenu:q}),Object(n.jsxs)("div",{className:"ptf-main",children:[Object(n.jsx)("div",{className:"ptf-page ptf-page--contact",children:Object(n.jsx)("section",{children:Object(n.jsx)("div",{className:"container-xxl",children:Object(n.jsxs)("div",{className:"row",children:[Object(n.jsx)("div",{className:"col-lg-3",children:Object(n.jsx)("div",{className:"docMenuContainer",children:Object(n.jsx)(J,{})})}),Object(n.jsx)("div",{className:"col-lg-9 docContent",children:Object(n.jsx)(N.a,{})})]})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3rem","--ptf-md":"2.5rem"}})]}),Object(n.jsx)("footer",{className:"ptf-footer ptf-footer--style-5 ptf-footer-doc",children:Object(n.jsx)("div",{className:"container-xxl",children:Object(n.jsx)(f,{})})})]}),Q=s(3);const X=q.flatMap((e=>e.routerPath?e:e.dropDownItems));var Z=function(){const e=window.location.hash.split("#")[1],t=X.findIndex((t=>t.routerPath===e));if(-1===t)return Object(n.jsx)("div",{className:"center-page-container",children:Object(n.jsxs)(x.b,{to:"/foodyman-documentation/recommendations",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Server requirements "})]})});const s=0===t?[null,X[1]]:t===X.length-1?[X[X.length-2],null]:[X[t-1],X[t+1]];return Object(n.jsx)("div",{className:"center-page-container",children:s.map(((e,t)=>e&&Object(n.jsxs)(x.b,{to:e.routerPath,className:"btn  previous",children:[Object(n.jsx)("p",{children:0===t?"Previos":"Next"}),Object(n.jsxs)("p",{className:"link",children:[" ",e.name," "]})]})))})};var $=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsxs)("div",{className:"alert alert-primary mb-5",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"Keep /configs/credential.php and /configs/init.php file while updating. If you don\u2019t keep them, you may have a problem with lisence."]}),Object(n.jsx)("h1",{className:"title",children:"Introduction"}),Object(n.jsxs)("strong",{className:"introduction-subTitle",children:["Build Your Business with our -",Object(n.jsx)("strong",{className:"strong",children:" Website"})]}),Object(n.jsx)("div",{className:"iframe-wrapper mb-5",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/678jGWxBI0k",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsxs)("strong",{className:"introduction-subTitle",children:["Build Your Business with our -",Object(n.jsx)("strong",{className:"strong",children:" Mobile app"})]}),Object(n.jsx)("div",{className:"iframe-wrapper mb-5",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/NKv9g64_qnM",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsxs)("strong",{className:"introduction-subTitle",children:["Build Your Business with our -",Object(n.jsx)("strong",{className:"strong",children:" Restaurant manager app "})]}),Object(n.jsx)("div",{className:"iframe-wrapper mb-5",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/MgbVA06C_NI",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsxs)("strong",{className:"introduction-subTitle",children:["Build Your Business with our -",Object(n.jsx)("strong",{className:"strong",children:" Delivery boy app"})]}),Object(n.jsx)("div",{className:"iframe-wrapper mb-5",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/IHK-mF0P50Q",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsx)("h3",{className:"introduction-title",children:"Overview"}),Object(n.jsxs)("strong",{className:"introduction-subTitle",children:["Welcome to - ",Object(n.jsx)("strong",{className:"strong",children:"Foodyman."})]}),Object(n.jsxs)("div",{children:["Foodyman is a multi restaurant marketplace, full solution ( apps, web, admin) developed by applying Laravel, React Js , Next Js and Flutter Frameworks. ",Object(n.jsx)("br",{}),"Laravel is an open-source PHP Framework intended for the development of web applications which is a complete toolset, robust, yet easy to understand with an excellent, expressive syntax. ",Object(n.jsx)("br",{}),"Next.js is an open-source web development framework created by Vercel enabling React-based web applications with server-side rendering and generating static websites.",Object(n.jsx)("br",{}),"React (also known as React.js or ReactJs) is a free and open-source front-end JavaScript library[3] for building user interfaces based on UI components.",Object(n.jsx)("br",{}),"Flutter is an open-source framework for mobile application development created by Google. It is applied to develop multi-platform, natively compiled applications for Android and iOS.",Object(n.jsx)("div",{className:"line-break"}),"Foodyman consists of 2 Mobile Applications, 5 Web Panel, 1 website:",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"introduction-img-container",children:[Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/admin-banner.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/admin-banner.jpg",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})}),Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/project/foodyman-banner.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/project/foodyman-banner.jpg",alt:"web main",loading:"lazy",className:"img-responsive-full"})})})})]}),Object(n.jsx)("div",{className:"line-break"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"3 Apps developed using Flutter"}),"\u2022 Customer application ",Object(n.jsx)("br",{}),"\u2022 Delivery Boy application (not included) ",Object(n.jsx)("br",{}),"\u2022 Manager application (not included) ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"5 web panel developed using Laravel & React Js"}),"\u2022 Admin web panel",Object(n.jsx)("br",{}),"\u2022 Manager web panel ",Object(n.jsx)("br",{}),"\u2022 Store web panel ",Object(n.jsx)("br",{}),"\u2022 Moderator web panel ",Object(n.jsx)("br",{}),"\u2022 Delivery boy web panel ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"1 web site developed using Typescript & Next Js"}),"\u2022 Customer web site ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"line-break"})]}),Object(n.jsx)(Z,{})]}),ee=s(10);var te=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Basic"}),"As mentioned above, Foodyman is a multi-shop/seller & single vendor marketplace, full solution ( apps, web, admin). To set up the project, you should have some programming knowledge and tool installed on your computer.",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Tools"}),"\u2022 For Mobile development: Visual Studio Code or Android studio , SDK and JDK with path setup in your IDE ",Object(n.jsx)("br",{}),"\u2022 For Backend development: Open Server Panel , XAMPP , Wamp Server , PhpStorm ",Object(n.jsx)("br",{}),"\u2022 For Frontend development: Nodejs , Visual Studio Code or WebStorm ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Knowledge"}),"\u2022 For Mobile development: Dart , Flutter , basic Java and basic Swift knowledge ",Object(n.jsx)("br",{}),"\u2022 For Backend development: PHP , MySQL , Laravel ",Object(n.jsx)("br",{}),"\u2022 For Frontend development: Next Js , React Js, Google map , Firebase ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"We would like to notify that the Envato price doesn\u2019t include any kind of installation and app publishing support. We kindly ask you to follow the documentation step by step for installation, setup and other branding related changes. Please note that, we bear no responsibility for your mistake. You are fully in charge for any kind of customizations made by your own."]}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"Note"})]}),Object(n.jsx)("p",{className:"h5",children:"The terminal in your server should support nodejs"})]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/introduction",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Introduction "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/server",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Backend "})]})]})]});var se=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Backend requirements"}),Object(n.jsxs)("ul",{children:[Object(n.jsx)("li",{children:"VPS server"}),Object(n.jsx)("li",{children:"PHP v8.1+"})]}),Object(n.jsx)("ul",{children:Object(n.jsxs)("li",{children:["Following extension have to be installed and enabled in your php:",Object(n.jsxs)("ul",{children:[Object(n.jsx)("li",{children:"openssl"}),Object(n.jsx)("li",{children:"fileinfo"}),Object(n.jsx)("li",{children:"gd"}),Object(n.jsx)("li",{children:"curl"}),Object(n.jsx)("li",{children:"sodium"}),Object(n.jsx)("li",{children:"zip"})]})]})}),Object(n.jsx)("br",{}),"\u2022 MySQL 8+ ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Tools"}),"\u2022 For Backend development: Open Server Panel , XAMPP , Wamp Server , PhpStorm ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Knowledge"}),"\u2022 For Backend development: PHP , MySQL , Laravel ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"We would like to notify that the Envato price doesn\u2019t include any kind of installation and app publishing support. We kindly ask you to follow the documentation step by step for installation, setup and other branding related changes. Please note that, we bear no responsibility for your mistake. You are fully in charge for any kind of customizations made by your own."]}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do this very carefully. We bear no responsibility for your mistake."]}),Object(n.jsx)(Z,{})]});var ae=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Frontend requirements"}),"\u2022 NodeJS v16+ ",Object(n.jsx)("br",{}),"\u2022 NextJS v12+ ",Object(n.jsx)("br",{}),"\u2022 ReactJS v17- ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Tools"}),"\u2022 For Frontend development: Nodejs , Visual Studio Code or WebStorm ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Knowledge"}),"\u2022 For Frontend development: Next Js , React Js, Google map , Firebase ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"We would like to notify that the Envato price doesn\u2019t include any kind of installation and app publishing support. We kindly ask you to follow the documentation step by step for installation, setup and other branding related changes. Please note that, we bear no responsibility for your mistake. You are fully in charge for any kind of customizations made by your own."]}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do this very carefully. We bear no responsibility for your mistake."]}),Object(n.jsx)(Z,{})]});var ce=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Frontend requirements"}),"\u2022 NodeJS v16+",Object(n.jsx)("br",{}),"\u2022 Vite v4+ ",Object(n.jsx)("br",{}),"\u2022 ReactJS v18+ ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Tools"}),"\u2022 For Frontend development: Nodejs , Visual Studio Code or WebStorm ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Knowledge"}),"\u2022 For Frontend development: Vite , React Js ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"We would like to notify that the Envato price doesn\u2019t include any kind of installation and app publishing support. We kindly ask you to follow the documentation step by step for installation, setup and other branding related changes. Please note that, we bear no responsibility for your mistake. You are fully in charge for any kind of customizations made by your own."]}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do this very carefully. We bear no responsibility for your mistake."]}),Object(n.jsx)(Z,{})]});var ne=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Build code and setup on server"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:" Frontend website "}),"\u2022 Open /.env and change every single credential with your own",Object(n.jsx)("br",{}),"\u2022 If there is no .env file, please create the one in root folder and fill as the same as in the example",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/qrcode-front.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/qrcode-front.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),"For building web data for deployment, you have to run commands:",Object(n.jsx)("br",{}),"\u2022 Install required package ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn"}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("yarn",3),children:3===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Build frontend using following command ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn build"}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s("yarn build",5),children:5===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Run project with pm2 ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"pm2 start \u201cyarn preview\u201d"}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("npm run start",6),children:6===e?"copied!":"copy"})]})}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"You have to configure your server for front website. Front website runs in port 3000. open server configuration file and add"," ",Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["ProxyPass / http://localhost:4173 ",Object(n.jsx)("br",{})," ProxyPassReverse / http://localhost:4173"]}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("ProxyPass / http://localhost:3000  ProxyPassReverse /http://localhost:3000",7),children:7===e?"copied!":"copy"})]})})," ","in your domain configuration section. Rewrite mode should be enabled in your server. After adding, restart your server"]}),Object(n.jsx)("h4",{children:"Or"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"Create .htaccess file in main webiste directory and put following content",Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"RewriteEngine on RewriteRule (.*) http://localhost:4173/$1 [P,L,R=301]"}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("RewriteEngine on RewriteRule (.*) http://localhost:3000/$1[P,L,R=301]",7),children:7===e?"copied!":"copy"})]})})]}),Object(n.jsx)(Z,{})]})};var ie=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Admin frontend requirements"}),"\u2022 NodeJS v16+",Object(n.jsx)("br",{}),"\u2022 React js v17- ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Tools"}),"\u2022 For Frontend development: Nodejs , Visual Studio Code or WebStorm ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Knowledge"}),"\u2022 For Frontend development: React Js, Google map , Firebase ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"We would like to notify that the Envato price doesn\u2019t include any kind of installation and app publishing support. We kindly ask you to follow the documentation step by step for installation, setup and other branding related changes. Please note that, we bear no responsibility for your mistake. You are fully in charge for any kind of customizations made by your own."]}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do this very carefully. We bear no responsibility for your mistake."]}),Object(n.jsx)(Z,{})]});var oe=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Recommendations"}),"\u2022 Operating system: Ubuntu 22.04 64bit ",Object(n.jsx)("br",{}),"\u2022 Cores: 4 ",Object(n.jsx)("br",{}),"\u2022 RAM: 4GB ",Object(n.jsx)("br",{}),"\u2022  VPS only, shared hosting servers are not recommended. ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do this very carefully. We bear no responsibility for your mistake."]}),Object(n.jsx)(Z,{})]});var re=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Frontend"}),Object(n.jsx)("p",{className:"inner-text",children:"Please, to setup frontend, download nodeJs and install it in your computer."}),"Windows:"," ",Object(n.jsx)("a",{href:"https://nodejs.org/dist/v16.17.0/node-v16.17.0-x64.msi",className:"introduction-link",children:"https://nodejs.org/dist/v16.17.0/node-v16.17.0-x64.msi"}),Object(n.jsx)("br",{}),"Mac:"," ",Object(n.jsx)("a",{href:"https://nodejs.org/dist/v16.17.0/node-v16.17.0.pkg",className:"introduction-link",children:"https://nodejs.org/dist/v16.17.0/node-v16.17.0.pkg"}),Object(n.jsx)("br",{}),"Linux:"," ",Object(n.jsx)("a",{href:"https://nodejs.org/dist/v16.17.0/node-v16.17.0-linux-x64.tar.xz",className:"introduction-link",children:"https://nodejs.org/dist/v16.17.0/node-v16.17.0-linux-x64.tar.xz"}),Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/flutter-sdk",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Mobile App "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/local-server",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Backend "})]})]})]}),le=s(40);var de=e=>{let{pageTitle:t="Mobile App"}=e;return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:t}),"\u2022 Android studio ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-success mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(le.a,{size:22})," ",Object(n.jsx)("strong",{children:" TIP"})]}),"You can download android studio via this link: ",Object(n.jsx)("br",{}),Object(n.jsx)("a",{href:"https://developer.android.com/studio?gclid=CjwKCAiAiKuOBhBQEiwAId_sK4X0PLQrES_2pG_S8nPflALgWSOCUEqRRAFpbS4AmR5mXmU6hIhvHxoCfBgQAvD_BwE&gclsrc=aw.ds",children:"https://developer.android.com/studio?gclid=CjwKCAiAiKuOBhBQEiwAId_sK4X0PLQrES_2pG_S8nPflALgWSOCUEqRRAFpbS4AmR5mXmU6hIhvHxoCfBgQAvD_BwE&gclsrc=aw.ds"})]}),"\u2022 Flutter SDK setup (version 3.24"," ",Object(n.jsx)("strong",{className:"strong",children:"Stable"}),") ",Object(n.jsx)("br",{}),"\u2022 Java setup (version 23) ",Object(n.jsx)("br",{}),"\u2022 Gradle (version 8.10) ",Object(n.jsx)("br",{}),"\u2022 JDK with path setup (only for vs code) ",Object(n.jsx)("br",{}),"\u2022 Xcode for IPA file build ",Object(n.jsx)("br",{}),"\u2022 State management -> riverpod ",Object(n.jsx)("br",{}),Object(n.jsx)(Z,{})]})};var je=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Mobile app"}),Object(n.jsx)("p",{className:"inner-text",children:"Please download and setup flutter from flutter.dev. You can find documentation for your own device via the following links."}),"Windows:"," ",Object(n.jsx)("a",{href:"https://docs.flutter.dev/get-started/install/windows",className:"introduction-link",children:"https://docs.flutter.dev/get-started/install/windows"}),Object(n.jsx)("br",{}),"Mac:"," ",Object(n.jsx)("a",{href:"https://docs.flutter.dev/get-started/install/macos",className:"introduction-link",children:"https://docs.flutter.dev/get-started/install/macos"}),Object(n.jsx)("br",{}),"Linux:"," ",Object(n.jsx)("a",{href:"https://docs.flutter.dev/get-started/install/linux",className:"introduction-link",children:"https://docs.flutter.dev/get-started/install/linux"}),Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/mobile-app",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Mobile App "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/local-front",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Frontend Local "})]})]})]});var be=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Backend"}),Object(n.jsxs)("p",{className:"inner-text",children:[" ","To run backend in your local machine, install one of these programs in your computer:"," "]}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"iframe-wrapper",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/xy3qeGYQNtw",title:"How to setup Docker on MacOS",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:" Xampp:"})," ",Object(n.jsx)("a",{href:"https://www.apachefriends.org/download.html",className:"introduction-link",children:"https://www.apachefriends.org/download.html"}),Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"WampServer:"})," ",Object(n.jsx)("a",{href:"https://www.wampserver.com/en/",className:"introduction-link",children:"https://www.wampserver.com/en/"}),Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/local-front",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:"Frontend"})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/mandatory-setup-backend",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Install on server "})]})]})]});var pe=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Install on server"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"Change title inside /public/index.html"]}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"Thus to install admin_panel, we need subdomains like admin.xyz.com and api.xyz.com."]}),Object(n.jsxs)("div",{className:"alert alert-warning mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"Warning"})]}),"You have to set SSL certificate for your admin website. Some functions doesn't work if your website doesn't have SSL certificate."]}),"\u2022 Download the code from codecayon ",Object(n.jsx)("br",{}),"\u2022 Extract the zip files",Object(n.jsx)("h3",{className:"introduction-contentTitle",children:" Admin Panel front "}),"\u2022 Open /public/index.html and change google map key with your google map key",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/index-map-key.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/index-map-key.png",alt:"admin config",loading:"lazy",className:"img-responsive-full"})})})})}),"\u2022 Open /src/configs/app_global.js and change BASE_URL with your server url",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/adminConfig.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/adminConfig.png",alt:"admin config",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/configFirebase.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/configFirebase.png",alt:"admin firebase",loading:"lazy",className:"img-responsive-full"})})})})}),"\u2022 Also select the main language",Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/theme.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/theme.png",alt:"theme config",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("p",{className:"mb-0",children:["\u2022 Create the ",Object(n.jsx)("strong",{children:"build"})," File"]}),Object(n.jsxs)("p",{children:["In your application's root directory, run ",Object(n.jsx)("strong",{children:"yarn "}),"to install the updated dependencies. Once this has finished, the next command you'll run is ",Object(n.jsx)("strong",{children:"yarn build"})]}),Object(n.jsxs)("p",{children:["You'll notice this creates a new directory in your project called",Object(n.jsx)("strong",{children:" build"}),". The build folder is essentially a super-compressed version of your program that has everything your browser needs to identify and run your app."]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/build.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/build.png",alt:"admin config",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{className:"mb-0",children:"\u2022 Connect to cPanel"}),Object(n.jsx)("p",{children:"Your cPanel manager should look something like this:"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/c-pannel.webp","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/c-pannel.webp",alt:"admin config",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsxs)("p",{className:"mb-0",children:["\u2022 Add the Build File Contents to ",Object(n.jsx)("strong",{children:"public_html"})]}),Object(n.jsxs)("p",{children:["Navigate to the build file in your app's root directory. Open it up and select all the contents ",Object(n.jsx)("strong",{children:"inside the build file."})," If you upload the entire build file itself, the process will not work."]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/build-file.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/build-file.png",alt:"admin config",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("p",{children:["Once you've copied all the contents inside the build file, upload them into ",Object(n.jsx)("strong",{children:"public_html."})]}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsxs)("p",{className:"mb-0",children:["\u2022 Create and Upload the ",Object(n.jsx)("strong",{children:".htaccess"})," File"]}),Object(n.jsxs)("p",{children:["In order for the routes to work in your React app, you need to add a",Object(n.jsx)("strong",{children:".htaccess"})," file. In the ",Object(n.jsx)("strong",{children:"public_html"}),"folder, at the same level as the ",Object(n.jsx)("strong",{children:"build"})," file contents, add a new file and name it ",Object(n.jsx)("strong",{children:".htaccess."})]}),Object(n.jsx)("p",{children:"Edit the file and insert the following boilerplate information:"}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("p",{children:["<IfModule mod_rewrite.c>",Object(n.jsx)("br",{})," RewriteEngine On",Object(n.jsx)("br",{})," RewriteBase /",Object(n.jsx)("br",{}),"RewriteRule ^index.html$ - [L]",Object(n.jsx)("br",{}),"RewriteCond %{REQUEST_FILENAME} !-f",Object(n.jsx)("br",{}),"RewriteCond %{REQUEST_FILENAME} !-d",Object(n.jsx)("br",{}),"RewriteCond %{REQUEST_FILENAME} !-l",Object(n.jsx)("br",{}),"RewriteRule . /index.html [L]",Object(n.jsx)("br",{}),"</IfModule>"]})}),Object(n.jsx)("p",{children:"Save the file."}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"If you meet this error, please, get recaptcha key using this tutorial."," ",Object(n.jsxs)("a",{href:"https://contactform7.com/recaptcha-v2/",target:"_blank",children:[Object(n.jsx)("strong",{children:"Link"})," "]})]}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/recaptcha.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/recaptcha.jpg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("iframe",{width:"100%",height:"420",src:"https://www.youtube.com/embed/HMUKllwbY2s",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0}),Object(n.jsx)(Z,{})]});var me=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Mandatory setup"}),Object(n.jsx)("h5",{className:"introduction-contentTitle",children:" Map Configuration"}),"A client should get ",Object(n.jsx)("strong",{className:"strong",children:"Map API"})," from Google in order to enable the maps into the panels.",Object(n.jsxs)("h5",{className:"introduction-contentTitle",children:[" ","Firebase Configuration (for notification)"]}),"The Firebase Push Notification will send messages for every order status. If admin turn on the status, customers, store, delivery man will get status notification when order status changed and if he turned off that then no one will get that message. To set up firebase notification go to admin panel\u2019s ",Object(n.jsx)("strong",{className:"strong",children:"Notification settings"})," ","menu.",Object(n.jsx)("h5",{className:"introduction-contentTitle",children:" Payment Configuration"}),"Here, Admin will be introduced with the payment gateways. Cash on delivery, Digital Payment like Razor pay, Paypal, Stripe, Paystack, MercadoPago, Payment accept are available for payment gateways. Admin can make the necessary setup to make the status active or inactive for mentioned payment gateways.",Object(n.jsx)("h5",{className:"introduction-contentTitle",children:" SMS Module Configuration"}),"SMS Module is utilized for SMS Gateways for OTP sending in the simplest way of user verification. Customer will receive OTP when they create their own account or it is used for password recovery.",Object(n.jsx)("h5",{className:"introduction-contentTitle",children:" Firebase configuration"}),Object(n.jsx)("span",{className:"youtube-blog",children:Object(n.jsx)("iframe",{src:"https://www.youtube.com/embed/OLwNp_e5bxM",title:"YouTube video player",frameBorder:"11",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"})}),Object(n.jsxs)("h5",{className:"introduction-contentTitle",children:[" ","Firebase auth configuration"]}),Object(n.jsx)("span",{className:"youtube-blog",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/5HzrGiY9cFo",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),Object(n.jsxs)("h5",{className:"introduction-contentTitle",children:[" ","Firebase chat configuration"]}),Object(n.jsx)("span",{className:"youtube-blog",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/29ARDVIXvXk",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),Object(n.jsxs)("h5",{className:"introduction-contentTitle",children:[" ","Firebase notification configuration"]}),Object(n.jsx)("span",{className:"youtube-blog",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/3E-kEe5X2bg",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),Object(n.jsxs)("h5",{className:"introduction-contentTitle",children:[" ","How to connect firebase to project"]}),Object(n.jsx)("span",{className:"youtube-blog",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/jCgZZiz1480",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/install-on-server",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Install on server "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/customization",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Customization"})]})]})]});var he=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Customization"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Translate admin panel"}),"\u2022 Translation admin panel is very easy. To translate admin panel, open"," ",Object(n.jsx)("br",{}),Object(n.jsxs)("span",{className:"introduction-step-2",children:["Settings",">"," Translations menu"]}),"and Translate all words into your language.",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"introduction-img-container",children:[Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/translation2.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/translation2.jpg",alt:"image01",loading:"lazy",className:"img-responsive-full"})})})}),Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/translation1.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/translation1.jpg",alt:"image02",loading:"lazy",className:"img-responsive-full"})})})})]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/mandatory-setup",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Mandatory setup Admin Panel "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/troubleshooting-admin",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Troubleshooting admin"})]})]})]});var ue=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Mandatory setup"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"The same documentation for Delivery app"]}),Object(n.jsxs)("h4",{className:"introduction-contentTitle",children:[" ","Run an existing flutter project on IDE"," "]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Logo "}),"You can generate app icon using this website https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html ",Object(n.jsx)("br",{}),"\u2022 Then go to"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/src/main/res"}),"and replace all mipmap folder with your /android folder ",Object(n.jsx)("br",{}),"\u2022 Again go to ",Object(n.jsx)("span",{className:"introduction-step",children:"/ios/Runner"})," and replace Assets.xcassets with your generated Assets.xcassets folder ",Object(n.jsx)("br",{}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Name "}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["1.Change the value of label from",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{children:'android:label="My App"'}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s('android:label="My App"\u2019',2),children:2===e?"copied!":"copy"})]})]})]}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["2.Change the value of CFBundleName from",Object(n.jsx)("span",{className:"introduction-step",children:" /iOS/Runner/info.plist"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/info.plist"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{children:["<key>CFBundleName</key>"," ",Object(n.jsx)("br",{}),"<string>My App</string>"]}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("<key>CFBundleName</key><string>My App</string>",3),children:3===e?"copied!":"copy"})]})]})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change Base URL"}),"Please do NOT put slash ( / ) at the end of your base url. Use your admin url as base url. First you have to install your admin panel. For example: If your admin url is"," ",Object(n.jsx)("span",{className:"introduction-step",children:" https://your_domain.com/admin"}),"then base url will be https://your_domain.com. Open"," ",Object(n.jsxs)("span",{className:"introduction-step",children:[" ","/lib/src/core/constants/app_constants.dart"]}),"and replace baseUrl variable value with your own URL.",Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/lib/src/core/constants/app_constants.dart"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["static const String baseUrl=",Object(n.jsx)("span",{className:"text-black",children:"'https://your_domain.com'"})]}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("baseUrl=https://your_domain.com",4),children:4===e?"copied!":"copy"})]})]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/mobile-1.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/mobile-1.jpg",alt:"image01",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Package"}),"Firstly, find out the existing package name. You can find it out from top of",Object(n.jsx)("span",{className:"introduction-step",children:"/app/src/main/AndroidManifest.xml"}),"file. Then right click on project folder from android studio and click on",Object(n.jsx)("span",{className:"introduction-step",children:"replace in path"}),"You will see a popup window with two input boxes. In first box you have to put existing package name that you saw in"," ",Object(n.jsx)("span",{className:"introduction-step",children:"AndroidManifest.xml"}),"file previously and then write down your preferred package name in second box and then click on"," ",Object(n.jsx)("span",{className:"introduction-step",children:"Replace All"})," button.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Setup Firebase for Push Notification"}),"\u2022 Firstly, change your package name. If you didn\u2019t then go to this link"," ",Object(n.jsx)("br",{}),"\u2022 Create your own firebase project from"," ",Object(n.jsx)("strong",{className:"strong",children:"https://console.firebase.google.com "}),"and also add there an android app with your own package name and app name"," ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do NOT create multiple projects if you have multiple app like User App, Delivery App. Create only one project and add multiple apps under one project."]}),"\u2022 Click register app and download google-services.json file from there."," ",Object(n.jsx)("br",{}),"\u2022 Copy that file and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/ folder"}),Object(n.jsx)("br",{}),"\u2022 Create a totally white png logo for notification icon. Paste it on"," ",Object(n.jsxs)("span",{className:"introduction-step",children:[" ","/android/app/src/main/res/drawable/"]}),"and replace notification_icon.png with your whiter version logo. ",Object(n.jsx)("br",{}),"\u2022 For IOS again create an app under the same project and download"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GoogleService-Info.plist"}),"and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/ folder"})," ",Object(n.jsx)("br",{}),"Also you are advised to follow this documentation for full setup for IOS:",Object(n.jsxs)("strong",{className:"strong",children:[" ","https://firebase.flutter.dev/docs/messaging/apple-integration"," "]}),Object(n.jsx)("br",{}),"\u2022 Paste firebase server key into admin panel Notification Settings section. You can receive server key from"," ",Object(n.jsxs)("span",{className:"introduction-step",children:["Firebase project settings","->","Cloud Messaging","->"," Server Key"," "]}),Object(n.jsx)("br",{}),"After setup, please restart your IDE and uninstall your previously installed app, and then run it. Also do NOT try to test it on emulator or simulator. Emulator and simulators are unable to get push. Use real device for this purpose.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Before upload app store mode set true"}),Object(n.jsx)("img",{src:"./assets/img/uzmart-doc/appstoremode.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Add Google Map API Key"}),"\u2022 Please generate the google API key. You can visit this link - https://developers.google.com/maps/documentation/embed/get-api-key ",Object(n.jsx)("br",{}),"\u2022 You need to enable mentioned APIs: Direction API, Distance Matrix API, Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. ",Object(n.jsx)("br",{}),"\u2022 Then you have to enable billing account. Visit this URL for activation: https://support.google.com/googleapi/answer/6158867?hl=en ",Object(n.jsx)("br",{}),"\u2022 After generating API key, you have to paste it on 3 different places: Android, iOS and web. ",Object(n.jsx)("br",{}),"For android, open",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"})," ","and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"com.google.android.geo.API_KEY "}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:" /android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>'}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s('<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>',5),children:5===e?"copied!":"copy"})]})]}),Object(n.jsxs)("div",{className:"mt-4",children:["For iOS: open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/Runner/AppDelegate.swift"}),"and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GMSServices.provideAPIKey"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/AppDelegate.swift"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")'}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s('GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")',6),children:6===e?"copied!":"copy"})]})]})]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/build-code-and-setup-on-server",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Build code and setup -on-server "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/customization-mobile",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Customization Mobile App "})]})]})]})};var xe=()=>Object(n.jsxs)("div",{className:"introduction","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Install on server backend"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"Thus to install admin_backend, we need subdomains like admin.xyz.com and api.xyz.com."]}),Object(n.jsxs)("div",{className:"alert alert-danger mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"Warning"})]}),"Don\u2019t install the admin_backend in a sub directory (like: yourdomain.com/folder)"]}),Object(n.jsx)("p",{children:"\u2022 When you connect subdomain to backend code, set public folder as root folder for this subdomains."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/domain.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/domain.png",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Compress your Laravel project files into a ZIP file. If git, node_modules folders exist in your Laravel project, be sure not to add them in the zip file."}),Object(n.jsx)("p",{children:"\u2022 Login to your cPanel"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/c-panel.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/c-panel.png",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Create a database & user. (Make sure to save the database name, user, and password into a text file in a safe place.)"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/create-db.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/create-db.png",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Add new user"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/create-user.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/create-user.png",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022Add User to Database"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/add-user.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/add-user.png",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Open your File Manager in cPanel and upload the ZIP file of your Laravel project into the public_html directory and extract the ZIP file. The ZIP file should be uploaded and extracted directly in the public_html folder and not inside any subfolder."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/backend-file.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/backend-file.png",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Now update your database details into the config file by opening the database.php file from the config folder and updating your database name, username and password into the database.php file. Now save the file. (Any sensitive credentials should not be uploaded in the env file in shared hosting.)"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/config.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/config.png",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 To make your project secure and protect your .htaccess and .env files, open the .htaccess file from the public_html folder and write the following code to disable direct access and directory browsing:"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/config-2.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/config-2.png",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Open terminal. Go admin_backend folder."}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsx)("p",{children:"composer install"})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"1.75rem"}}),Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/composer.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/composer.jpg",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"to install all php dependencies if you have not uploaded vendor folder. Then change your environmental variables in the .env file Generate a new application key"}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsx)("p",{children:"php artisan key:generate"})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"1.75rem"}}),Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/generate.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/generate.jpg",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"Don\u2019t forget to give a permission to write to storage, config and bootstrap folders."]}),Object(n.jsxs)("ul",{children:[Object(n.jsx)("li",{children:"Create /config/init.php file with empty content"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/init-php.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/init-php.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("li",{children:Object(n.jsx)("strong",{children:"Change lisence keys in /config/credential.php with your\u2019s"})})]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/php_backend_food.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/php_backend_food.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./sertificate.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./sertificate.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),Object(n.jsxs)("ul",{children:[Object(n.jsx)("li",{children:"purchase_id = Item ID"}),Object(n.jsx)("li",{children:"purchase_code = Item Purchase Code"})]})]}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsxs)("p",{children:["Migrate and seed your database as per you need.",Object(n.jsx)("br",{})," 1. Migrate Tables: ",Object(n.jsx)("strong",{children:"`php artisan migrate`"})," and"," ",Object(n.jsx)("strong",{children:"`php artisan migrate --path=database/migrations/booking`"}),Object(n.jsx)("br",{})," 2. Seed Database: ",Object(n.jsx)("strong",{children:"`php artisan db:seed`"}),Object(n.jsx)("br",{}),"Set the correct folder permissions"]}),Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/migrate.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/migrate.jpg",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"1.75rem"}}),Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/migrate-2.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/migrate-2.jpg",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsx)("p",{children:"chmod -R 775 storage"})}),Object(n.jsx)("p",{children:"Run storage link"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{className:"mb-0",children:"php artisan storage:link"}),Object(n.jsx)("p",{children:"php artisan optimize:clear"})]}),Object(n.jsx)("p",{children:"Your app should work now."}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"Usage Commands"})]}),"1. Migrate Tables: `php artisan migrate` ",Object(n.jsx)("br",{})," 2. Seed Database: `php artisan db:seed` ",Object(n.jsx)("br",{})," 3. Start Server: `php artisan serve` ",Object(n.jsx)("br",{}),"4. Watch Server: `yarn watch` or `npm run watch`"]}),Object(n.jsx)("h5",{children:"Other Commands:"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsxs)("p",{className:"mb-0",children:["# Turn on maintenance mode ",Object(n.jsx)("br",{})," php artisan down"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Turn off maintenance mode ",Object(n.jsx)("br",{})," php artisan up"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Sync composer.local with composer.json ",Object(n.jsx)("br",{})," composer update --lock"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Create Model ",Object(n.jsx)("br",{})," php artisan make:model Flight"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Create Model & Migration ",Object(n.jsx)("br",{}),"php artisan make:model Flight -m"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Create Migration ",Object(n.jsx)("br",{})," php artisan make:migration create_users_table --create=users"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Update Existing Migration ",Object(n.jsx)("br",{})," php artisan make:migration add_votes_to_users_table --table=users"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Create Controller ",Object(n.jsx)("br",{})," php artisan make:controller PhotoController"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Composer dump ",Object(n.jsx)("br",{})," composer dump-autoload"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Clear caches ",Object(n.jsx)("br",{})," php artisan cache:clear"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Clear and cache routes ",Object(n.jsx)("br",{})," php artisan route:clear ",Object(n.jsx)("br",{})," php artisan route:cache"]}),Object(n.jsxs)("p",{className:"mb-0",children:["# Clear and cache config ",Object(n.jsx)("br",{})," php artisan config:clear ",Object(n.jsx)("br",{})," php artisan config:cache"]})]}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 How to setup foodyman project from cpanel - Backend"}),Object(n.jsx)("iframe",{width:"100%",height:"420",src:"https://www.youtube.com/embed/TKyjjO8yZY0",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0}),Object(n.jsx)("br",{}),Object(n.jsx)("br",{}),Object(n.jsx)("p",{children:'\u2022 To add the translation of the errors in the backend, clone the "en" folder and put the language short code in the name of the folder.'}),Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/backend_error_lang1.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/backend_error_lang1.jpg",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})}),Object(n.jsx)("br",{}),Object(n.jsx)("p",{children:"\u2022 In the picture, for example, the messages in the Arabic language errors.php file have been changed."}),Object(n.jsx)("br",{}),Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/backend_error_lang1.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/backend_error_lang1.jpg",alt:"admin",loading:"lazy",className:"img-responsive-full"})})})}),Object(n.jsx)(Z,{})]});var Oe=()=>Object(n.jsxs)("div",{className:"introduction","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Install Payments"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"All other payment methods are done in a similar way through the admin panel and adding a webhook on the payment system website"]}),Object(n.jsxs)("div",{className:"alert alert-danger mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"Warning"})]}),"Webhook urls: ",Object(n.jsx)("br",{}),"- stripe:"," ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/stripe/payment"})," ",Object(n.jsx)("br",{}),"- razorpay:"," ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/razorpay/payment"})," ",Object(n.jsx)("br",{}),"- paystack:"," ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/paystack/payment"})," ",Object(n.jsx)("br",{}),"- paytabs: ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/paytabs/payment"})," ",Object(n.jsx)("br",{}),"- flutterwave:"," ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/flw/payment"})," ",Object(n.jsx)("br",{}),"- paypal: ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/paypal/payment"})," ",Object(n.jsx)("br",{}),"- mercado-pago:"," ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/mercado-pago/payment"})," ",Object(n.jsx)("br",{}),"- moyasar:"," ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/moya-sar/payment"})," ",Object(n.jsx)("br",{}),"- mollie: ",Object(n.jsx)("b",{children:"https://your-api-url.com/api/v1/webhook/mollie/payment"})," ",Object(n.jsx)("br",{})]}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Stripe integration."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)("iframe",{width:"100%",height:"400",src:"https://www.youtube.com/embed/ZROHalq6kNg?si=uhK0fckee2R1Xyja",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Paystack integration."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)("iframe",{width:"100%",height:"400",src:"https://www.youtube.com/embed/nPFoKorY_cw?si=A_FXSJIklHJuAYsS",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{children:"\u2022 Razorpay integration."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)("iframe",{width:"100%",height:"400",src:"https://www.youtube.com/embed/ITjVuL8ub-Q?si=9qoDmJyz6HWEibRh",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)(Z,{})]});var ge=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Image Settings"}),Object(n.jsxs)("div",{className:"alert alert-danger mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"You can upload images to server storage or aws s3 space."]}),Object(n.jsx)("h4",{children:"1. If you want to use server storage to upload, do following steps:"}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/img-settings.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/img-settings.png",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("ol",{children:[Object(n.jsxs)("li",{children:["Open .env file and change ",Object(n.jsx)("strong",{children:"example.com"})," with your backend website url"]}),Object(n.jsx)("li",{children:"Open public folder and remove storage"}),Object(n.jsx)("li",{children:'Run following commands on terminal: "php artisan storage link" and "php artisan optimize:clear'}),Object(n.jsx)("li",{children:"Open admin website, go to General settings page and press permission tab. Disable aws and save."})]}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/img_settings.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/img_settings.jpg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{children:"2. If you want to use aws s3 bucket to upload images, do following steps:"}),Object(n.jsxs)("ol",{children:[Object(n.jsx)("li",{children:"Configure your aws s3 bucket"}),Object(n.jsx)("li",{children:"Open .env and put your aws bucket link and all necessary aws keys. Then save."}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/img-setting-storage.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/img-setting-storage.png",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("li",{children:'run following command on terminal: "php artisan optimize:clear"'})]}),Object(n.jsx)(Z,{})]});var fe=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Mandatory setup"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"The same documentation for Delivery app"]}),Object(n.jsxs)("h4",{className:"introduction-contentTitle",children:[" ","Run an existing flutter project on IDE"," "]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Logo "}),"You can generate app icon using this website https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html ",Object(n.jsx)("br",{}),"\u2022 Then go to"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/src/main/res"}),"and replace all mipmap folder with your /android folder ",Object(n.jsx)("br",{}),"\u2022 Again go to ",Object(n.jsx)("span",{className:"introduction-step",children:"/ios/Runner"})," and replace Assets.xcassets with your generated Assets.xcassets folder ",Object(n.jsx)("br",{}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Name "}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["1.Change the value of label from",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{children:'android:label="My App"'}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s('android:label="My App"\u2019',2),children:2===e?"copied!":"copy"})]})]})]}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["2.Change the value of CFBundleName from",Object(n.jsx)("span",{className:"introduction-step",children:" /iOS/Runner/info.plist"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/info.plist"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{children:["<key>CFBundleName</key>"," ",Object(n.jsx)("br",{}),"<string>My App</string>"]}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("<key>CFBundleName</key><string>My App</string>",3),children:3===e?"copied!":"copy"})]})]})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change Base URL"}),"Please do NOT put slash ( / ) at the end of your base url. Use your admin url as base url. First you have to install your admin panel. For example: If your admin url is"," ",Object(n.jsx)("span",{className:"introduction-step",children:" https://your_domain.com/admin"}),"then base url will be https://your_domain.com. Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /lib/app_constants.dart"}),"and replace baseUrl variable value with your own URL.",Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/lib/app_constants.dart"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["static const String baseUrl=",Object(n.jsx)("span",{className:"text-black",children:"'https://your_domain.com'"})]}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("baseUrl=https://your_domain.com",4),children:4===e?"copied!":"copy"})]})]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/app_const.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/app_const.png",alt:"image0",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Package"}),"Firstly, find out the existing package name. You can find it out from top of",Object(n.jsx)("span",{className:"introduction-step",children:"/app/src/main/AndroidManifest.xml"}),"file. Then right click on project folder from android studio and click on",Object(n.jsx)("span",{className:"introduction-step",children:"replace in path"}),"You will see a popup window with two input boxes. In first box you have to put existing package name that you saw in"," ",Object(n.jsx)("span",{className:"introduction-step",children:"AndroidManifest.xml"}),"file previously and then write down your preferred package name in second box and then click on"," ",Object(n.jsx)("span",{className:"introduction-step",children:"Replace All"})," button.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Setup Firebase"}),"\u2022 Firstly, change your package name. If you didn\u2019t then go to this link ",Object(n.jsx)("br",{}),"\u2022 Create your own firebase project from"," ",Object(n.jsx)("strong",{className:"strong",children:"https://console.firebase.google.com "}),"and also add there an android app with your own package name and app name"," ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do NOT create multiple projects if you have multiple app like User App, Delivery App. Create only one project and add multiple apps under one project."]}),"\u2022 Click register app and download google-services.json file from there."," ",Object(n.jsx)("br",{}),"\u2022 Copy that file and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/ folder"}),Object(n.jsx)("br",{}),"\u2022 Create a totally white png logo for notification icon. Paste it on"," ",Object(n.jsxs)("span",{className:"introduction-step",children:[" ","/android/app/src/main/res/drawable/"]}),"and replace notification_icon.png with your whiter version logo. ",Object(n.jsx)("br",{}),"\u2022 For IOS again create an app under the same project and download"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GoogleService-Info.plist"}),"and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/ folder"})," ",Object(n.jsx)("br",{}),"Also you are advised to follow this documentation for full setup for IOS:",Object(n.jsxs)("strong",{className:"strong",children:[" ","https://firebase.flutter.dev/docs/messaging/apple-integration"," "]}),Object(n.jsx)("br",{}),"After setup, please restart your IDE and uninstall your previously installed app, and then run it. Also do NOT try to test it on emulator or simulator. Emulator and simulators are unable to get push. Use real device for this purpose.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Add Google Map API Key"}),"\u2022 Please generate the google API key. You can visit this link - https://developers.google.com/maps/documentation/embed/get-api-key ",Object(n.jsx)("br",{}),"\u2022 You need to enable mentioned APIs: Direction API, Distance Matrix API, Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. ",Object(n.jsx)("br",{}),"\u2022 Then you have to enable billing account. Visit this URL for activation: https://support.google.com/googleapi/answer/6158867?hl=en ",Object(n.jsx)("br",{}),"\u2022 After generating API key, you have to paste it on 3 different places: Android, iOS and web. ",Object(n.jsx)("br",{}),"For android, open",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"})," ","and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"com.google.android.geo.API_KEY "}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:" /android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>'}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s('<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>',5),children:5===e?"copied!":"copy"})]})]}),Object(n.jsxs)("div",{className:"mt-4",children:["For iOS: open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/Runner/AppDelegate.swift"}),"and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GMSServices.provideAPIKey"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/AppDelegate.swift"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")'}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s('GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")',6),children:6===e?"copied!":"copy"})]})]})]}),Object(n.jsx)(Z,{})]})};var ye=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Mandatory setup"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"The same documentation for Delivery app"]}),Object(n.jsxs)("h4",{className:"introduction-contentTitle",children:[" ","Run an existing flutter project on IDE"," "]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Logo "}),"You can generate app icon using this website https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html ",Object(n.jsx)("br",{}),"\u2022 Then go to"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/src/main/res"}),"and replace all mipmap folder with your /android folder ",Object(n.jsx)("br",{}),"\u2022 Again go to ",Object(n.jsx)("span",{className:"introduction-step",children:"/ios/Runner"})," and replace Assets.xcassets with your generated Assets.xcassets folder ",Object(n.jsx)("br",{}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Name "}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["1.Change the value of label from",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{children:'android:label="My App"'}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s('android:label="My App"\u2019',2),children:2===e?"copied!":"copy"})]})]})]}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["2.Change the value of CFBundleName from",Object(n.jsx)("span",{className:"introduction-step",children:" /iOS/Runner/info.plist"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/info.plist"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{children:["<key>CFBundleName</key>"," ",Object(n.jsx)("br",{}),"<string>My App</string>"]}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("<key>CFBundleName</key><string>My App</string>",3),children:3===e?"copied!":"copy"})]})]})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change Base URL"}),"Please do NOT put slash ( / ) at the end of your base url. Use your admin url as base url. First you have to install your admin panel. For example: If your admin url is"," ",Object(n.jsx)("span",{className:"introduction-step",children:" https://your_domain.com/admin"}),"then base url will be https://your_domain.com. Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /lib/app_constants.dart"}),"and replace baseUrl variable value with your own URL.",Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/lib/app_constants.dart"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["static const String baseUrl=",Object(n.jsx)("span",{className:"text-black",children:"'https://your_domain.com'"})]}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("baseUrl=https://your_domain.com",4),children:4===e?"copied!":"copy"})]})]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/app_const.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/app_const.png",alt:"image02",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Package"}),"Firstly, find out the existing package name. You can find it out from top of",Object(n.jsx)("span",{className:"introduction-step",children:"/app/src/main/AndroidManifest.xml"}),"file. Then right click on project folder from android studio and click on",Object(n.jsx)("span",{className:"introduction-step",children:"replace in path"}),"You will see a popup window with two input boxes. In first box you have to put existing package name that you saw in"," ",Object(n.jsx)("span",{className:"introduction-step",children:"AndroidManifest.xml"}),"file previously and then write down your preferred package name in second box and then click on"," ",Object(n.jsx)("span",{className:"introduction-step",children:"Replace All"})," button.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Setup Firebase"}),"\u2022 Firstly, change your package name. If you didn\u2019t then go to this link ",Object(n.jsx)("br",{}),"\u2022 Create your own firebase project from"," ",Object(n.jsx)("strong",{className:"strong",children:"https://console.firebase.google.com "}),"and also add there an android app with your own package name and app name"," ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do NOT create multiple projects if you have multiple app like User App, Delivery App. Create only one project and add multiple apps under one project."]}),"\u2022 Click register app and download google-services.json file from there."," ",Object(n.jsx)("br",{}),"\u2022 Copy that file and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/ folder"}),Object(n.jsx)("br",{}),"\u2022 Create a totally white png logo for notification icon. Paste it on"," ",Object(n.jsxs)("span",{className:"introduction-step",children:[" ","/android/app/src/main/res/drawable/"]}),"and replace notification_icon.png with your whiter version logo. ",Object(n.jsx)("br",{}),"\u2022 For IOS again create an app under the same project and download"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GoogleService-Info.plist"}),"and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/ folder"})," ",Object(n.jsx)("br",{}),"Also you are advised to follow this documentation for full setup for IOS:",Object(n.jsxs)("strong",{className:"strong",children:[" ","https://firebase.flutter.dev/docs/messaging/apple-integration"," "]}),Object(n.jsx)("br",{}),"After setup, please restart your IDE and uninstall your previously installed app, and then run it. Also do NOT try to test it on emulator or simulator. Emulator and simulators are unable to get push. Use real device for this purpose.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Add Google Map API Key"}),"\u2022 Please generate the google API key. You can visit this link - https://developers.google.com/maps/documentation/embed/get-api-key ",Object(n.jsx)("br",{}),"\u2022 You need to enable mentioned APIs: Direction API, Distance Matrix API, Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. ",Object(n.jsx)("br",{}),"\u2022 Then you have to enable billing account. Visit this URL for activation: https://support.google.com/googleapi/answer/6158867?hl=en ",Object(n.jsx)("br",{}),"\u2022 After generating API key, you have to paste it on 3 different places: Android, iOS and web. ",Object(n.jsx)("br",{}),"For android, open",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"})," ","and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"com.google.android.geo.API_KEY "}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:" /android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>'}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s('<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>',5),children:5===e?"copied!":"copy"})]})]}),Object(n.jsxs)("div",{className:"mt-4",children:["For iOS: open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/Runner/AppDelegate.swift"}),"and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GMSServices.provideAPIKey"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/AppDelegate.swift"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")'}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s('GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")',6),children:6===e?"copied!":"copy"})]})]})]}),Object(n.jsx)(Z,{})]})};var ve=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" POS setup"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"The same documentation for Delivery app"]}),Object(n.jsxs)("h4",{className:"introduction-contentTitle",children:[" ","Run an existing flutter project on IDE"," "]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Logo "}),"You can generate app icon using this website https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html ",Object(n.jsx)("br",{}),"\u2022 Then go to"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/src/main/res"}),"and replace all mipmap folder with your /android folder ",Object(n.jsx)("br",{}),"\u2022 Again go to ",Object(n.jsx)("span",{className:"introduction-step",children:"/ios/Runner"})," and replace Assets.xcassets with your generated Assets.xcassets folder ",Object(n.jsx)("br",{}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Name "}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["1.Change the value of label from",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{children:'android:label="My App"'}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s('android:label="My App"\u2019',2),children:2===e?"copied!":"copy"})]})]})]}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["2.Change the value of CFBundleName from",Object(n.jsx)("span",{className:"introduction-step",children:" /iOS/Runner/info.plist"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/info.plist"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{children:["<key>CFBundleName</key>"," ",Object(n.jsx)("br",{}),"<string>My App</string>"]}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("<key>CFBundleName</key><string>My App</string>",3),children:3===e?"copied!":"copy"})]})]})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change Base URL"}),"Please do NOT put slash ( / ) at the end of your base url. Use your admin url as base url. First you have to install your admin panel. For example: If your admin url is"," ",Object(n.jsx)("span",{className:"introduction-step",children:" https://your_domain.com/admin"}),"then base url will be https://your_domain.com. Open"," ",Object(n.jsxs)("span",{className:"introduction-step",children:[" ","/lib/src/core/constants/secret_vars.dart"]}),"and replace baseUrl variable value with your own URL.",Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/lib/src/core/constants/secret_vars.dart"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["static const String baseUrl=",Object(n.jsx)("span",{className:"text-black",children:"'https://your_domain.com'"})]}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("baseUrl=https://your_domain.com",4),children:4===e?"copied!":"copy"})]})]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/pos-translation2.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/pos-translation2.jpg",alt:"image02",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Package"}),"Firstly, find out the existing package name. You can find it out from top of",Object(n.jsx)("span",{className:"introduction-step",children:"/app/src/main/AndroidManifest.xml"}),"file. Then right click on project folder from android studio and click on",Object(n.jsx)("span",{className:"introduction-step",children:"replace in path"}),"You will see a popup window with two input boxes. In first box you have to put existing package name that you saw in"," ",Object(n.jsx)("span",{className:"introduction-step",children:"AndroidManifest.xml"}),"file previously and then write down your preferred package name in second box and then click on"," ",Object(n.jsx)("span",{className:"introduction-step",children:"Replace All"})," button.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Setup Firebase for Push Notification"}),"\u2022 Firstly, change your package name. If you didn\u2019t then go to this link"," ",Object(n.jsx)("br",{}),"\u2022 Create your own firebase project from"," ",Object(n.jsx)("strong",{className:"strong",children:"https://console.firebase.google.com "}),"and also add there an android app with your own package name and app name"," ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do NOT create multiple projects if you have multiple app like User App, Delivery App. Create only one project and add multiple apps under one project."]}),"\u2022 Click register app and download google-services.json file from there."," ",Object(n.jsx)("br",{}),"\u2022 Copy that file and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/ folder"}),Object(n.jsx)("br",{}),"\u2022 Create a totally white png logo for notification icon. Paste it on"," ",Object(n.jsxs)("span",{className:"introduction-step",children:[" ","/android/app/src/main/res/drawable/"]}),"and replace notification_icon.png with your whiter version logo. ",Object(n.jsx)("br",{}),"\u2022 For IOS again create an app under the same project and download"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GoogleService-Info.plist"}),"and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/ folder"})," ",Object(n.jsx)("br",{}),"Also you are advised to follow this documentation for full setup for IOS:",Object(n.jsxs)("strong",{className:"strong",children:[" ","https://firebase.flutter.dev/docs/messaging/apple-integration"," "]}),Object(n.jsx)("br",{}),"\u2022 Paste firebase server key into admin panel Notification Settings section. You can receive server key from"," ",Object(n.jsxs)("span",{className:"introduction-step",children:["Firebase project settings","->","Cloud Messaging","->"," Server Key"," "]}),Object(n.jsx)("br",{}),"After setup, please restart your IDE and uninstall your previously installed app, and then run it. Also do NOT try to test it on emulator or simulator. Emulator and simulators are unable to get push. Use real device for this purpose.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Add Google Map API Key"}),"\u2022 Please generate the google API key. You can visit this link - https://developers.google.com/maps/documentation/embed/get-api-key ",Object(n.jsx)("br",{}),"\u2022 You need to enable mentioned APIs: Direction API, Distance Matrix API, Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. ",Object(n.jsx)("br",{}),"\u2022 Then you have to enable billing account. Visit this URL for activation: https://support.google.com/googleapi/answer/6158867?hl=en ",Object(n.jsx)("br",{}),"\u2022 After generating API key, you have to paste it on 3 different places: Android, iOS and web. ",Object(n.jsx)("br",{}),"For android, open",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"})," ","and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"com.google.android.geo.API_KEY "}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:" /android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>'}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s('<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>',5),children:5===e?"copied!":"copy"})]})]}),Object(n.jsxs)("div",{className:"mt-4",children:["For iOS: open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/Runner/AppDelegate.swift"}),"and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GMSServices.provideAPIKey"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/AppDelegate.swift"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")'}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s('GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")',6),children:6===e?"copied!":"copy"})]})]})]}),Object(n.jsx)(Z,{})]})};var Ne=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Mandatory setup"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"The same documentation for Delivery app"]}),Object(n.jsxs)("h4",{className:"introduction-contentTitle",children:[" ","Run an existing flutter project on IDE"," "]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Logo "}),"You can generate app icon using this website https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html ",Object(n.jsx)("br",{}),"\u2022 Then go to"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/src/main/res"}),"and replace all mipmap folder with your /android folder ",Object(n.jsx)("br",{}),"\u2022 Again go to ",Object(n.jsx)("span",{className:"introduction-step",children:"/ios/Runner"})," and replace Assets.xcassets with your generated Assets.xcassets folder ",Object(n.jsx)("br",{}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Name "}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["1.Change the value of label from",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{children:'android:label="My App"'}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s('android:label="My App"\u2019',2),children:2===e?"copied!":"copy"})]})]})]}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["2.Change the value of CFBundleName from",Object(n.jsx)("span",{className:"introduction-step",children:" /iOS/Runner/info.plist"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/info.plist"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{children:["<key>CFBundleName</key>"," ",Object(n.jsx)("br",{}),"<string>My App</string>"]}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("<key>CFBundleName</key><string>My App</string>",3),children:3===e?"copied!":"copy"})]})]})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change Base URL"}),"Please do NOT put slash ( / ) at the end of your base url. Use your admin url as base url. First you have to install your admin panel. For example: If your admin url is"," ",Object(n.jsx)("span",{className:"introduction-step",children:" https://your_domain.com/admin"}),"then base url will be https://your_domain.com. Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /lib/app_constants.dart"}),"and replace baseUrl variable value with your own URL.",Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/lib/app_constants.dart"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["static const String baseUrl=",Object(n.jsx)("span",{className:"text-black",children:"'https://your_domain.com'"})]}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("baseUrl=https://your_domain.com",4),children:4===e?"copied!":"copy"})]})]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/app_const.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/app_const.png",alt:"image05",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Package"}),"Firstly, find out the existing package name. You can find it out from top of",Object(n.jsx)("span",{className:"introduction-step",children:"/app/src/main/AndroidManifest.xml"}),"file. Then right click on project folder from android studio and click on",Object(n.jsx)("span",{className:"introduction-step",children:"replace in path"}),"You will see a popup window with two input boxes. In first box you have to put existing package name that you saw in"," ",Object(n.jsx)("span",{className:"introduction-step",children:"AndroidManifest.xml"}),"file previously and then write down your preferred package name in second box and then click on"," ",Object(n.jsx)("span",{className:"introduction-step",children:"Replace All"})," button.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Setup Firebase"}),"\u2022 Firstly, change your package name. If you didn\u2019t then go to this link ",Object(n.jsx)("br",{}),"\u2022 Create your own firebase project from"," ",Object(n.jsx)("strong",{className:"strong",children:"https://console.firebase.google.com "}),"and also add there an android app with your own package name and app name"," ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do NOT create multiple projects if you have multiple app like User App, Delivery App. Create only one project and add multiple apps under one project."]}),"\u2022 Click register app and download google-services.json file from there."," ",Object(n.jsx)("br",{}),"\u2022 Copy that file and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/ folder"}),Object(n.jsx)("br",{}),"\u2022 Create a totally white png logo for notification icon. Paste it on"," ",Object(n.jsxs)("span",{className:"introduction-step",children:[" ","/android/app/src/main/res/drawable/"]}),"and replace notification_icon.png with your whiter version logo. ",Object(n.jsx)("br",{}),"\u2022 For IOS again create an app under the same project and download"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GoogleService-Info.plist"}),"and paste it under",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/ folder"})," ",Object(n.jsx)("br",{}),"Also you are advised to follow this documentation for full setup for IOS:",Object(n.jsxs)("strong",{className:"strong",children:[" ","https://firebase.flutter.dev/docs/messaging/apple-integration"," "]}),Object(n.jsx)("br",{}),"After setup, please restart your IDE and uninstall your previously installed app, and then run it. Also do NOT try to test it on emulator or simulator. Emulator and simulators are unable to get push. Use real device for this purpose.",Object(n.jsxs)("h4",{className:"introduction-contentTitle",children:[" ","Before upload app store mode set true"]}),Object(n.jsx)("img",{src:"./assets/img/doc/app_const.png",alt:"images",loading:"lazy",className:"img-responsive-full"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Add Google Map API Key"}),"\u2022 Please generate the google API key. You can visit this link - https://developers.google.com/maps/documentation/embed/get-api-key ",Object(n.jsx)("br",{}),"\u2022 You need to enable mentioned APIs: Direction API, Distance Matrix API, Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. ",Object(n.jsx)("br",{}),"\u2022 Then you have to enable billing account. Visit this URL for activation: https://support.google.com/googleapi/answer/6158867?hl=en ",Object(n.jsx)("br",{}),"\u2022 After generating API key, you have to paste it on 3 different places: Android, iOS and web. ",Object(n.jsx)("br",{}),"For android, open",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"})," ","and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"com.google.android.geo.API_KEY "}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:" /android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>'}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s('<meta-data android:name="com.google.android.geo.API_KEY" android:value=\u201cYOUR_MAP_API_KEY_HERE\u201d/>',5),children:5===e?"copied!":"copy"})]})]}),Object(n.jsxs)("div",{className:"mt-4",children:["For iOS: open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/iOS/Runner/AppDelegate.swift"}),"and place the value of"," ",Object(n.jsx)("span",{className:"introduction-step",children:"GMSServices.provideAPIKey"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/AppDelegate.swift"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")'}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s('GMSServices.provideAPIKey(\u201cYOUR_MAP_API_KEY_HERE")',6),children:6===e?"copied!":"copy"})]})]})]}),Object(n.jsx)(Z,{})]})};var we=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Customization"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Translate mobile app"}),"\u2022 Translation admin panel is very easy. To translate admin panel, go to admin panel and open ",Object(n.jsx)("br",{}),Object(n.jsxs)("span",{className:"introduction-step-2",children:["Settings ",">"," Translations menu"]}),"and Translate all words into your language",Object(n.jsxs)("div",{className:"introduction-img-container",children:[Object(n.jsx)("img",{src:"./assets/img/doc/translation2.jpg",alt:"img",className:"img-responsive"}),Object(n.jsx)("img",{src:"./assets/img/doc/translation1.jpg",alt:"img",className:"img-responsive"})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Color"}),"\u2022 Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/lib/presentation/styles/style.dart"}),"file and change colors as you want. ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)("img",{src:"./assets/img/doc/color.jpg",alt:"img",className:"img-responsive-full"})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Font"}),"\u2022 in our apps, google font package is installed. You may change app fonts easily by selecting new fonts",Object(n.jsx)(Z,{})]});var ke=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Customization"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Translate mobile app"}),"\u2022 Translation admin panel is very easy. To translate admin panel, go to admin panel and open ",Object(n.jsx)("br",{}),Object(n.jsxs)("span",{className:"introduction-step-2",children:["Settings ",">"," Translations menu"]}),"and Translate all words into your language",Object(n.jsxs)("div",{className:"introduction-img-container",children:[Object(n.jsx)("img",{src:"./assets/img/doc/translation2.jpg",alt:"img",className:"img-responsive"}),Object(n.jsx)("img",{src:"./assets/img/doc/translation1.jpg",alt:"img",className:"img-responsive"})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Color"}),"\u2022 Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/lib/presentation/styles/style.dart"}),"file and change colors as you want. ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)("img",{src:"./assets/img/doc/color2.jpg",alt:"img",className:"img-responsive-full"})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Font"}),"\u2022 in our apps, google font package is installed. You may change app fonts easily by selecting new fonts",Object(n.jsx)(Z,{})]});var Ae=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Customization"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Translate mobile app"}),"\u2022 Translation admin panel is very easy. To translate admin panel, go to admin panel and open ",Object(n.jsx)("br",{}),Object(n.jsxs)("span",{className:"introduction-step-2",children:["Settings ",">"," Translations menu"]}),"and Translate all words into your language",Object(n.jsxs)("div",{className:"introduction-img-container",children:[Object(n.jsx)("img",{src:"./assets/img/doc/translation2.jpg",alt:"img",className:"img-responsive"}),Object(n.jsx)("img",{src:"./assets/img/doc/translation1.jpg",alt:"img",className:"img-responsive"})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Color"}),"\u2022 Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/lib/presentation/theme/app_style.dart"}),"file and change colors as you want. ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)("img",{src:"./assets/img/doc/color3.jpg",alt:"img",className:"img-responsive-full"})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Font"}),"\u2022 in our apps, google font package is installed. You may change app fonts easily by selecting new fonts",Object(n.jsx)(Z,{})]});var Pe=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Customization"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Translate mobile app"}),"\u2022 Translation admin panel is very easy. To translate admin panel, go to admin panel and open ",Object(n.jsx)("br",{}),Object(n.jsxs)("span",{className:"introduction-step-2",children:["Settings ",">"," Translations menu"]}),"and Translate all words into your language",Object(n.jsxs)("div",{className:"introduction-img-container",children:[Object(n.jsx)("img",{src:"./assets/img/doc/translation2.jpg",alt:"img",className:"img-responsive"}),Object(n.jsx)("img",{src:"./assets/img/doc/translation1.jpg",alt:"img",className:"img-responsive"})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Color"}),"\u2022 Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/lib/presentation/styles/style.dart"}),"file and change colors as you want. ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)("img",{src:"./assets/img/demand24/presentation-style-dart.png",alt:"img",className:"img-responsive-full"})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Font"}),"\u2022 in our apps, google font package is installed. You may change app fonts easily by selecting new fonts",Object(n.jsx)(Z,{})]});var Se=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"App build & release"}),Object(n.jsx)("h3",{className:"introduction-title",children:"Build for Android"}),"For debug build you can run command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk"}),Object(n.jsx)("span",{className:1===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk",1),children:1===e?"copied!":"copy"})]})}),"You will get a larger merged apk with this. But you can split them with this command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi"}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi",2),children:2===e?"copied!":"copy"})]})}),"Build file location:",Object(n.jsx)("span",{className:"introduction-step",children:" /build/app/outputs/apk/"}),"For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/android"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Build for iOS"}),"There are no general way to generate app for iOS. Apple doesn\u2019t allow to install app like this debug way. If you want to install it on your iOS device then you have to deploy it on TestFlight or AppStore. For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/ios"}),Object(n.jsx)(Z,{})]})};var Fe=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Pos build & release"}),Object(n.jsx)("h3",{className:"introduction-title",children:"Build for Android"}),"For debug build you can run command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk"}),Object(n.jsx)("span",{className:1===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk",1),children:1===e?"copied!":"copy"})]})}),"You will get a larger merged apk with this. But you can split them with this command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi"}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi",2),children:2===e?"copied!":"copy"})]})}),"Build file location:",Object(n.jsx)("span",{className:"introduction-step",children:" /build/app/outputs/apk/"}),"For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/android"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Build for iOS"}),"There are no general way to generate app for iOS. Apple doesn\u2019t allow to install app like this debug way. If you want to install it on your iOS device then you have to deploy it on TestFlight or AppStore. For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/ios"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Build for Windows"}),"Firebase must be committed when building to Windows. Otherwise you will have problem with firebase on Windows.",Object(n.jsx)(Z,{})]})};var Te=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"App build & release"}),Object(n.jsx)("h3",{className:"introduction-title",children:"Build for Android"}),"For debug build you can run command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk"}),Object(n.jsx)("span",{className:1===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk",1),children:1===e?"copied!":"copy"})]})}),"You will get a larger merged apk with this. But you can split them with this command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi"}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi",2),children:2===e?"copied!":"copy"})]})}),"Build file location:",Object(n.jsx)("span",{className:"introduction-step",children:" /build/app/outputs/apk/"}),"For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/android"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Build for iOS"}),"There are no general way to generate app for iOS. Apple doesn\u2019t allow to install app like this debug way. If you want to install it on your iOS device then you have to deploy it on TestFlight or AppStore. For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/ios"}),Object(n.jsx)(Z,{})]})};var Ie=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"App build & release"}),Object(n.jsx)("h3",{className:"introduction-title",children:"Build for Android"}),"For debug build you can run command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk"}),Object(n.jsx)("span",{className:1===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk",1),children:1===e?"copied!":"copy"})]})}),"You will get a larger merged apk with this. But you can split them with this command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi"}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi",2),children:2===e?"copied!":"copy"})]})}),"Build file location:",Object(n.jsx)("span",{className:"introduction-step",children:" /build/app/outputs/apk/"}),"For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/android"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Build for iOS"}),"There are no general way to generate app for iOS. Apple doesn\u2019t allow to install app like this debug way. If you want to install it on your iOS device then you have to deploy it on TestFlight or AppStore. For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/ios"}),Object(n.jsx)(Z,{})]})};var Ce=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Build code and setup on server"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:" Frontend website "}),"\u2022 Open /.env and change every single credential with your own",Object(n.jsx)("br",{}),"\u2022 If there is no .env file, please create the one in root folder and fill as the same as in the example",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsxs)(Q.a,{children:[Object(n.jsx)("a",{href:"./assets/img/doc/foodyman-front.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/foodyman-front.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"})}),Object(n.jsx)("a",{href:"./assets/img/firebase-messaging.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/firebase-messaging.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"})})]})})}),"For building web data for deployment, you have to run commands:",Object(n.jsx)("br",{}),"\u2022 Install required package ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn"}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("yarn",3),children:3===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Build frontend using following command ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn build"}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s("yarn build",5),children:5===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Run project with pm2 ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'pm2 start "yarn start"'}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("yarn start",6),children:6===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"iframe-wrapper mb-2",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/Fd1TXISmw-o",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsx)(Z,{})]})};var _e=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Build code and setup on server"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:" Frontend website "}),"\u2022 Open /.env and change every single credential with your own",Object(n.jsx)("br",{}),"\u2022 If there is no .env file, please create the one in root folder and fill as the same as in the example",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsxs)(Q.a,{children:[Object(n.jsx)("a",{href:"./assets/img/doc/foodyman-front.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/foodyman-front.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"})}),Object(n.jsx)("a",{href:"./assets/img/firebase-messaging.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/firebase-messaging.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"})})]})})}),"For building web data for deployment, you have to run commands:",Object(n.jsx)("br",{}),"\u2022 Install required package ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn"}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("yarn",3),children:3===e?"copied!":"copy"})]})}),Object(n.jsx)("p",{className:"mt-2",children:"or"}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"npm install"}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("npm install",4),children:4===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Build frontend using following command ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn build"}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s("yarn build",5),children:5===e?"copied!":"copy"})]})}),Object(n.jsx)("p",{className:"mt-2",children:"or"}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"npm run build"}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("npm run build",6),children:6===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Run project with pm2 ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'pm2 start "npm run start"'}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("npm run start",6),children:6===e?"copied!":"copy"})]})}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"You have to configure your server for front website. Front website runs in port 3000. open server configuration file and add"," ",Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["ProxyPass / http://localhost:3000 ",Object(n.jsx)("br",{})," ProxyPassReverse / http://localhost:3000"]}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("ProxyPass / http://localhost:3000  ProxyPassReverse /http://localhost:3000",7),children:7===e?"copied!":"copy"})]})})," ","in your domain configuration section. Rewrite mode should be enabled in your server. After adding, restart your server"]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/mandatory-setup-web",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Mandatory setup web "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/mandatory-setup-customer",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Mandatory setup customer "})]})]})]})};var Re=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Build code and setup on server"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:" Admin Panel front "}),"\u2022 Open /next.config.js and change BASE_URL with your server url",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/web-1.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/sundaymartWebSettings.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),"For building web data for deployment, you have to run commands:",Object(n.jsx)("br",{}),"\u2022 Install required package ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn"}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("yarn",3),children:3===e?"copied!":"copy"})]})}),Object(n.jsx)("p",{className:"mt-2",children:"or"}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"npm install"}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("npm install",4),children:4===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Build frontend using following command ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn build"}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s("yarn build",5),children:5===e?"copied!":"copy"})]})}),Object(n.jsx)("p",{className:"mt-2",children:"or"}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"npm run build"}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("npm run build",6),children:6===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Run project with pm2 ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:'pm2 start "npm run start"'}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("npm run start",6),children:6===e?"copied!":"copy"})]})}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"You have to configure your server for front website. Front website runs in port 3000. open server configuration file and add"," ",Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["ProxyPass / http://localhost:3000 ",Object(n.jsx)("br",{})," ProxyPassReverse / http://localhost:3000"]}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("ProxyPass / http://localhost:3000  ProxyPassReverse /http://localhost:3000",7),children:7===e?"copied!":"copy"})]})})," ","in your domain configuration section. Rewrite mode should be enabled in your server. After adding, restart your server"]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/build-code-and-setup-on-server",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Build code and setup on server "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/mandatory-setup-customer",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Mandatory setup customer "})]})]})]})};var ze=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Admin Panel"}),Object(n.jsx)("h3",{className:"introduction-title",children:"Update"}),"For update the admin panel just upload admin_front and admin_backend folders in your project root folder and extract it. Build project.",Object(n.jsx)("div",{className:"alert alert-danger mt-4",role:"alert",children:"Keep config/init.php and config/credential.php while updating"}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"iframe-wrapper",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/I6bhGMcVR5E",title:"How to update project",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"You have to be a developer in this case. If something goes wrong, we won't be responsible for that."]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/firebase",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Firebase "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/update-app-web",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" App & Web "})]})]})]});var Me=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"App & Web"}),Object(n.jsx)("h3",{className:"introduction-title",children:"Update mobile App"}),"Download source code and replace it with old one and build.",Object(n.jsx)("h3",{className:"introduction-title",children:"Update mobile Web"}),"Upload front files to server and do following steps:",Object(n.jsx)("br",{}),"\u2022 Install required package ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn"}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("yarn",3),children:3===e?"copied!":"copy"})]})}),Object(n.jsx)("p",{className:"mt-2",children:"or"}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"npm install"}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("npm install",4),children:4===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Build frontend using following command ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"yarn next build"}),Object(n.jsx)("span",{className:5===e?"bg-success copy":"copy",onClick:()=>s("yarn build",5),children:5===e?"copied!":"copy"})]})}),Object(n.jsx)("p",{className:"mt-2",children:"or"}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"npm next build"}),Object(n.jsx)("span",{className:6===e?"bg-success copy":"copy",onClick:()=>s("npm run build",6),children:6===e?"copied!":"copy"})]})}),Object(n.jsx)("br",{}),"\u2022 Restart project with pm2 ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"pm2 stop 0 & pm2 start 0"}),Object(n.jsx)("span",{className:7===e?"bg-success copy":"copy",onClick:()=>s("yarn build",7),children:7===e?"copied!":"copy"})]})}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"iframe-wrapper",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/I6bhGMcVR5E",title:"How to update project",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"You have to be a developer in this case. If something goes wrong, we won't be responsible for that."]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-documentation/update-admin-panel",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Admin Panel "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-documentation/support-plan",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Support plan "})]})]})]})};var Ee=e=>{let{data:t=[]}=e;return Object(n.jsx)(n.Fragment,{children:t.map(((e,t)=>Object(n.jsx)("div",{className:"col-xl-4 col-lg-6 mb-5",children:Object(n.jsx)("div",{className:"ptf-animated-block h-100","data-aos":"fade","data-aos-delay":100*t,children:Object(n.jsxs)("div",{className:"ptf-pricing-table h-100",children:[2===t?Object(n.jsx)("div",{className:"badge",children:"Popular"}):"",Object(n.jsx)("div",{className:"ptf-pricing-table__header",children:Object(n.jsx)("h4",{className:"ptf-pricing-table__title",children:e.title})}),Object(n.jsxs)("div",{className:"ptf-pricing-table__price",children:[Object(n.jsx)("span",{className:"currency",children:"$"}),Object(n.jsx)("span",{className:"price",children:e.price}),Object(n.jsx)("span",{className:"period",children:"/ Month"})]}),Object(n.jsx)("div",{className:"ptf-pricing-table__description",children:"Month"}),Object(n.jsx)("div",{className:"ptf-pricing-table__content",dangerouslySetInnerHTML:{__html:e.description}}),Object(n.jsx)("div",{className:"ptf-pricing-table__action",children:Object(n.jsx)(x.b,{className:"ptf-btn ptf-btn--primary ptf-btn--block",to:"/login",onClick:e=>e.preventDefault(),children:"Purchase"})})]})})},t)))})};var Ye=e=>{let{data:t}=e;return Object(n.jsxs)("section",{children:[Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"8.75rem","--ptf-md":"4.375rem"}}),Object(n.jsxs)("div",{className:"container",children:[Object(n.jsx)("div",{className:"ptf-animated-block","data-aos":"fade","data-aos-delay":"0",children:Object(n.jsx)("h2",{className:"h2 large-heading text-white",children:"Pricing & Plan"})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem","--ptf-md":"1.875rem"}})]}),Object(n.jsx)("div",{className:"container",children:Object(n.jsx)("div",{className:"row",style:{"--bs-gutter-x":"2rem"},children:Object(n.jsx)(Ee,{data:t})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"8.75rem","--ptf-md":"4.375rem"}})]})};const De=[{title:"Plan 0",price:"150",description:"Setup Admin and Store panel on your hosting."},{title:"Plan 1",price:"400",description:"Setup Admin and Store panel on your hosting. Link user, POS system and deliveryman apps with your admin panel. Customize app name, logo, a color, package name, splash screen, etc. Setup OTP configuration."},{title:"Plan 2",price:"600",description:"Plan 1 + Configure push notification. Setup payment gateways. Third party API setup (google map API setup included)."},{title:"Plan 3",price:"800",description:"Plan 2 + Translate all applications to your local language. Upload user, store and delivery man apps to Google Play Store. [Optional] Upload apps to Apple Store (+$200 per app for Apple store release)."},{title:"Plan 4",price:"1100",description:"Plan 3 + Upload All apps to the Apple Store. One revision before project handover."}];var Be=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Support"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Licenses, activation and customization"}),Object(n.jsx)("div",{children:"After you purchased our product and installed it, please reach out to us via Telegram (**************) or WhatsApp (**************) to schedule activation. Please note that once you have downloaded the item, we will not issue a refund."}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Holders of a regular license:"}),Object(n.jsxs)("div",{children:["We are happy to respond if you have one or two specific questions but can\u2019t provide extensive support and guidance for free. No support is provided to set up your server or flutter environment in your local device (iOS or Android) installation. To receive installation support, you need to purchase one of our support plans.",Object(n.jsx)("br",{}),"Please check our documentation before you purchase the item if you want to install it on your own. We are continually striving to improve documentation, so if you are unhappy with the current state of documentation, please check in later, as an updated version might suit your installation needs better. Please note that once you have downloaded the item, we will not issue a refund. Please also note that some of the features such as subscription are only available under the extended license.",Object(n.jsx)("br",{}),"Holders of an extended license",Object(n.jsx)("br",{}),"Please reach out to us if you have questions."]}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Customization"}),Object(n.jsxs)("div",{children:["Our team is available to help with customization. Please reach out to us with detailed information so that our team can give you a quote.",Object(n.jsx)("br",{}),"If our team has not done the customization, we cannot help you with any potential issues that may arise from your customization."]}),Object(n.jsx)(Ye,{data:De})]});var Ue=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Firebase"}),Object(n.jsx)("p",{className:"inner-text",children:"We use Firebase Authentication, Cloud Messaging and Firestore in Foodyman. So, you have to setup Firebase in order to use Foodyman properly."}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"iframe-wrapper",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/OLwNp_e5bxM",title:"Firebase configuration",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"iframe-wrapper",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/jCgZZiz1480",title:"How to run connect firebase to project",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"iframe-wrapper",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/5HzrGiY9cFo",title:"Firebase auth configuration",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"iframe-wrapper",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/cchc8T1QnCE?si=RvYlapwJ2S-oMd0h",title:"Cloud messaging",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsxs)("div",{className:"",children:[Object(n.jsx)("p",{style:{margin:"10px"},children:"Add downloaded file to backend_folder/storage/app and run the command php artisan storage:link if you haven\u2019t run it before"}),Object(n.jsx)("img",{src:"../assets/img/google-service-json.jpg",alt:"icon",style:{width:"100%"}})]}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"iframe-wrapper",children:Object(n.jsx)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/29ARDVIXvXk",title:"Firestore",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0})}),Object(n.jsx)(Z,{})]});var Ke=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Troubleshooting backend"}),Object(n.jsxs)("div",{class:"accordion",id:"accordionExample",children:[Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingOne",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseOne","aria-expanded":"true","aria-controls":"collapseOne",children:"Troubleshooting 1"})}),Object(n.jsx)("div",{id:"collapseOne",class:"accordion-collapse collapse show","aria-labelledby":"headingOne","data-bs-parent":"#accordionExample",children:Object(n.jsxs)("div",{class:"accordion-body",children:[Object(n.jsx)("p",{children:"if you meet your serialized closure might have been modified or it's unsafe to be unserialized error you should use this code"}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/trouble.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/trouble.jpg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsx)("p",{className:"mb-0",children:"php artisan storage:link"})}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsx)("p",{children:"php artisan optimize:clear"})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingTwo",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseTwo","aria-expanded":"true","aria-controls":"collapseTwo",children:"Troubleshooting 2"})}),Object(n.jsx)("div",{id:"collapseTwo",class:"accordion-collapse collapse show","aria-labelledby":"headingTwo","data-bs-parent":"#accordionExample1",children:Object(n.jsxs)("div",{class:"accordion-body",children:[Object(n.jsx)("p",{children:"If you meet this error, check db credentials entered correctly in .env file. If everything is correct, run"}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/laravel-migrate.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/laravel-migrate.png",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsx)("p",{className:"mb-0",children:"php artisan optimize:clear"})}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsx)("p",{className:"mb-0",children:"php artisan migrate"})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingThree",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseThree","aria-expanded":"true","aria-controls":"collapseThree",children:"Troubleshooting 3"})}),Object(n.jsx)("div",{id:"collapseThree",class:"accordion-collapse collapse show","aria-labelledby":"headingThree","data-bs-parent":"#accordionExample3",children:Object(n.jsxs)("div",{class:"accordion-body",children:[Object(n.jsxs)("p",{children:["If you meet this error while installing, check following extentions installed and enabled in your system:",Object(n.jsxs)("ul",{children:[Object(n.jsx)("li",{children:"openssl"}),Object(n.jsx)("li",{children:"fileinfo"}),Object(n.jsx)("li",{children:"gd"}),Object(n.jsx)("li",{children:"curl"}),Object(n.jsx)("li",{children:"sodium"}),Object(n.jsx)("li",{children:"zip"})]})]}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/trsh-3.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/trsh-3.png",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingFive",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseFive","aria-expanded":"true","aria-controls":"collapseFive",children:"Troubleshooting 4"})}),Object(n.jsx)("div",{id:"collapseFive",class:"accordion-collapse collapse show","aria-labelledby":"headingFive","data-bs-parent":"#accordionExample4",children:Object(n.jsxs)("div",{class:"accordion-body",children:[Object(n.jsx)("h6",{className:"fw-normal",children:"If you meet this error, please check /config/credentials.php file. Are your lisence keys in this file ? If not, please, add your lisence keys to this file. Then run \u201cphp artisan optimize:clear\u201d"})," ",Object(n.jsx)("br",{})," ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/project/troubleshooting6.jpeg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/project/troubleshooting6.jpeg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingTwo",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseTwo","aria-expanded":"true","aria-controls":"collapseTwo",children:"Troubleshooting 5"})}),Object(n.jsx)("div",{id:"collapseTwo",class:"accordion-collapse collapse show","aria-labelledby":"headingTwo","data-bs-parent":"#accordionExample1",children:Object(n.jsxs)("div",{class:"accordion-body",children:[Object(n.jsxs)("p",{children:["\"message\": \"SQLSTATE[42000]: Syntax error or access violation: 1055 'elbarrio_foody_test_bd.u.firstname' isn't in GROUP BY (SQL: select u.id as id, u.firstname as firstname, u.img as img, u.lastname as lastname, u.phone as phone, sum(distinct orders.total_price) as total_price, count(distinct orders.id) as count from orders cross join users as u on orders.user_id = u.id where date(`orders`.`created_at`) ",">"," 2023-02-24 and orders.status = delivered group by id having total_price ",">"," 0 or count ",">",' 0 order by total_price desc limit 6 offset 0) in /home2/elbarrio/api.lavilla.pe/vendor/laravel/framework/src/Illuminate/Database/Connection.php:712"'," ","}"]}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/troubleshooting-2.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/troubleshooting-2.jpg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("div",{children:Object(n.jsx)("span",{children:'Open configs/database.php and make "strict" false'})}),Object(n.jsx)("div",{children:Object(n.jsx)("span",{children:"php artisan optimize:clear"})})]})]})})]})]}),Object(n.jsx)(Z,{})]});var Ge=()=>{const[e,t]=Object(a.useState)(null);return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Troubleshooting admin"}),Object(n.jsxs)("div",{class:"accordion",id:"accordionExample",children:[Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingOne",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseOne","aria-expanded":"true","aria-controls":"collapseOne",children:"Troubleshooting 1"})}),Object(n.jsx)("div",{id:"collapseOne",class:"accordion-collapse collapse show","aria-labelledby":"headingOne","data-bs-parent":"#accordionExample",children:Object(n.jsxs)("div",{class:"accordion-body has-white-color",children:[Object(n.jsx)("p",{children:"If you see above issue in your terminal, open package.json and remove GENERATE_SOURCEMAP=false. Then try to rebuild"}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/troubleshooting-1.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/troubleshooting-1.jpg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"introduction-code",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{children:"GENERATE_SOURCEMAP=false"}),Object(n.jsx)("span",{className:1===e?"bg-success copy":"copy",onClick:()=>(async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}})("GENERATE_SOURCEMAP=false",1),children:1===e?"copied!":"copy"})]})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingThree",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseThree","aria-expanded":"true","aria-controls":"collapseThree",children:"Troubleshooting 2"})}),Object(n.jsx)("div",{id:"collapseThree",class:"accordion-collapse collapse show","aria-labelledby":"headingThree","data-bs-parent":"#accordionExample3",children:Object(n.jsxs)("div",{class:"accordion-body",children:["If your website's design has style issur, do following steps",Object(n.jsxs)("p",{children:["1. Before build, open package.json. ",Object(n.jsx)("br",{}),'2. Replace "antd": "^4.20.6", with "antd": "4.20.6". ',Object(n.jsx)("br",{}),"3. Then build and upload."]}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/antd.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/antd.jpg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingFour",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseFour","aria-expanded":"true","aria-controls":"collapseFour",children:"Troubleshooting 3"})}),Object(n.jsx)("div",{id:"collapseFour",class:"accordion-collapse collapse show","aria-labelledby":"headingFour","data-bs-parent":"#accordionExample4",children:Object(n.jsxs)("div",{class:"accordion-body",children:["If you meet this error, build app locally in your machine and upload to server. you meet this error, when you server is not strong to build the project. ",Object(n.jsx)("br",{})," ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:"FATAL ERROR: Reached heap limit Allocation failed - JavaScript heap out of memory 1: 00007FF61345168F v8:internal CodeObjectRegistry::~CodeObjectRegistry+122159 2: 00007FF6133DB456 DSA_meth_get_flags+64118 3: 00007FF6133DC4D2 DSA_meth_ get_flags+68338 4: 00007FF613D13CB4 v8::Isolate::ReportExternalAllocation LimitReached+ 116 5: 00007FF613CFE27D v8::SharedArrayBuffer::Externalize+781 6: 00007FF613BA183C v8:internal::Heap::EphemeronKeyWriteBarrierFromCode+1468 7: 00007FF613B9E954 v8::internal:: Heap::CollectGarbage+4244 8: 00007FF613B9C2D0 v8:internal:: Heap::AllocateExternalBackingStore+2000 9: 00007FF613BCOE56 v8:internal:: Factory::NewFillerObject+214 10: 00007FF6138F3565 v8.:internal::DateCache::Weekday+1797 11: 00007FF613DA1991 v8::internal::SetupisolateDelegate::SetupHeap+494417 12: 00007FF613D3689E v8::internal::SetupisolateDelegate::SetupHeap+55902 13: 00000298B2BE89B2"})})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingFive",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseFive","aria-expanded":"true","aria-controls":"collapseFive",children:"Troubleshooting 4"})}),Object(n.jsx)("div",{id:"collapseFive",class:"accordion-collapse collapse show","aria-labelledby":"headingFive","data-bs-parent":"#accordionExample4",children:Object(n.jsxs)("div",{class:"accordion-body",children:[Object(n.jsx)("h6",{className:"fw-normal",children:"If you get this error, delete the env file or set REACT_APP_IS_DEMO=false"})," ",Object(n.jsx)("br",{})," ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsxs)(Q.a,{children:[Object(n.jsx)("a",{href:"./assets/img/project/troubleshooting5-error.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/project/troubleshooting5-error.png",alt:"admin install",loading:"lazy",className:"img-responsive-full"})}),Object(n.jsx)("a",{href:"./assets/img/project/troubleshooting5.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/project/troubleshooting5.png",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})]})})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingFive",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseFive","aria-expanded":"true","aria-controls":"collapseFive",children:"Troubleshooting 5"})}),Object(n.jsx)("div",{id:"collapseFive",class:"accordion-collapse collapse show","aria-labelledby":"headingFive","data-bs-parent":"#accordionExample4",children:Object(n.jsxs)("div",{class:"accordion-body",children:[Object(n.jsxs)("h6",{className:"fw-normal",children:["If you meet this error, please, get recaptcha key using this tutorial."," ",Object(n.jsx)("strong",{children:Object(n.jsx)("a",{href:"https://contactform7.com/recaptcha-v2/",target:"_blank",children:"link"})})]})," ",Object(n.jsx)("br",{})," ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/recaptcha.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/recaptcha.jpg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})})]})})]}),Object(n.jsxs)("div",{class:"accordion-item",children:[Object(n.jsx)("h2",{class:"accordion-header",id:"headingFive",children:Object(n.jsx)("button",{class:"accordion-button",type:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseFive","aria-expanded":"true","aria-controls":"collapseFive",children:"Troubleshooting 6"})}),Object(n.jsx)("div",{id:"collapseFive",class:"accordion-collapse collapse show","aria-labelledby":"headingFive","data-bs-parent":"#accordionExample4",children:Object(n.jsxs)("div",{class:"accordion-body",children:[Object(n.jsxs)("h6",{className:"fw-normal",children:['We recommend to use yarn to manage packages. If you meet this error, use yarn or run "npm run \u2014legacy-per-deps" command.'," "]})," ",Object(n.jsx)("br",{})," ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container mb-5",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/troubleshooting-version.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/troubleshooting-version.jpg",alt:"admin install",loading:"lazy",className:"img-responsive-full"})})})})})]})})]})]}),Object(n.jsx)(Z,{})]})};var We=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"How do I update existing code with new release?"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Special Notes"}),Object(n.jsxs)("p",{children:["As per Envato policy installation, setup and configurations or modification are not included in free support. Free support is only for any bug/ error in original code. we do not provide installation and customization support in FREE SUPPORT. ",Object(n.jsx)("br",{})," Still, We are providing steps for How to update future release code to existing source code for your knowledge purpose."]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"How to update future release code to existing source code"}),Object(n.jsx)("p",{children:"If you want which file changes are on the latest updated version then you have to manage the git repository by yourself."}),Object(n.jsx)("p",{children:"Here we have provided steps on how to update existing source code."}),Object(n.jsx)("h1",{className:"title",children:"For First time: Initial project downloaded from codecanyon server. Step"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Step 1: Create or login with github"}),Object(n.jsxs)("p",{children:["Login or register your account with github:",Object(n.jsx)("a",{href:"https://github.com/login",children:"https://github.com/login"})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Step 2: Create a new project in Github"}),Object(n.jsx)("p",{children:"In your dashboard, click the green New button. This opens the New project page."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/c-repo.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/c-repo.jpg",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/c_repo-2.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/c_repo-2.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Step 3: Clone your project to your local system"}),Object(n.jsx)("p",{children:"Once project is created on your github server. You have to clone the project to your local system. You can clone it with the command line."}),Object(n.jsxs)("p",{children:[Object(n.jsx)("strong",{children:"For ex:"})," git clone https://github.com/YOURUSERNAME/your-repository-name.git"]}),Object(n.jsx)("p",{children:"Copy your project url and clone into your exiting system"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/c_repo-3.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/c_repo-3.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/clone-repo.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/clone-repo.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("p",{children:"Once successfully clone then system will create a folder on your system"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/local-repo.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/local-repo.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("h4",{className:"introduction-img-container",children:["Step 4: Download project from codecanyon server ",Object(n.jsx)("br",{}),"Step 5: Copy/paste your initial downloaded project to clone directories."]}),Object(n.jsx)("p",{children:"Once successfully downloaded project from codecanyon, copy/paste your downloaded project into clone directories"}),Object(n.jsx)("p",{children:Object(n.jsx)("strong",{children:"Note: "})}),Object(n.jsx)("p",{children:"Only orignal source code is put here."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/local-file.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/local-file.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{className:"introduction-img-container",children:"Step 6: Commit and push to github server"}),Object(n.jsx)("p",{children:"Onces copy/paste your changes to clone directres, you have to push all files to your github server. For that use the following commands."}),Object(n.jsx)("p",{children:"After That follow below steps"}),Object(n.jsx)("ul",{children:Object(n.jsx)("li",{children:"\u2013 Goto inside your clone directory project"})}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/go-repository.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/go-repository.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("ul",{children:[Object(n.jsx)("li",{children:"\u2013 Add your all files with \u201cgit add .\u201d command"}),Object(n.jsx)("li",{children:"\u2013 Now commit your changes with below command"}),Object(n.jsx)("li",{children:"git commit -m \u2018initial commit\u2019"})]}),Object(n.jsx)("p",{children:Object(n.jsx)("storng",{children:"Note:"})}),Object(n.jsx)("p",{children:"Write your latest version message instead of \u201cinitial commit\u201d."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/git-add.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/git-add.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/git-commit.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/git-commit.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("ul",{children:[Object(n.jsx)("li",{children:"\u2013 Push your changes to server with below command"}),Object(n.jsx)("li",{children:"\u201cgit push\u201d And provide you github credential details"})]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/git-push.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/git-push.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("ul",{children:Object(n.jsx)("li",{children:"\u2013 Check your all changes to github server"})}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/repo.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/repo.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h1",{className:"title mt-4",children:"For Update existing code (If Already have old version of project)"}),Object(n.jsxs)("p",{children:[Object(n.jsx)("strong",{children:"NOTE: "}),"If you remove the project from the local system then clone the project again from your github server. Follow the same above Step 3: Clone your project to your local system"]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Steps 1: Download the latest version from codecanyon server."}),Object(n.jsx)("p",{children:"Once you will received mail for updates. Just download latest code from codecanyon server."}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Steps 2: Copy/paste your initial downloaded project to clone directories."}),Object(n.jsx)("p",{children:"Once successfully downloaded project from codecanyon, copy/paste your downloaded project into clone directories"}),Object(n.jsx)("p",{children:Object(n.jsx)("strong",{children:"Note:"})}),Object(n.jsx)("p",{children:"Only orignal source code is put here."}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Steps 3: Commit and push to github server"}),Object(n.jsx)("p",{children:"Follow same Step 6: Commit and push to github server"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Steps 4: Check updated files"}),Object(n.jsx)("p",{children:"After committing your latest changes. Goto github project dashboard and click on commit link."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/commits.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/commits.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("p",{children:"Click on \u201cXX changed file\u201d to see which file has been changed."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/changed.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/changed.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("p",{children:"Connect to server and open project directory"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/root.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/root.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("p",{children:"Run \u201cgit init\u201d command to initialize git"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/init.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/init.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("p",{children:"Run following commant to connect your server with github repository. Don\u2019t forget to put your oauth key and github repository url."}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/git-remote.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/git-remote.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("p",{children:"Pull changes using following command"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/git-pull.png","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/git-pull.png",alt:"images",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"if you have any confilicts while pulling, use google to find a solution"]}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"Do above steps to update admin website, customer website and backend."]}),Object(n.jsx)(Z,{})]});var Le=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" KIOSK setup"}),Object(n.jsxs)("h4",{className:"introduction-contentTitle",children:[" ","Run an existing flutter project on IDE"," "]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Logo "}),"You can generate app icon using this website https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html ",Object(n.jsx)("br",{}),"\u2022 Then go to"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /android/app/src/main/res"}),"and replace all mipmap folder with your /android folder ",Object(n.jsx)("br",{}),"\u2022 Again go to ",Object(n.jsx)("span",{className:"introduction-step",children:"/ios/Runner"})," and replace Assets.xcassets with your generated Assets.xcassets folder ",Object(n.jsx)("br",{}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change App Name "}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["1.Change the value of label from",Object(n.jsx)("span",{className:"introduction-step",children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/android/app/src/main/AndroidManifest.xml"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{children:'android:label="My App"'}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s('android:label="My App"\u2019',2),children:2===e?"copied!":"copy"})]})]})]}),Object(n.jsxs)("div",{className:"mt-4 mb-3",children:["2.Change the value of CFBundleName from",Object(n.jsx)("span",{className:"introduction-step",children:" /iOS/Runner/info.plist"}),Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/iOS/Runner/info.plist"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{children:["<key>CFBundleName</key>"," ",Object(n.jsx)("br",{}),"<string>My App</string>"]}),Object(n.jsx)("span",{className:3===e?"bg-success copy":"copy",onClick:()=>s("<key>CFBundleName</key><string>My App</string>",3),children:3===e?"copied!":"copy"})]})]})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:" Change Base URL"}),"Please do NOT put slash ( / ) at the end of your base url. Use your admin url as base url. First you have to install your admin panel. For example: If your admin url is"," ",Object(n.jsx)("span",{className:"introduction-step",children:" https://your_domain.com/admin"}),"then base url will be https://your_domain.com. Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:" /lib/app_constants.dart"}),"and replace baseUrl variable value with your own URL.",Object(n.jsxs)("div",{className:"introduction-code",children:[Object(n.jsx)("p",{children:"/lib/src/core/constants/secret_vars.dart"}),Object(n.jsx)("hr",{}),Object(n.jsxs)("div",{children:[Object(n.jsxs)("span",{className:"ps-0",children:["static const String baseUrl=",Object(n.jsx)("span",{className:"text-black",children:"'https://your_domain.com'"})]}),Object(n.jsx)("span",{className:4===e?"bg-success copy":"copy",onClick:()=>s("baseUrl=https://your_domain.com",4),children:4===e?"copied!":"copy"})]})]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/doc/kiosk-translation2.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/doc/kiosk-translation2.jpg",alt:"image02",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Package"}),"Firstly, find out the existing package name. You can find it out from top of",Object(n.jsx)("span",{className:"introduction-step",children:"/app/src/main/AndroidManifest.xml"}),"file. Then right click on project folder from android studio and click on",Object(n.jsx)("span",{className:"introduction-step",children:"replace in path"}),"You will see a popup window with two input boxes. In first box you have to put existing package name that you saw in"," ",Object(n.jsx)("span",{className:"introduction-step",children:"AndroidManifest.xml"}),"file previously and then write down your preferred package name in second box and then click on"," ",Object(n.jsx)("span",{className:"introduction-step",children:"Replace All"})," button.",Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Setup Firebase"}),"IOS Firebase :"," ",Object(n.jsx)("a",{className:"strong",href:"https://firebase.google.com/docs/ios/setup?authuser=0",target:"_blank",children:"ios/setup"}),Object(n.jsx)("br",{}),"Android Firebase :"," ",Object(n.jsx)("a",{className:"strong",href:"https://firebase.google.com/docs/android/setup?authuser=0",target:"_blank",children:"android/setup"}),Object(n.jsx)("br",{}),Object(n.jsx)(Z,{})]})};var He=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Customization"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Translate mobile app"}),"\u2022 Translation admin panel is very easy. To translate admin panel, go to admin panel and open ",Object(n.jsx)("br",{}),Object(n.jsxs)("span",{className:"introduction-step-2",children:["Settings ",">"," Translations menu"]}),"and Translate all words into your language",Object(n.jsxs)("div",{className:"introduction-img-container",children:[Object(n.jsx)("img",{src:"./assets/img/doc/translation2.jpg",alt:"img",className:"img-responsive"}),Object(n.jsx)("img",{src:"./assets/img/doc/translation1.jpg",alt:"img",className:"img-responsive"})]}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Color"}),"\u2022 Open"," ",Object(n.jsx)("span",{className:"introduction-step",children:"/lib/src/presentation/theme/app_style.dart"}),"file and change colors as you want. ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)("img",{src:"./assets/img/doc/kiosk-color3.jpg",alt:"img",className:"img-responsive-full"})}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Change App Font"}),"\u2022 in our apps, google font package is installed. You may change app fonts easily by selecting new fonts",Object(n.jsx)(Z,{})]});var qe=()=>{const[e,t]=Object(a.useState)(null),s=async(e,s)=>{try{await navigator.clipboard.writeText(e),t(s)}catch(a){t("Failed to copy!")}};return Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"Kiosk build & release"}),Object(n.jsx)("h3",{className:"introduction-title",children:"Build for Android"}),"For debug build you can run command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk"}),Object(n.jsx)("span",{className:1===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk",1),children:1===e?"copied!":"copy"})]})}),"You will get a larger merged apk with this. But you can split them with this command:",Object(n.jsx)("div",{className:"introduction-code mb-4",children:Object(n.jsxs)("div",{children:[Object(n.jsx)("span",{className:"ps-0",children:"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi"}),Object(n.jsx)("span",{className:2===e?"bg-success copy":"copy",onClick:()=>s("flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi",2),children:2===e?"copied!":"copy"})]})}),"Build file location:",Object(n.jsx)("span",{className:"introduction-step",children:" /build/app/outputs/apk/"}),"For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/android"}),Object(n.jsx)("h4",{className:"introduction-contentTitle",children:"Build for iOS"}),"There are no general way to generate app for iOS. Apple doesn\u2019t allow to install app like this debug way. If you want to install it on your iOS device then you have to deploy it on TestFlight or AppStore. For deploying it please follow this documentation:",Object(n.jsx)("strong",{className:"strong",children:"https://docs.flutter.dev/deployment/ios"}),Object(n.jsx)(Z,{})]})};var Je=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:"QR code requirements"}),"\u2022 NodeJS v14+",Object(n.jsx)("br",{}),"\u2022 React js v18+ ",Object(n.jsx)("br",{}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("div",{className:"mt-4"}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Tools"}),"\u2022 For Frontend development: Nodejs , Visual Studio Code or WebStorm ",Object(n.jsx)("br",{}),Object(n.jsx)("h3",{className:"introduction-contentTitle",children:"Knowledge"}),"\u2022 For Frontend development: React Js ",Object(n.jsx)("br",{}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"We would like to notify that the Envato price doesn\u2019t include any kind of installation and app publishing support. We kindly ask you to follow the documentation step by step for installation, setup and other branding related changes. Please note that, we bear no responsibility for your mistake. You are fully in charge for any kind of customizations made by your own."]}),Object(n.jsxs)("div",{className:"alert alert-danger mt-4",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(ee.a,{size:22})," ",Object(n.jsx)("strong",{children:"WARNING"})]}),"Please do this very carefully. We bear no responsibility for your mistake."]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-single-documentation/pos-app-build-release",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:" Pos build & release"})]}),Object(n.jsxs)(x.b,{to:"/foodyman-single-documentation",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Introduction "})]})]})]});var Ve=()=>Object(n.jsxs)("div",{className:"introduction ","data-aos":"fade-right","data-aos-delay":"300","data-aos-duration":"1000",children:[Object(n.jsx)("h1",{className:"title",children:" Install QR code"}),Object(n.jsxs)("div",{className:"alert alert-primary mt-3",role:"alert",children:[Object(n.jsxs)("div",{className:"mb-2",children:[Object(n.jsx)(Y.a,{size:22})," ",Object(n.jsx)("strong",{children:"INFO"})]}),"Change title inside /public/index.html"]}),"\u2022 Download the code from codecayon ",Object(n.jsx)("br",{}),"\u2022 Extract the zip files",Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsxs)("p",{className:"mb-0",children:["\u2022 Create the ",Object(n.jsx)("strong",{children:"dist"})," File"]}),Object(n.jsxs)("p",{children:["In your application's root directory, run ",Object(n.jsx)("strong",{children:"yarn "}),"to install the updated dependencies. Once this has finished, the next command you'll run is ",Object(n.jsx)("strong",{children:"yarn build"})," (",Object(n.jsx)("strong",{children:"npm install"})," and ",Object(n.jsx)("strong",{children:"npm build"})," work, too)."]}),Object(n.jsxs)("p",{children:["You'll notice this creates a new directory in your project called",Object(n.jsx)("strong",{children:" dist"}),". The dist folder is essentially a super-compressed version of your program that has everything your browser needs to identify and run your app."]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/project/qrcode-build.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/project/qrcode-build.jpg",alt:"admin config",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsx)("p",{className:"mb-0",children:"\u2022 Connect to cPanel"}),Object(n.jsx)("p",{children:"Your cPanel manager should look something like this:"}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/food-doc/c-pannel.webp","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/food-doc/c-pannel.webp",alt:"admin config",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsx)("div",{className:"ptf-spacer",style:{"--ptf-xxl":"3.75rem"}}),Object(n.jsxs)("p",{className:"mb-0",children:["\u2022 Add the Build File Contents to ",Object(n.jsx)("strong",{children:"public_html"})]}),Object(n.jsxs)("p",{children:["Navigate to the dist file in your app's root directory. Open it up and select all the contents ",Object(n.jsx)("strong",{children:"inside the dist file."})," If you upload the entire dist file itself, the process will not work."]}),Object(n.jsx)("div",{className:"introduction-img-container",children:Object(n.jsx)(Q.b,{children:Object(n.jsx)(Q.a,{children:Object(n.jsx)("a",{href:"./assets/img/project/view-qr-build.jpg","data-fancybox":!0,rel:"nofollow",children:Object(n.jsx)("img",{src:"./assets/img/project/view-qr-build.jpg",alt:"admin config",loading:"lazy",className:"img-responsive-full"})})})})}),Object(n.jsxs)("p",{children:["Once you've copied all the contents inside the dist file, upload them into ",Object(n.jsx)("strong",{children:"public_html."})]}),Object(n.jsxs)("div",{className:"center-page-container",children:[Object(n.jsxs)(x.b,{to:"/foodyman-single-documentation/qr-app",className:"btn  previous",children:[Object(n.jsx)("p",{children:"Previous"}),Object(n.jsx)("p",{className:"link",children:"Requirements "})]}),Object(n.jsxs)(x.b,{to:"/foodyman-single-documentation/firebase",className:"btn  next",children:[Object(n.jsx)("p",{children:"Next"}),Object(n.jsx)("p",{className:"link",children:" Firebase setup "})]})]})]});var Qe=()=>Object(n.jsxs)(N.b,{path:"/",element:Object(n.jsx)(V,{}),children:[Object(n.jsx)(N.b,{path:"/",index:!0,element:Object(n.jsx)($,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/introduction",element:Object(n.jsx)($,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/basic",element:Object(n.jsx)(te,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/server",element:Object(n.jsx)(se,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/front",element:Object(n.jsx)(ae,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/front-qr",element:Object(n.jsx)(ce,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-web-qr",element:Object(n.jsx)(ne,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/admin",element:Object(n.jsx)(ie,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/recommendations",element:Object(n.jsx)(oe,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/local-front",element:Object(n.jsx)(re,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mobile-app",element:Object(n.jsx)(de,{pageTitle:"Customer App"})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/vendor-app",element:Object(n.jsx)(de,{pageTitle:"Vendor App"})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/driver-app",element:Object(n.jsx)(de,{pageTitle:"Driver App"})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/pos-app",element:Object(n.jsx)(de,{pageTitle:"Pos App"})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/flutter-sdk",element:Object(n.jsx)(je,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/local-server",element:Object(n.jsx)(be,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/install-on-server",element:Object(n.jsx)(pe,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup",element:Object(n.jsx)(me,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/customization",element:Object(n.jsx)(he,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-mobile",element:Object(n.jsx)(ue,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-backend",element:Object(n.jsx)(xe,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/payment-installation",element:Object(n.jsx)(Oe,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/image-settings",element:Object(n.jsx)(ge,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-vendor",element:Object(n.jsx)(fe,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-customer",element:Object(n.jsx)(ye,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-pos",element:Object(n.jsx)(ve,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-deliveryboy",element:Object(n.jsx)(Ne,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/customization-vendor",element:Object(n.jsx)(we,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/customization-deliveryboy",element:Object(n.jsx)(ke,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/customization-customer",element:Object(n.jsx)(Ae,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/customization-pos",element:Object(n.jsx)(Pe,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/customer-app-build-release",element:Object(n.jsx)(Se,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/pos-app-build-release",element:Object(n.jsx)(Fe,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/vendor-app-build-release",element:Object(n.jsx)(Te,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/deliveryboy-app-build-release",element:Object(n.jsx)(Ie,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-web",element:Object(n.jsx)(Ce,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/build-code-and-setup-on-server",element:Object(n.jsx)(_e,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/build-code-and-setup-on-server-backend",element:Object(n.jsx)(Re,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/update-admin-panel",element:Object(n.jsx)(ze,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/update-app-web",element:Object(n.jsx)(Me,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/support-plan",element:Object(n.jsx)(Be,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/firebase",element:Object(n.jsx)(Ue,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/troubleshooting-backend",element:Object(n.jsx)(Ke,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/troubleshooting-admin",element:Object(n.jsx)(Ge,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/update",element:Object(n.jsx)(We,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/kiosk-app",element:Object(n.jsx)(de,{pageTitle:"Kiosk App"})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/mandatory-setup-kiosk",element:Object(n.jsx)(Le,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/customization-kiosk",element:Object(n.jsx)(He,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/kiosk-app-build-release",element:Object(n.jsx)(qe,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/qr-app",element:Object(n.jsx)(Je,{})}),Object(n.jsx)(N.b,{path:"/foodyman-documentation/install-qrcode",element:Object(n.jsx)(Ve,{})})]});var Xe=()=>Object(n.jsx)(n.Fragment,{children:Object(n.jsxs)(x.a,{children:[Object(n.jsx)(G,{}),Object(n.jsxs)(N.d,{children:[Qe(),Object(n.jsx)(N.b,{path:"*",element:Object(n.jsx)(K,{})})]})]})});var Ze=()=>(Object(a.useEffect)((()=>{r.a.init({duration:1200})}),[]),Object(n.jsx)(h,{children:Object(n.jsxs)(n.Fragment,{children:[Object(n.jsxs)(l.a,{children:[Object(n.jsx)("title",{children:"GitHubit"}),Object(n.jsx)("meta",{name:"description",content:"Githubit"}),Object(n.jsx)("meta",{name:"keywords",content:"agency, business, clean, corporate, creative, fullpage, minimal, modern, multipurpose, parallax, personal, photography, portfolio, showcase"})]}),Object(n.jsx)(i,{}),Object(n.jsx)(u.a,{position:"top-right",autoClose:3e3,hideProgressBar:!0,closeOnClick:!0,pauseOnHover:!0,draggable:!0}),Object(n.jsx)(Xe,{})]})}));var $e=e=>{e&&e instanceof Function&&s.e(3).then(s.bind(null,68)).then((t=>{let{getCLS:s,getFID:a,getFCP:c,getLCP:n,getTTFB:i}=t;s(e),a(e),c(e),n(e),i(e)}))},et=(s(59),s(16)),tt=s(19),st=s(41);var at={auth:S};const ct={key:"auth",storage:s.n(st).a,whitelist:["user"]},nt=Object(et.b)({...at,auth:Object(tt.g)(ct,at.auth)}),it=Object(w.a)({reducer:nt,middleware:e=>e({serializableCheck:{ignoredActions:[tt.a,tt.f,tt.b,tt.c,tt.d,tt.e]}})});Object(tt.h)(it);s(62),s(63),s(64);const ot=()=>Object(n.jsx)(v.a,{store:it,children:Object(n.jsx)(Ze,{})});Object(c.render)(Object(n.jsx)(ot,{}),document.getElementById("root")),$e()}},[[65,1,2]]]);
//# sourceMappingURL=main.a9a8a195.chunk.js.map