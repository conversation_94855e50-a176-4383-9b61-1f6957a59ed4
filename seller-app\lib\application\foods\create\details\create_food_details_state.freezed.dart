// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_food_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreateFoodDetailsState {
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get tax => throw _privateConstructorUsedError;
  String get minQty => throw _privateConstructorUsedError;
  String get maxQty => throw _privateConstructorUsedError;
  String get qrcode => throw _privateConstructorUsedError;
  String get interval => throw _privateConstructorUsedError;
  bool get active => throw _privateConstructorUsedError;
  bool get isCreating => throw _privateConstructorUsedError;
  List<String> get images => throw _privateConstructorUsedError;
  List<Galleries> get listOfUrls => throw _privateConstructorUsedError;
  ProductData? get createdProduct => throw _privateConstructorUsedError;

  /// Create a copy of CreateFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateFoodDetailsStateCopyWith<CreateFoodDetailsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateFoodDetailsStateCopyWith<$Res> {
  factory $CreateFoodDetailsStateCopyWith(CreateFoodDetailsState value,
          $Res Function(CreateFoodDetailsState) then) =
      _$CreateFoodDetailsStateCopyWithImpl<$Res, CreateFoodDetailsState>;
  @useResult
  $Res call(
      {String title,
      String description,
      String tax,
      String minQty,
      String maxQty,
      String qrcode,
      String interval,
      bool active,
      bool isCreating,
      List<String> images,
      List<Galleries> listOfUrls,
      ProductData? createdProduct});
}

/// @nodoc
class _$CreateFoodDetailsStateCopyWithImpl<$Res,
        $Val extends CreateFoodDetailsState>
    implements $CreateFoodDetailsStateCopyWith<$Res> {
  _$CreateFoodDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = null,
    Object? tax = null,
    Object? minQty = null,
    Object? maxQty = null,
    Object? qrcode = null,
    Object? interval = null,
    Object? active = null,
    Object? isCreating = null,
    Object? images = null,
    Object? listOfUrls = null,
    Object? createdProduct = freezed,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as String,
      minQty: null == minQty
          ? _value.minQty
          : minQty // ignore: cast_nullable_to_non_nullable
              as String,
      maxQty: null == maxQty
          ? _value.maxQty
          : maxQty // ignore: cast_nullable_to_non_nullable
              as String,
      qrcode: null == qrcode
          ? _value.qrcode
          : qrcode // ignore: cast_nullable_to_non_nullable
              as String,
      interval: null == interval
          ? _value.interval
          : interval // ignore: cast_nullable_to_non_nullable
              as String,
      active: null == active
          ? _value.active
          : active // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreating: null == isCreating
          ? _value.isCreating
          : isCreating // ignore: cast_nullable_to_non_nullable
              as bool,
      images: null == images
          ? _value.images
          : images // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listOfUrls: null == listOfUrls
          ? _value.listOfUrls
          : listOfUrls // ignore: cast_nullable_to_non_nullable
              as List<Galleries>,
      createdProduct: freezed == createdProduct
          ? _value.createdProduct
          : createdProduct // ignore: cast_nullable_to_non_nullable
              as ProductData?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateFoodDetailsStateImplCopyWith<$Res>
    implements $CreateFoodDetailsStateCopyWith<$Res> {
  factory _$$CreateFoodDetailsStateImplCopyWith(
          _$CreateFoodDetailsStateImpl value,
          $Res Function(_$CreateFoodDetailsStateImpl) then) =
      __$$CreateFoodDetailsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String title,
      String description,
      String tax,
      String minQty,
      String maxQty,
      String qrcode,
      String interval,
      bool active,
      bool isCreating,
      List<String> images,
      List<Galleries> listOfUrls,
      ProductData? createdProduct});
}

/// @nodoc
class __$$CreateFoodDetailsStateImplCopyWithImpl<$Res>
    extends _$CreateFoodDetailsStateCopyWithImpl<$Res,
        _$CreateFoodDetailsStateImpl>
    implements _$$CreateFoodDetailsStateImplCopyWith<$Res> {
  __$$CreateFoodDetailsStateImplCopyWithImpl(
      _$CreateFoodDetailsStateImpl _value,
      $Res Function(_$CreateFoodDetailsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = null,
    Object? tax = null,
    Object? minQty = null,
    Object? maxQty = null,
    Object? qrcode = null,
    Object? interval = null,
    Object? active = null,
    Object? isCreating = null,
    Object? images = null,
    Object? listOfUrls = null,
    Object? createdProduct = freezed,
  }) {
    return _then(_$CreateFoodDetailsStateImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as String,
      minQty: null == minQty
          ? _value.minQty
          : minQty // ignore: cast_nullable_to_non_nullable
              as String,
      maxQty: null == maxQty
          ? _value.maxQty
          : maxQty // ignore: cast_nullable_to_non_nullable
              as String,
      qrcode: null == qrcode
          ? _value.qrcode
          : qrcode // ignore: cast_nullable_to_non_nullable
              as String,
      interval: null == interval
          ? _value.interval
          : interval // ignore: cast_nullable_to_non_nullable
              as String,
      active: null == active
          ? _value.active
          : active // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreating: null == isCreating
          ? _value.isCreating
          : isCreating // ignore: cast_nullable_to_non_nullable
              as bool,
      images: null == images
          ? _value._images
          : images // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listOfUrls: null == listOfUrls
          ? _value._listOfUrls
          : listOfUrls // ignore: cast_nullable_to_non_nullable
              as List<Galleries>,
      createdProduct: freezed == createdProduct
          ? _value.createdProduct
          : createdProduct // ignore: cast_nullable_to_non_nullable
              as ProductData?,
    ));
  }
}

/// @nodoc

class _$CreateFoodDetailsStateImpl extends _CreateFoodDetailsState {
  const _$CreateFoodDetailsStateImpl(
      {this.title = '',
      this.description = '',
      this.tax = '',
      this.minQty = '',
      this.maxQty = '',
      this.qrcode = '',
      this.interval = '',
      this.active = true,
      this.isCreating = false,
      final List<String> images = const [],
      final List<Galleries> listOfUrls = const [],
      this.createdProduct})
      : _images = images,
        _listOfUrls = listOfUrls,
        super._();

  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final String tax;
  @override
  @JsonKey()
  final String minQty;
  @override
  @JsonKey()
  final String maxQty;
  @override
  @JsonKey()
  final String qrcode;
  @override
  @JsonKey()
  final String interval;
  @override
  @JsonKey()
  final bool active;
  @override
  @JsonKey()
  final bool isCreating;
  final List<String> _images;
  @override
  @JsonKey()
  List<String> get images {
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_images);
  }

  final List<Galleries> _listOfUrls;
  @override
  @JsonKey()
  List<Galleries> get listOfUrls {
    if (_listOfUrls is EqualUnmodifiableListView) return _listOfUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listOfUrls);
  }

  @override
  final ProductData? createdProduct;

  @override
  String toString() {
    return 'CreateFoodDetailsState(title: $title, description: $description, tax: $tax, minQty: $minQty, maxQty: $maxQty, qrcode: $qrcode, interval: $interval, active: $active, isCreating: $isCreating, images: $images, listOfUrls: $listOfUrls, createdProduct: $createdProduct)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateFoodDetailsStateImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.tax, tax) || other.tax == tax) &&
            (identical(other.minQty, minQty) || other.minQty == minQty) &&
            (identical(other.maxQty, maxQty) || other.maxQty == maxQty) &&
            (identical(other.qrcode, qrcode) || other.qrcode == qrcode) &&
            (identical(other.interval, interval) ||
                other.interval == interval) &&
            (identical(other.active, active) || other.active == active) &&
            (identical(other.isCreating, isCreating) ||
                other.isCreating == isCreating) &&
            const DeepCollectionEquality().equals(other._images, _images) &&
            const DeepCollectionEquality()
                .equals(other._listOfUrls, _listOfUrls) &&
            (identical(other.createdProduct, createdProduct) ||
                other.createdProduct == createdProduct));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      title,
      description,
      tax,
      minQty,
      maxQty,
      qrcode,
      interval,
      active,
      isCreating,
      const DeepCollectionEquality().hash(_images),
      const DeepCollectionEquality().hash(_listOfUrls),
      createdProduct);

  /// Create a copy of CreateFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateFoodDetailsStateImplCopyWith<_$CreateFoodDetailsStateImpl>
      get copyWith => __$$CreateFoodDetailsStateImplCopyWithImpl<
          _$CreateFoodDetailsStateImpl>(this, _$identity);
}

abstract class _CreateFoodDetailsState extends CreateFoodDetailsState {
  const factory _CreateFoodDetailsState(
      {final String title,
      final String description,
      final String tax,
      final String minQty,
      final String maxQty,
      final String qrcode,
      final String interval,
      final bool active,
      final bool isCreating,
      final List<String> images,
      final List<Galleries> listOfUrls,
      final ProductData? createdProduct}) = _$CreateFoodDetailsStateImpl;
  const _CreateFoodDetailsState._() : super._();

  @override
  String get title;
  @override
  String get description;
  @override
  String get tax;
  @override
  String get minQty;
  @override
  String get maxQty;
  @override
  String get qrcode;
  @override
  String get interval;
  @override
  bool get active;
  @override
  bool get isCreating;
  @override
  List<String> get images;
  @override
  List<Galleries> get listOfUrls;
  @override
  ProductData? get createdProduct;

  /// Create a copy of CreateFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateFoodDetailsStateImplCopyWith<_$CreateFoodDetailsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
