# Brazilian "Pay on Delivery" Payment System Implementation Study

## Executive Summary

This document outlines the comprehensive implementation of a Brazilian-specific "Pay on Delivery" payment system for the multi-platform food delivery ecosystem. The system replaces the generic "cash" payment option with four Brazilian market-specific delivery payment methods, following iFood's UX patterns and Brazilian consumer expectations.

## System Overview

The implementation spans across:
- **Flutter Customer App**: Enhanced payment selection with Brazilian options
- **Driver App**: Clear payment method display with change handling
- **Seller App**: Payment information in order management
- **Admin Panel**: Payment method configuration and reporting
- **API Backend**: Enhanced order processing with delivery payment support
- **React Web App**: Consistent payment information display

---

## Phase 1: Current State Analysis ✅

### Database Schema Current State

#### Orders Table (`orders`)
- **Current Structure**: No dedicated payment method field
- **Payment Linking**: Via polymorphic relationship to transactions
- **Key Fields**: `total_price`, `delivery_fee`, `tax`, `commission_fee`
- **Delivery Types**: `pickup`, `delivery`, `dine_in`, `point`, `kiosk`

#### Payments Table (`payments`)
- **Current Tags**: `cash`, `wallet`, `stripe`, `mercado-pago`, `paypal`, etc.
- **Structure**: `id`, `tag`, `input`, `sandbox`, `active`
- **Issue**: Generic `cash` tag without delivery-specific handling

#### Transactions Table (`transactions`)
- **Polymorphic Link**: `payable_type`, `payable_id` to orders
- **Payment Reference**: `payment_sys_id` links to payments table
- **Status Options**: `progress`, `paid`, `canceled`, `rejected`, `refund`
- **Missing**: Delivery payment method specifics and change handling

#### Shop Payments Table (`shop_payments`)
- **Purpose**: Links shops to available payment methods
- **Structure**: `shop_id`, `payment_id`, `status`, `client_id`, `secret_id`
- **Current Logic**: Filters payment methods per shop

#### Shops Table (`shops`)
- **Payment Timing**: `order_payment` field (`before`/`after`)
- **Controls**: When payment is required in the flow
- **Constants**: `ORDER_PAYMENT_BEFORE`, `ORDER_PAYMENT_AFTER`

### Current Payment Flow Analysis

1. **Customer Selection**: Chooses from available shop payment methods
2. **Order Creation**: Creates order with `payment_id` reference
3. **Transaction Processing**:
   - Cash/Wallet: Immediate transaction creation
   - Online Payments: Gateway redirect
4. **Status Tracking**: Via transactions table status field

### Frontend Current Implementation

#### Flutter Customer App
- **Component**: `PaymentMethods` widget
- **Display**: Simple list based on payment `tag`
- **Selection**: Basic radio button selection
- **Missing**: Change amount input, delivery-specific options

#### Driver App
- **Display**: `transaction.paymentSystem.tag` in order details
- **Information**: Basic payment method name
- **Missing**: Change information, delivery payment instructions

#### Seller App
- **Features**: Payment info in order management
- **Display**: Transaction status and payment method
- **Integration**: Order payment provider

#### Admin Panel
- **Functionality**: Payment method configuration
- **Reporting**: Order payment analysis
- **Management**: Payment gateway settings

---

## Phase 2: Brazilian Pay on Delivery System Design ✅

### New Payment Method Structure

#### Brazilian Delivery Payment Methods
```sql
-- Replace generic 'cash' with Brazilian-specific options
'cash_delivery'     -- Dinheiro (Físico) - Traditional cash with change
'card_delivery'     -- Cartão na Máquina - Credit card via driver terminal
'pix_delivery'      -- PIX na Máquina - PIX via driver mobile terminal
'debit_delivery'    -- Cartão de Débito - Debit card via driver terminal
```

#### Payment Type Categories
- **Pay Now** (`pagar_agora`): Online payment methods (existing)
- **Pay on Delivery** (`pagar_na_entrega`): New Brazilian delivery methods

### Enhanced Database Schema Design

#### Orders Table Modifications
```sql
ALTER TABLE orders ADD COLUMN (
    payment_method ENUM('cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery', 'online') NULL,
    change_required BOOLEAN DEFAULT FALSE,
    change_amount DECIMAL(10,2) NULL,
    payment_notes TEXT NULL,
    INDEX idx_payment_method (payment_method),
    INDEX idx_change_required (change_required)
);
```

#### Enhanced Payments Table
```sql
-- Add new Brazilian delivery payment methods
INSERT INTO payments (tag, input, active) VALUES 
('cash_delivery', 1, 1),
('card_delivery', 1, 1), 
('pix_delivery', 1, 1),
('debit_delivery', 1, 1);
```

#### Transaction Enhancements
```sql
ALTER TABLE transactions ADD COLUMN (
    delivery_payment_method ENUM('cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery') NULL,
    change_given DECIMAL(10,2) NULL,
    payment_confirmed_at TIMESTAMP NULL,
    driver_notes TEXT NULL,
    INDEX idx_delivery_payment (delivery_payment_method)
);
```

#### Shop Delivery Payment Settings
```sql
CREATE TABLE shop_delivery_payment_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    shop_id BIGINT NOT NULL,
    accept_cash_delivery BOOLEAN DEFAULT TRUE,
    accept_card_delivery BOOLEAN DEFAULT TRUE,
    accept_pix_delivery BOOLEAN DEFAULT TRUE,
    accept_debit_delivery BOOLEAN DEFAULT TRUE,
    max_change_amount DECIMAL(10,2) DEFAULT 100.00,
    delivery_payment_instructions TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
    UNIQUE KEY unique_shop_settings (shop_id),
    INDEX idx_shop_delivery_settings (shop_id)
);
```

### Payment Selection User Experience

#### Customer App Enhanced Flow
1. **Primary Selection**:
   - 💳 **Pagar Agora** - Online payment methods (existing flow)
   - 🚚 **Pagar na Entrega** - Brazilian delivery options (new)

2. **Delivery Payment Options**:
   - 💳 **Cartão de Crédito (Máquina)** - Credit card via driver's terminal
   - 💳 **Cartão de Débito (Máquina)** - Debit card via driver's terminal  
   - 📱 **PIX (Máquina)** - PIX payment via driver's mobile terminal
   - 💵 **Dinheiro (Físico)** - Traditional cash payment

3. **Enhanced Cash Flow**:
   ```
   When "Dinheiro (Físico)" selected:
   ├── "Vai precisar de troco?" (Will you need change?)
   ├── If "Sim": 
   │   └── "Troco para quanto?" (Change for how much?)
   │       └── Numeric input field (R$ XX,XX)
   └── If "Não": 
       └── "Valor exato" (Exact amount) confirmation
   ```

#### Driver App Enhancement
```dart
// Enhanced payment information display
PaymentInfoCard(
  paymentMethod: order.paymentMethod,
  changeRequired: order.changeRequired,
  changeAmount: order.changeAmount,
  totalAmount: order.totalPrice,
  paymentInstructions: order.paymentNotes,
)
```

#### Payment Method Visual Indicators
- 💵 **Dinheiro**: Shows change amount if required
- 💳 **Cartão**: Shows "Máquina necessária" badge
- 📱 **PIX**: Shows "Terminal PIX necessário" badge
- 🔄 **Status**: Payment completion confirmation

---

## Phase 3: Database Schema Implementation

### Migration Scripts

#### Migration 1: Orders Table Enhancement
```php
<?php
// database/migrations/2024_XX_XX_add_delivery_payment_to_orders_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeliveryPaymentToOrdersTable extends Migration
{
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('payment_method', [
                'cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery', 'online'
            ])->nullable()->after('delivery_type');
            
            $table->boolean('change_required')->default(false)->after('payment_method');
            $table->decimal('change_amount', 10, 2)->nullable()->after('change_required');
            $table->text('payment_notes')->nullable()->after('change_amount');
            
            $table->index('payment_method');
            $table->index('change_required');
        });
    }

    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['payment_method']);
            $table->dropIndex(['change_required']);
            $table->dropColumn(['payment_method', 'change_required', 'change_amount', 'payment_notes']);
        });
    }
}
```

#### Migration 2: Brazilian Payment Methods
```php
<?php
// database/migrations/2024_XX_XX_add_brazilian_payment_methods.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddBrazilianPaymentMethods extends Migration
{
    public function up(): void
    {
        // Add new Brazilian delivery payment methods
        DB::table('payments')->insert([
            [
                'tag' => 'cash_delivery',
                'input' => 1,
                'sandbox' => 0,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'card_delivery',
                'input' => 1,
                'sandbox' => 0,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'pix_delivery',
                'input' => 1,
                'sandbox' => 0,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'debit_delivery',
                'input' => 1,
                'sandbox' => 0,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    public function down(): void
    {
        DB::table('payments')->whereIn('tag', [
            'cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery'
        ])->delete();
    }
}
```

#### Migration 3: Shop Delivery Payment Settings
```php
<?php
// database/migrations/2024_XX_XX_create_shop_delivery_payment_settings_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShopDeliveryPaymentSettingsTable extends Migration
{
    public function up(): void
    {
        Schema::create('shop_delivery_payment_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shop_id')->constrained()->cascadeOnDelete();
            $table->boolean('accept_cash_delivery')->default(true);
            $table->boolean('accept_card_delivery')->default(true);
            $table->boolean('accept_pix_delivery')->default(true);
            $table->boolean('accept_debit_delivery')->default(true);
            $table->decimal('max_change_amount', 10, 2)->default(100.00);
            $table->text('delivery_payment_instructions')->nullable();
            $table->timestamps();

            $table->unique('shop_id');
            $table->index('shop_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('shop_delivery_payment_settings');
    }
}
```

#### Migration 4: Transaction Enhancements
```php
<?php
// database/migrations/2024_XX_XX_enhance_transactions_for_delivery_payments.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EnhanceTransactionsForDeliveryPayments extends Migration
{
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->enum('delivery_payment_method', [
                'cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery'
            ])->nullable()->after('status');

            $table->decimal('change_given', 10, 2)->nullable()->after('delivery_payment_method');
            $table->timestamp('payment_confirmed_at')->nullable()->after('change_given');
            $table->text('driver_notes')->nullable()->after('payment_confirmed_at');

            $table->index('delivery_payment_method');
        });
    }

    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropIndex(['delivery_payment_method']);
            $table->dropColumn([
                'delivery_payment_method', 'change_given',
                'payment_confirmed_at', 'driver_notes'
            ]);
        });
    }
}
```

### Model Updates

#### Order Model Enhancement
```php
<?php
// app/Models/Order.php - Add to existing model

class Order extends Model
{
    // Add to existing constants
    const PAYMENT_METHOD_CASH_DELIVERY = 'cash_delivery';
    const PAYMENT_METHOD_CARD_DELIVERY = 'card_delivery';
    const PAYMENT_METHOD_PIX_DELIVERY = 'pix_delivery';
    const PAYMENT_METHOD_DEBIT_DELIVERY = 'debit_delivery';
    const PAYMENT_METHOD_ONLINE = 'online';

    const PAYMENT_METHODS = [
        self::PAYMENT_METHOD_CASH_DELIVERY,
        self::PAYMENT_METHOD_CARD_DELIVERY,
        self::PAYMENT_METHOD_PIX_DELIVERY,
        self::PAYMENT_METHOD_DEBIT_DELIVERY,
        self::PAYMENT_METHOD_ONLINE,
    ];

    const DELIVERY_PAYMENT_METHODS = [
        self::PAYMENT_METHOD_CASH_DELIVERY,
        self::PAYMENT_METHOD_CARD_DELIVERY,
        self::PAYMENT_METHOD_PIX_DELIVERY,
        self::PAYMENT_METHOD_DEBIT_DELIVERY,
    ];

    // Add to fillable array
    protected $fillable = [
        // ... existing fields
        'payment_method',
        'change_required',
        'change_amount',
        'payment_notes',
    ];

    // Add to casts array
    protected $casts = [
        // ... existing casts
        'change_required' => 'boolean',
        'change_amount' => 'decimal:2',
    ];

    // Helper methods
    public function isDeliveryPayment(): bool
    {
        return in_array($this->payment_method, self::DELIVERY_PAYMENT_METHODS);
    }

    public function requiresChange(): bool
    {
        return $this->change_required && $this->payment_method === self::PAYMENT_METHOD_CASH_DELIVERY;
    }

    public function getChangeAmountFormatted(): string
    {
        return $this->change_amount ? 'R$ ' . number_format($this->change_amount, 2, ',', '.') : '';
    }
}
```

#### ShopDeliveryPaymentSetting Model
```php
<?php
// app/Models/ShopDeliveryPaymentSetting.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShopDeliveryPaymentSetting extends Model
{
    protected $fillable = [
        'shop_id',
        'accept_cash_delivery',
        'accept_card_delivery',
        'accept_pix_delivery',
        'accept_debit_delivery',
        'max_change_amount',
        'delivery_payment_instructions',
    ];

    protected $casts = [
        'accept_cash_delivery' => 'boolean',
        'accept_card_delivery' => 'boolean',
        'accept_pix_delivery' => 'boolean',
        'accept_debit_delivery' => 'boolean',
        'max_change_amount' => 'decimal:2',
    ];

    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    public function getAcceptedPaymentMethods(): array
    {
        $methods = [];

        if ($this->accept_cash_delivery) $methods[] = 'cash_delivery';
        if ($this->accept_card_delivery) $methods[] = 'card_delivery';
        if ($this->accept_pix_delivery) $methods[] = 'pix_delivery';
        if ($this->accept_debit_delivery) $methods[] = 'debit_delivery';

        return $methods;
    }
}
```

---

## Phase 4: API Backend Updates

### Enhanced Order Creation Endpoint

#### Request Validation
```php
<?php
// app/Http/Requests/Rest/RestStoreRequest.php - Add validation rules

class RestStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            // ... existing rules
            'payment_method' => [
                'nullable',
                'string',
                Rule::in(Order::PAYMENT_METHODS)
            ],
            'change_required' => 'boolean',
            'change_amount' => [
                'nullable',
                'numeric',
                'min:0',
                'required_if:change_required,true'
            ],
            'payment_notes' => 'nullable|string|max:500',
        ];
    }

    public function messages(): array
    {
        return [
            'change_amount.required_if' => 'Change amount is required when change is needed.',
            'payment_method.in' => 'Invalid payment method selected.',
        ];
    }
}
```

#### Order Service Enhancement
```php
<?php
// app/Services/OrderService/OrderService.php - Enhance setOrderParams method

private function setOrderParams(array $data, Shop $shop): array
{
    // ... existing code

    $orderParams = [
        // ... existing parameters
        'payment_method' => data_get($data, 'payment_method'),
        'change_required' => data_get($data, 'change_required', false),
        'change_amount' => data_get($data, 'change_amount'),
        'payment_notes' => data_get($data, 'payment_notes'),
    ];

    // Validate change amount for cash delivery
    if (data_get($data, 'payment_method') === Order::PAYMENT_METHOD_CASH_DELIVERY) {
        if (data_get($data, 'change_required') && !data_get($data, 'change_amount')) {
            throw new Exception('Change amount is required for cash delivery when change is needed.');
        }
    }

    return $orderParams;
}
```

### Enhanced API Response Structure

#### Order Resource Enhancement
```php
<?php
// app/Http/Resources/OrderResource.php - Add payment fields

class OrderResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            // ... existing fields
            'payment_method' => $this->payment_method,
            'change_required' => $this->change_required,
            'change_amount' => $this->change_amount,
            'change_amount_formatted' => $this->getChangeAmountFormatted(),
            'payment_notes' => $this->payment_notes,
            'is_delivery_payment' => $this->isDeliveryPayment(),
            'requires_change' => $this->requiresChange(),

            // Enhanced transaction information
            'transaction' => new TransactionResource($this->whenLoaded('transaction')),
            'payment_instructions' => $this->getPaymentInstructions(),
        ];
    }

    private function getPaymentInstructions(): ?string
    {
        return match($this->payment_method) {
            Order::PAYMENT_METHOD_CASH_DELIVERY => $this->requiresChange()
                ? "Cliente precisa de troco para {$this->getChangeAmountFormatted()}"
                : "Cliente tem valor exato",
            Order::PAYMENT_METHOD_CARD_DELIVERY => "Pagamento no cartão via máquina do entregador",
            Order::PAYMENT_METHOD_PIX_DELIVERY => "Pagamento via PIX no terminal do entregador",
            Order::PAYMENT_METHOD_DEBIT_DELIVERY => "Pagamento no cartão de débito via máquina",
            default => null,
        };
    }
}
```

### Shop Payment Methods API

#### New Endpoint for Delivery Payment Methods
```php
<?php
// app/Http/Controllers/API/v1/Rest/ShopController.php - Add method

public function deliveryPaymentMethods(Request $request, int $shopId): JsonResponse
{
    $shop = Shop::with(['deliveryPaymentSettings'])->findOrFail($shopId);

    $settings = $shop->deliveryPaymentSettings ?? new ShopDeliveryPaymentSetting([
        'accept_cash_delivery' => true,
        'accept_card_delivery' => true,
        'accept_pix_delivery' => true,
        'accept_debit_delivery' => true,
    ]);

    $availableMethods = [];

    if ($settings->accept_cash_delivery) {
        $availableMethods[] = [
            'tag' => 'cash_delivery',
            'name' => 'Dinheiro (Físico)',
            'icon' => '💵',
            'description' => 'Pagamento em dinheiro na entrega',
            'supports_change' => true,
            'max_change_amount' => $settings->max_change_amount,
        ];
    }

    if ($settings->accept_card_delivery) {
        $availableMethods[] = [
            'tag' => 'card_delivery',
            'name' => 'Cartão na Máquina',
            'icon' => '💳',
            'description' => 'Cartão de crédito via máquina do entregador',
            'supports_change' => false,
        ];
    }

    if ($settings->accept_pix_delivery) {
        $availableMethods[] = [
            'tag' => 'pix_delivery',
            'name' => 'PIX na Máquina',
            'icon' => '📱',
            'description' => 'PIX via terminal do entregador',
            'supports_change' => false,
        ];
    }

    if ($settings->accept_debit_delivery) {
        $availableMethods[] = [
            'tag' => 'debit_delivery',
            'name' => 'Cartão de Débito',
            'icon' => '💳',
            'description' => 'Cartão de débito via máquina do entregador',
            'supports_change' => false,
        ];
    }

    return $this->successResponse('Delivery payment methods retrieved successfully', [
        'shop_id' => $shopId,
        'delivery_payment_methods' => $availableMethods,
        'instructions' => $settings->delivery_payment_instructions,
    ]);
}
```

---

## Phase 5: Flutter Customer App Implementation

### Enhanced Payment Selection UI

#### Payment Type Selection Screen
```dart
// lib/presentation/pages/order/widgets/payment_type_selector.dart

class PaymentTypeSelector extends ConsumerWidget {
  const PaymentTypeSelector({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // Pay Now Option
        PaymentTypeCard(
          title: AppHelpers.getTranslation(TrKeys.payNow),
          subtitle: AppHelpers.getTranslation(TrKeys.payNowDescription),
          icon: Icons.payment,
          onTap: () => _showOnlinePaymentMethods(context),
        ),

        16.verticalSpace,

        // Pay on Delivery Option
        PaymentTypeCard(
          title: AppHelpers.getTranslation(TrKeys.payOnDelivery),
          subtitle: AppHelpers.getTranslation(TrKeys.payOnDeliveryDescription),
          icon: Icons.local_shipping,
          onTap: () => _showDeliveryPaymentMethods(context),
        ),
      ],
    );
  }

  void _showDeliveryPaymentMethods(BuildContext context) {
    AppHelpers.showCustomModalBottomSheet(
      context: context,
      modal: const DeliveryPaymentMethods(),
      isDarkMode: false,
      isDrag: true,
      radius: 12,
    );
  }
}
```

#### Delivery Payment Methods Screen
```dart
// lib/presentation/pages/order/widgets/delivery_payment_methods.dart

class DeliveryPaymentMethods extends ConsumerStatefulWidget {
  const DeliveryPaymentMethods({Key? key}) : super(key: key);

  @override
  ConsumerState<DeliveryPaymentMethods> createState() => _DeliveryPaymentMethodsState();
}

class _DeliveryPaymentMethodsState extends ConsumerState<DeliveryPaymentMethods> {
  int selectedIndex = -1;
  bool changeRequired = false;
  double changeAmount = 0.0;
  final TextEditingController changeController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final deliveryPaymentState = ref.watch(deliveryPaymentProvider);

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Text(
            AppHelpers.getTranslation(TrKeys.selectDeliveryPayment),
            style: AppStyle.interSemi(size: 18),
          ),

          24.verticalSpace,

          // Payment Methods List
          ...deliveryPaymentState.methods.asMap().entries.map((entry) {
            final index = entry.key;
            final method = entry.value;

            return DeliveryPaymentMethodItem(
              method: method,
              isSelected: selectedIndex == index,
              onTap: () => _selectPaymentMethod(index, method),
            );
          }).toList(),

          // Change Amount Input (shown only for cash)
          if (_isCashSelected()) ...[
            24.verticalSpace,
            _buildChangeSection(),
          ],

          32.verticalSpace,

          // Confirm Button
          CustomButton(
            title: AppHelpers.getTranslation(TrKeys.confirm),
            onPressed: _isValidSelection() ? _confirmSelection : null,
          ),
        ],
      ),
    );
  }

  Widget _buildChangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppHelpers.getTranslation(TrKeys.needChange),
          style: AppStyle.interMedium(size: 16),
        ),

        16.verticalSpace,

        // Change Required Toggle
        Row(
          children: [
            Expanded(
              child: ChangeOptionButton(
                title: AppHelpers.getTranslation(TrKeys.yes),
                isSelected: changeRequired,
                onTap: () => setState(() => changeRequired = true),
              ),
            ),

            12.horizontalSpace,

            Expanded(
              child: ChangeOptionButton(
                title: AppHelpers.getTranslation(TrKeys.no),
                isSelected: !changeRequired,
                onTap: () => setState(() {
                  changeRequired = false;
                  changeAmount = 0.0;
                  changeController.clear();
                }),
              ),
            ),
          ],
        ),

        // Change Amount Input
        if (changeRequired) ...[
          16.verticalSpace,
          Text(
            AppHelpers.getTranslation(TrKeys.changeForAmount),
            style: AppStyle.interMedium(size: 14),
          ),
          8.verticalSpace,
          CustomTextField(
            controller: changeController,
            keyboardType: TextInputType.number,
            prefix: Text('R\$ ', style: AppStyle.interMedium(size: 16)),
            hint: '0,00',
            onChanged: (value) {
              changeAmount = double.tryParse(value.replaceAll(',', '.')) ?? 0.0;
            },
          ),
        ],
      ],
    );
  }

  bool _isCashSelected() {
    if (selectedIndex == -1) return false;
    final methods = ref.read(deliveryPaymentProvider).methods;
    return methods[selectedIndex].tag == 'cash_delivery';
  }

  bool _isValidSelection() {
    if (selectedIndex == -1) return false;
    if (_isCashSelected() && changeRequired && changeAmount <= 0) return false;
    return true;
  }

  void _confirmSelection() {
    final methods = ref.read(deliveryPaymentProvider).methods;
    final selectedMethod = methods[selectedIndex];

    final paymentData = DeliveryPaymentData(
      method: selectedMethod,
      changeRequired: _isCashSelected() ? changeRequired : false,
      changeAmount: _isCashSelected() ? changeAmount : null,
    );

    ref.read(orderProvider.notifier).setDeliveryPayment(paymentData);
    Navigator.pop(context);
  }
}
```

#### Payment Method Item Component
```dart
// lib/presentation/pages/order/widgets/delivery_payment_method_item.dart

class DeliveryPaymentMethodItem extends StatelessWidget {
  final DeliveryPaymentMethod method;
  final bool isSelected;
  final VoidCallback onTap;

  const DeliveryPaymentMethodItem({
    Key? key,
    required this.method,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: isSelected ? AppStyle.primary.withOpacity(0.1) : AppStyle.white,
          border: Border.all(
            color: isSelected ? AppStyle.primary : AppStyle.borderColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            // Payment Method Icon
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: AppStyle.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: Text(
                  method.icon,
                  style: TextStyle(fontSize: 24.sp),
                ),
              ),
            ),

            16.horizontalSpace,

            // Payment Method Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    method.name,
                    style: AppStyle.interSemi(
                      size: 16,
                      color: AppStyle.black,
                    ),
                  ),
                  4.verticalSpace,
                  Text(
                    method.description,
                    style: AppStyle.interRegular(
                      size: 14,
                      color: AppStyle.textGrey,
                    ),
                  ),
                ],
              ),
            ),

            // Selection Indicator
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppStyle.primary,
                size: 24.r,
              ),
          ],
        ),
      ),
    );
  }
}
```

#### State Management
```dart
// lib/application/delivery_payment/delivery_payment_provider.dart

final deliveryPaymentProvider = StateNotifierProvider<DeliveryPaymentNotifier, DeliveryPaymentState>(
  (ref) => DeliveryPaymentNotifier(),
);

class DeliveryPaymentNotifier extends StateNotifier<DeliveryPaymentState> {
  DeliveryPaymentNotifier() : super(const DeliveryPaymentState());

  Future<void> loadDeliveryPaymentMethods(int shopId) async {
    state = state.copyWith(isLoading: true);

    try {
      final response = await _repository.getDeliveryPaymentMethods(shopId);
      response.when(
        success: (data) {
          state = state.copyWith(
            isLoading: false,
            methods: data.deliveryPaymentMethods,
            instructions: data.instructions,
          );
        },
        failure: (error, status) {
          state = state.copyWith(
            isLoading: false,
            error: error,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}

// lib/application/delivery_payment/delivery_payment_state.dart
@freezed
class DeliveryPaymentState with _$DeliveryPaymentState {
  const factory DeliveryPaymentState({
    @Default(false) bool isLoading,
    @Default([]) List<DeliveryPaymentMethod> methods,
    @Default('') String instructions,
    @Default('') String error,
  }) = _DeliveryPaymentState;
}
```

---

## Phase 6: Driver App Updates

### Enhanced Order Details Display

#### Payment Information Card
```dart
// lib/presentation/pages/order_details/widgets/payment_info_card.dart

class PaymentInfoCard extends StatelessWidget {
  final OrderData order;

  const PaymentInfoCard({Key? key, required this.order}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Style.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Style.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                FlutterRemix.bank_card_2_line,
                size: 20.r,
                color: Style.primary,
              ),
              8.horizontalSpace,
              Text(
                AppHelpers.getTranslation(TrKeys.paymentMethod),
                style: Style.interSemi(size: 16),
              ),
            ],
          ),

          16.verticalSpace,

          // Payment Method Display
          _buildPaymentMethodInfo(),

          // Change Information (for cash payments)
          if (_requiresChange()) ...[
            16.verticalSpace,
            _buildChangeInfo(),
          ],

          // Payment Instructions
          if (_hasInstructions()) ...[
            16.verticalSpace,
            _buildInstructions(),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentMethodInfo() {
    final paymentMethod = order.paymentMethod ?? order.transaction?.paymentSystem?.tag;

    return Row(
      children: [
        // Payment Method Icon
        Container(
          width: 40.w,
          height: 40.h,
          decoration: BoxDecoration(
            color: _getPaymentMethodColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              _getPaymentMethodIcon(),
              style: TextStyle(fontSize: 20.sp),
            ),
          ),
        ),

        12.horizontalSpace,

        // Payment Method Details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getPaymentMethodName(),
                style: Style.interSemi(size: 14),
              ),
              4.verticalSpace,
              Text(
                _getPaymentMethodDescription(),
                style: Style.interRegular(
                  size: 12,
                  color: Style.textGrey,
                ),
              ),
            ],
          ),
        ),

        // Status Badge
        _buildStatusBadge(),
      ],
    );
  }

  Widget _buildChangeInfo() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Style.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Style.warning.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            FlutterRemix.exchange_line,
            size: 16.r,
            color: Style.warning,
          ),
          8.horizontalSpace,
          Expanded(
            child: Text(
              order.changeRequired == true
                  ? 'Cliente precisa de troco para ${AppHelpers.numberFormat(number: order.changeAmount ?? 0)}'
                  : 'Cliente tem valor exato',
              style: Style.interMedium(
                size: 12,
                color: Style.warning,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Style.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            FlutterRemix.information_line,
            size: 16.r,
            color: Style.primary,
          ),
          8.horizontalSpace,
          Expanded(
            child: Text(
              _getPaymentInstructions(),
              style: Style.interRegular(
                size: 12,
                color: Style.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    final status = order.transaction?.status ?? 'pending';
    final color = _getStatusColor(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        AppHelpers.getTranslation(_getStatusText(status)),
        style: Style.interMedium(
          size: 10,
          color: color,
        ),
      ),
    );
  }

  // Helper methods
  bool _requiresChange() {
    return order.paymentMethod == 'cash_delivery' && order.changeRequired == true;
  }

  bool _hasInstructions() {
    return _getPaymentInstructions().isNotEmpty;
  }

  String _getPaymentMethodIcon() {
    return switch (order.paymentMethod ?? order.transaction?.paymentSystem?.tag) {
      'cash_delivery' => '💵',
      'card_delivery' => '💳',
      'pix_delivery' => '📱',
      'debit_delivery' => '💳',
      _ => '💳',
    };
  }

  String _getPaymentMethodName() {
    return switch (order.paymentMethod ?? order.transaction?.paymentSystem?.tag) {
      'cash_delivery' => AppHelpers.getTranslation(TrKeys.cashDelivery),
      'card_delivery' => AppHelpers.getTranslation(TrKeys.cardDelivery),
      'pix_delivery' => AppHelpers.getTranslation(TrKeys.pixDelivery),
      'debit_delivery' => AppHelpers.getTranslation(TrKeys.debitDelivery),
      _ => AppHelpers.getTranslation(TrKeys.unknown),
    };
  }

  String _getPaymentMethodDescription() {
    return switch (order.paymentMethod ?? order.transaction?.paymentSystem?.tag) {
      'cash_delivery' => 'Pagamento em dinheiro',
      'card_delivery' => 'Cartão via máquina',
      'pix_delivery' => 'PIX via terminal',
      'debit_delivery' => 'Débito via máquina',
      _ => 'Método de pagamento',
    };
  }

  String _getPaymentInstructions() {
    return switch (order.paymentMethod ?? order.transaction?.paymentSystem?.tag) {
      'cash_delivery' => order.changeRequired == true
          ? 'Levar troco para ${AppHelpers.numberFormat(number: order.changeAmount ?? 0)}'
          : 'Cliente tem valor exato',
      'card_delivery' => 'Levar máquina de cartão para pagamento',
      'pix_delivery' => 'Usar terminal PIX para receber pagamento',
      'debit_delivery' => 'Levar máquina para cartão de débito',
      _ => '',
    };
  }

  Color _getPaymentMethodColor() {
    return switch (order.paymentMethod ?? order.transaction?.paymentSystem?.tag) {
      'cash_delivery' => Style.success,
      'card_delivery' => Style.primary,
      'pix_delivery' => Style.warning,
      'debit_delivery' => Style.primary,
      _ => Style.textGrey,
    };
  }

  Color _getStatusColor(String status) {
    return switch (status) {
      'paid' => Style.success,
      'progress' => Style.warning,
      'canceled' => Style.red,
      'rejected' => Style.red,
      _ => Style.textGrey,
    };
  }

  String _getStatusText(String status) {
    return switch (status) {
      'paid' => TrKeys.paid,
      'progress' => TrKeys.pending,
      'canceled' => TrKeys.canceled,
      'rejected' => TrKeys.rejected,
      _ => TrKeys.unknown,
    };
  }
}
```

---

## Phase 7: Seller App Integration

### Order Management Enhancement

#### Order List Item Enhancement
```dart
// lib/presentation/pages/orders/widgets/order_item.dart - Add payment info

class OrderItem extends StatelessWidget {
  final OrderData order;

  const OrderItem({Key? key, required this.order}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // ... existing container setup
      child: Column(
        children: [
          // ... existing order info

          // Payment Information Row
          _buildPaymentInfo(),

          // ... rest of existing content
        ],
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          // Payment Method Icon
          Container(
            width: 24.w,
            height: 24.h,
            decoration: BoxDecoration(
              color: _getPaymentMethodColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Center(
              child: Text(
                _getPaymentMethodIcon(),
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),

          8.horizontalSpace,

          // Payment Method Name
          Text(
            _getPaymentMethodName(),
            style: AppStyle.interMedium(size: 12),
          ),

          // Change Info (if applicable)
          if (_showChangeInfo()) ...[
            8.horizontalSpace,
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: AppStyle.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                'Troco: ${AppHelpers.numberFormat(number: order.changeAmount ?? 0)}',
                style: AppStyle.interMedium(
                  size: 10,
                  color: AppStyle.warning,
                ),
              ),
            ),
          ],

          const Spacer(),

          // Payment Status
          _buildPaymentStatus(),
        ],
      ),
    );
  }

  Widget _buildPaymentStatus() {
    final status = order.transaction?.status ?? 'pending';
    final color = _getStatusColor(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        AppHelpers.getTranslation(_getStatusText(status)),
        style: AppStyle.interMedium(
          size: 10,
          color: color,
        ),
      ),
    );
  }

  // Helper methods similar to driver app
  bool _showChangeInfo() {
    return order.paymentMethod == 'cash_delivery' &&
           order.changeRequired == true &&
           order.changeAmount != null;
  }

  String _getPaymentMethodIcon() {
    return switch (order.paymentMethod ?? order.transaction?.paymentSystem?.tag) {
      'cash_delivery' => '💵',
      'card_delivery' => '💳',
      'pix_delivery' => '📱',
      'debit_delivery' => '💳',
      _ => '💳',
    };
  }

  String _getPaymentMethodName() {
    return switch (order.paymentMethod ?? order.transaction?.paymentSystem?.tag) {
      'cash_delivery' => 'Dinheiro',
      'card_delivery' => 'Cartão',
      'pix_delivery' => 'PIX',
      'debit_delivery' => 'Débito',
      _ => 'Online',
    };
  }

  Color _getPaymentMethodColor() {
    return switch (order.paymentMethod ?? order.transaction?.paymentSystem?.tag) {
      'cash_delivery' => AppStyle.success,
      'card_delivery' => AppStyle.primary,
      'pix_delivery' => AppStyle.warning,
      'debit_delivery' => AppStyle.primary,
      _ => AppStyle.textGrey,
    };
  }

  Color _getStatusColor(String status) {
    return switch (status) {
      'paid' => AppStyle.success,
      'progress' => AppStyle.warning,
      'canceled' => AppStyle.red,
      'rejected' => AppStyle.red,
      _ => AppStyle.textGrey,
    };
  }

  String _getStatusText(String status) {
    return switch (status) {
      'paid' => TrKeys.paid,
      'progress' => TrKeys.pending,
      'canceled' => TrKeys.canceled,
      'rejected' => TrKeys.rejected,
      _ => TrKeys.unknown,
    };
  }
}
```

---

## Phase 8: Admin Panel Enhancement

### Order Management Updates

#### Order Details Component Enhancement
```javascript
// src/views/order/order-details.js - Enhanced payment information display

const OrderDetails = () => {
  // ... existing code

  const paymentTableColumns = [
    {
      title: t('id'),
      dataIndex: 'id',
      key: 'id',
      render: (id) => `#${id}`,
    },
    {
      title: t('payment.method'),
      dataIndex: 'payment_system',
      key: 'payment_method',
      render: (paymentSystem, record) => (
        <div className="payment-method-cell">
          <span className="payment-icon">
            {getPaymentMethodIcon(record.delivery_payment_method || paymentSystem?.tag)}
          </span>
          <div className="payment-info">
            <div className="payment-name">
              {getPaymentMethodName(record.delivery_payment_method || paymentSystem?.tag)}
            </div>
            {record.delivery_payment_method === 'cash_delivery' && record.change_given && (
              <div className="change-info">
                <Tag color="orange" size="small">
                  Troco: R$ {record.change_given.toFixed(2)}
                </Tag>
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: t('amount'),
      dataIndex: 'price',
      key: 'price',
      render: (price) => numberToPrice(price),
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {t(status)}
        </Tag>
      ),
    },
    {
      title: t('payment.confirmed'),
      dataIndex: 'payment_confirmed_at',
      key: 'payment_confirmed_at',
      render: (confirmedAt) => (
        confirmedAt ? moment(confirmedAt).format('DD/MM/YYYY HH:mm') : '-'
      ),
    },
    {
      title: t('driver.notes'),
      dataIndex: 'driver_notes',
      key: 'driver_notes',
      render: (notes) => notes || '-',
    },
  ];

  const getPaymentMethodIcon = (method) => {
    const icons = {
      'cash_delivery': '💵',
      'card_delivery': '💳',
      'pix_delivery': '📱',
      'debit_delivery': '💳',
      'cash': '💵',
      'wallet': '👛',
      'stripe': '💳',
      'mercado-pago': '💳',
    };
    return icons[method] || '💳';
  };

  const getPaymentMethodName = (method) => {
    const names = {
      'cash_delivery': 'Dinheiro (Entrega)',
      'card_delivery': 'Cartão (Entrega)',
      'pix_delivery': 'PIX (Entrega)',
      'debit_delivery': 'Débito (Entrega)',
      'cash': 'Dinheiro',
      'wallet': 'Carteira',
      'stripe': 'Stripe',
      'mercado-pago': 'Mercado Pago',
    };
    return names[method] || method;
  };

  const getStatusColor = (status) => {
    const colors = {
      'paid': 'green',
      'progress': 'orange',
      'canceled': 'red',
      'rejected': 'red',
    };
    return colors[status] || 'default';
  };

  // ... rest of component
};
```

#### Order List Enhancement
```javascript
// src/views/order/orders.js - Add payment method filter and display

const Orders = () => {
  // ... existing state

  const [paymentMethodFilter, setPaymentMethodFilter] = useState('');

  const columns = [
    // ... existing columns
    {
      title: t('payment.method'),
      dataIndex: 'transaction',
      key: 'payment_method',
      render: (transaction, record) => {
        const paymentMethod = record.payment_method || transaction?.payment_system?.tag;
        return (
          <div className="payment-method-display">
            <span className="payment-icon">
              {getPaymentMethodIcon(paymentMethod)}
            </span>
            <div className="payment-details">
              <div className="payment-name">
                {getPaymentMethodName(paymentMethod)}
              </div>
              {record.payment_method === 'cash_delivery' && record.change_required && (
                <div className="change-indicator">
                  <Tag color="orange" size="small">
                    Troco: R$ {record.change_amount?.toFixed(2) || '0,00'}
                  </Tag>
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    // ... rest of columns
  ];

  const paymentMethodOptions = [
    { label: t('all'), value: '' },
    { label: 'Dinheiro (Entrega)', value: 'cash_delivery' },
    { label: 'Cartão (Entrega)', value: 'card_delivery' },
    { label: 'PIX (Entrega)', value: 'pix_delivery' },
    { label: 'Débito (Entrega)', value: 'debit_delivery' },
    { label: 'Online', value: 'online' },
  ];

  // ... rest of component with enhanced filtering
};
```

---

## Phase 9: React Web App Updates

### Order Display Enhancement

#### Order Information Component
```typescript
// components/orderInfo/orderInfo.tsx - Enhanced payment display

interface OrderInfoProps {
  data: Order;
}

const OrderInfo: React.FC<OrderInfoProps> = ({ data }) => {
  const { t } = useTranslation();

  const getPaymentMethodDisplay = () => {
    const paymentMethod = data.payment_method || data.transaction?.payment_system?.tag;

    const methodInfo = {
      'cash_delivery': {
        icon: '💵',
        name: 'Dinheiro (Entrega)',
        description: 'Pagamento em dinheiro na entrega',
      },
      'card_delivery': {
        icon: '💳',
        name: 'Cartão (Entrega)',
        description: 'Cartão via máquina do entregador',
      },
      'pix_delivery': {
        icon: '📱',
        name: 'PIX (Entrega)',
        description: 'PIX via terminal do entregador',
      },
      'debit_delivery': {
        icon: '💳',
        name: 'Débito (Entrega)',
        description: 'Cartão de débito via máquina',
      },
    };

    return methodInfo[paymentMethod] || {
      icon: '💳',
      name: paymentMethod || 'Online',
      description: 'Pagamento online',
    };
  };

  const paymentInfo = getPaymentMethodDisplay();

  return (
    <div className={cls.container}>
      {/* ... existing order info */}

      {/* Enhanced Payment Information */}
      <div className={cls.paymentSection}>
        <h6 className={cls.sectionTitle}>{t("payment.information")}</h6>

        <div className={cls.paymentMethod}>
          <span className={cls.paymentIcon}>{paymentInfo.icon}</span>
          <div className={cls.paymentDetails}>
            <div className={cls.paymentName}>{paymentInfo.name}</div>
            <div className={cls.paymentDescription}>{paymentInfo.description}</div>
          </div>
        </div>

        {/* Change Information for Cash Delivery */}
        {data.payment_method === 'cash_delivery' && data.change_required && (
          <div className={cls.changeInfo}>
            <div className={cls.changeLabel}>{t("change.required")}</div>
            <div className={cls.changeAmount}>
              <Price number={data.change_amount} symbol={data.currency?.symbol} />
            </div>
          </div>
        )}

        {/* Payment Status */}
        <div className={cls.paymentStatus}>
          <label>{t("payment.status")}</label>
          <h6 className={cls.text} style={{ textTransform: "capitalize" }}>
            {t(data.transaction?.status || 'pending')}
          </h6>
        </div>

        {/* Payment Instructions */}
        {data.payment_notes && (
          <div className={cls.paymentNotes}>
            <label>{t("payment.notes")}</label>
            <p className={cls.notesText}>{data.payment_notes}</p>
          </div>
        )}
      </div>

      {/* ... rest of existing content */}
    </div>
  );
};
```

---

## Phase 10: Testing and Validation

### Comprehensive Testing Strategy

#### Database Testing
```sql
-- Test data insertion for new payment methods
INSERT INTO orders (
    user_id, shop_id, total_price, payment_method,
    change_required, change_amount, payment_notes
) VALUES (
    1, 501, 35.50, 'cash_delivery',
    true, 50.00, 'Cliente pediu troco para R$ 50'
);

-- Test shop delivery payment settings
INSERT INTO shop_delivery_payment_settings (
    shop_id, accept_cash_delivery, accept_card_delivery,
    accept_pix_delivery, max_change_amount
) VALUES (
    501, true, true, true, 100.00
);

-- Verify data integrity
SELECT
    o.id,
    o.payment_method,
    o.change_required,
    o.change_amount,
    t.delivery_payment_method,
    t.status
FROM orders o
LEFT JOIN transactions t ON t.payable_id = o.id AND t.payable_type = 'App\\Models\\Order'
WHERE o.payment_method IN ('cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery');
```

#### API Testing
```javascript
// Test order creation with delivery payment
const testOrderCreation = async () => {
  const orderData = {
    shop_id: 501,
    delivery_type: 'delivery',
    payment_method: 'cash_delivery',
    change_required: true,
    change_amount: 50.00,
    payment_notes: 'Cliente precisa de troco',
    products: [
      { stock_id: 1, quantity: 2 }
    ]
  };

  const response = await fetch('/api/v1/rest/orders', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(orderData)
  });

  const result = await response.json();
  console.log('Order created:', result);
};

// Test delivery payment methods endpoint
const testDeliveryPaymentMethods = async (shopId) => {
  const response = await fetch(`/api/v1/rest/shops/${shopId}/delivery-payment-methods`);
  const result = await response.json();
  console.log('Available methods:', result);
};
```

#### Frontend Testing Checklist

##### Flutter Customer App
- [ ] Payment type selection (Pay Now vs Pay on Delivery)
- [ ] Delivery payment method selection
- [ ] Cash payment change amount input
- [ ] Form validation for change amount
- [ ] Order creation with delivery payment data
- [ ] Error handling for invalid payment selections

##### Driver App
- [ ] Payment method display in order details
- [ ] Change amount visibility for cash orders
- [ ] Payment instructions display
- [ ] Payment status indicators
- [ ] Order list payment method icons

##### Seller App
- [ ] Payment method display in order management
- [ ] Change information visibility
- [ ] Payment status tracking
- [ ] Order filtering by payment method

##### Admin Panel
- [ ] Enhanced payment method reporting
- [ ] Order details payment information
- [ ] Payment method configuration
- [ ] Change amount tracking in analytics

#### End-to-End Testing Scenarios

1. **Cash Delivery with Change**:
   - Customer selects "Pagar na Entrega" → "Dinheiro (Físico)"
   - Indicates need for change and enters amount
   - Order is created with change information
   - Driver sees change requirement in order details
   - Seller sees payment method and change amount

2. **Card Delivery**:
   - Customer selects "Cartão na Máquina"
   - Order is created with card delivery method
   - Driver sees instruction to bring card terminal
   - Payment is processed at delivery

3. **PIX Delivery**:
   - Customer selects "PIX na Máquina"
   - Order shows PIX delivery method
   - Driver receives PIX terminal instruction
   - Payment completed via driver's PIX terminal

### Performance Testing

#### Database Performance
```sql
-- Index performance testing
EXPLAIN SELECT * FROM orders
WHERE payment_method = 'cash_delivery'
AND change_required = true;

-- Query optimization for order lists
EXPLAIN SELECT
    o.id, o.payment_method, o.change_required, o.change_amount,
    t.status as payment_status
FROM orders o
LEFT JOIN transactions t ON t.payable_id = o.id
WHERE o.created_at >= '2024-01-01'
ORDER BY o.created_at DESC
LIMIT 50;
```

#### API Performance Testing
- Load testing for order creation with new payment fields
- Response time testing for delivery payment methods endpoint
- Concurrent user testing for payment method selection

### Security Testing

#### Input Validation
- SQL injection testing on new payment fields
- XSS testing on payment notes field
- Change amount validation (positive numbers only)
- Payment method enum validation

#### Data Integrity
- Ensure change amount is only set for cash delivery
- Validate payment method against shop settings
- Verify transaction consistency with order payment method

---

## Implementation Timeline

### Phase 1-2: Analysis & Design (Week 1)
- ✅ Current system analysis
- ✅ Brazilian payment system design
- ✅ Database schema design
- ✅ API specification design

### Phase 3: Database Implementation (Week 2)
- [ ] Create and test migration scripts
- [ ] Update models and relationships
- [ ] Implement data validation
- [ ] Test database performance

### Phase 4: API Backend (Week 3)
- [ ] Enhance order creation endpoints
- [ ] Implement delivery payment methods API
- [ ] Update order response structure
- [ ] Add payment validation logic

### Phase 5-6: Mobile Apps (Week 4-5)
- [ ] Flutter customer app payment selection
- [ ] Driver app payment information display
- [ ] Seller app order management updates
- [ ] State management implementation

### Phase 7-8: Web Applications (Week 6)
- [ ] Admin panel enhancements
- [ ] React web app updates
- [ ] Payment method configuration
- [ ] Reporting and analytics

### Phase 9: Testing & Validation (Week 7)
- [ ] Unit testing all components
- [ ] Integration testing across platforms
- [ ] End-to-end testing scenarios
- [ ] Performance and security testing

### Phase 10: Deployment & Monitoring (Week 8)
- [ ] Production deployment
- [ ] Monitoring setup
- [ ] User acceptance testing
- [ ] Bug fixes and optimizations

---

## Risk Assessment & Mitigation

### Technical Risks

1. **Database Migration Risk**
   - **Risk**: Data loss during migration
   - **Mitigation**: Comprehensive backup strategy, rollback procedures

2. **API Compatibility Risk**
   - **Risk**: Breaking existing integrations
   - **Mitigation**: Maintain backward compatibility, versioned APIs

3. **Mobile App Performance Risk**
   - **Risk**: UI lag with new payment selection
   - **Mitigation**: Optimize state management, lazy loading

### Business Risks

1. **User Adoption Risk**
   - **Risk**: Users confused by new payment options
   - **Mitigation**: Clear UI/UX, user education, gradual rollout

2. **Driver Workflow Risk**
   - **Risk**: Drivers unprepared for new payment methods
   - **Mitigation**: Driver training, clear instructions in app

3. **Operational Risk**
   - **Risk**: Increased support requests
   - **Mitigation**: Comprehensive documentation, FAQ updates

### Mitigation Strategies

1. **Feature Flags**: Gradual rollout with ability to disable
2. **A/B Testing**: Test with subset of users first
3. **Monitoring**: Real-time tracking of payment method usage
4. **Support Training**: Prepare support team for new features
5. **Rollback Plan**: Quick reversion to previous system if needed

---

## Success Metrics

### Technical Metrics
- [ ] Zero data loss during migration
- [ ] API response time < 200ms for payment methods
- [ ] Mobile app payment selection < 3 seconds
- [ ] 99.9% uptime during rollout

### Business Metrics
- [ ] 80% adoption of delivery payment methods within 30 days
- [ ] Reduced payment-related support tickets by 50%
- [ ] Improved driver satisfaction scores
- [ ] Increased order completion rates

### User Experience Metrics
- [ ] Payment selection completion rate > 95%
- [ ] User satisfaction score > 4.5/5
- [ ] Reduced payment selection time by 40%
- [ ] Zero critical payment-related bugs

---

## Conclusion

This comprehensive implementation study provides a complete roadmap for implementing a Brazilian-specific "Pay on Delivery" payment system. The solution addresses the unique needs of the Brazilian market while maintaining system integrity and user experience across all platforms.

The phased approach ensures minimal disruption to existing operations while providing a robust foundation for future payment method enhancements. The emphasis on testing, monitoring, and gradual rollout mitigates risks and ensures successful adoption.

Key benefits of this implementation:
- **Market Alignment**: Follows Brazilian UX patterns and consumer expectations
- **Operational Clarity**: Clear payment instructions for all stakeholders
- **System Integrity**: Maintains data consistency and backward compatibility
- **Scalability**: Foundation for future payment method additions
- **User Experience**: Intuitive and familiar payment selection process
```
