// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_addon_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreateAddonState {
  bool get isLoading => throw _privateConstructorUsedError;

  /// Create a copy of CreateAddonState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateAddonStateCopyWith<CreateAddonState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateAddonStateCopyWith<$Res> {
  factory $CreateAddonStateCopyWith(
          CreateAddonState value, $Res Function(CreateAddonState) then) =
      _$CreateAddonStateCopyWithImpl<$Res, CreateAddonState>;
  @useResult
  $Res call({bool isLoading});
}

/// @nodoc
class _$CreateAddonStateCopyWithImpl<$Res, $Val extends CreateAddonState>
    implements $CreateAddonStateCopyWith<$Res> {
  _$CreateAddonStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateAddonState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateAddonStateImplCopyWith<$Res>
    implements $CreateAddonStateCopyWith<$Res> {
  factory _$$CreateAddonStateImplCopyWith(_$CreateAddonStateImpl value,
          $Res Function(_$CreateAddonStateImpl) then) =
      __$$CreateAddonStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isLoading});
}

/// @nodoc
class __$$CreateAddonStateImplCopyWithImpl<$Res>
    extends _$CreateAddonStateCopyWithImpl<$Res, _$CreateAddonStateImpl>
    implements _$$CreateAddonStateImplCopyWith<$Res> {
  __$$CreateAddonStateImplCopyWithImpl(_$CreateAddonStateImpl _value,
      $Res Function(_$CreateAddonStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateAddonState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
  }) {
    return _then(_$CreateAddonStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CreateAddonStateImpl extends _CreateAddonState {
  const _$CreateAddonStateImpl({this.isLoading = false}) : super._();

  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'CreateAddonState(isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateAddonStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading);

  /// Create a copy of CreateAddonState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateAddonStateImplCopyWith<_$CreateAddonStateImpl> get copyWith =>
      __$$CreateAddonStateImplCopyWithImpl<_$CreateAddonStateImpl>(
          this, _$identity);
}

abstract class _CreateAddonState extends CreateAddonState {
  const factory _CreateAddonState({final bool isLoading}) =
      _$CreateAddonStateImpl;
  const _CreateAddonState._() : super._();

  @override
  bool get isLoading;

  /// Create a copy of CreateAddonState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateAddonStateImplCopyWith<_$CreateAddonStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
