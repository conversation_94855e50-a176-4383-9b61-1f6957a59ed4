// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'delete_extras_item_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeleteExtrasItemState {
  bool get isLoading => throw _privateConstructorUsedError;

  /// Create a copy of DeleteExtrasItemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeleteExtrasItemStateCopyWith<DeleteExtrasItemState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeleteExtrasItemStateCopyWith<$Res> {
  factory $DeleteExtrasItemStateCopyWith(DeleteExtrasItemState value,
          $Res Function(DeleteExtrasItemState) then) =
      _$DeleteExtrasItemStateCopyWithImpl<$Res, DeleteExtrasItemState>;
  @useResult
  $Res call({bool isLoading});
}

/// @nodoc
class _$DeleteExtrasItemStateCopyWithImpl<$Res,
        $Val extends DeleteExtrasItemState>
    implements $DeleteExtrasItemStateCopyWith<$Res> {
  _$DeleteExtrasItemStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeleteExtrasItemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeleteExtrasItemStateImplCopyWith<$Res>
    implements $DeleteExtrasItemStateCopyWith<$Res> {
  factory _$$DeleteExtrasItemStateImplCopyWith(
          _$DeleteExtrasItemStateImpl value,
          $Res Function(_$DeleteExtrasItemStateImpl) then) =
      __$$DeleteExtrasItemStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isLoading});
}

/// @nodoc
class __$$DeleteExtrasItemStateImplCopyWithImpl<$Res>
    extends _$DeleteExtrasItemStateCopyWithImpl<$Res,
        _$DeleteExtrasItemStateImpl>
    implements _$$DeleteExtrasItemStateImplCopyWith<$Res> {
  __$$DeleteExtrasItemStateImplCopyWithImpl(_$DeleteExtrasItemStateImpl _value,
      $Res Function(_$DeleteExtrasItemStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeleteExtrasItemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
  }) {
    return _then(_$DeleteExtrasItemStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$DeleteExtrasItemStateImpl extends _DeleteExtrasItemState {
  const _$DeleteExtrasItemStateImpl({this.isLoading = false}) : super._();

  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'DeleteExtrasItemState(isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteExtrasItemStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading);

  /// Create a copy of DeleteExtrasItemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteExtrasItemStateImplCopyWith<_$DeleteExtrasItemStateImpl>
      get copyWith => __$$DeleteExtrasItemStateImplCopyWithImpl<
          _$DeleteExtrasItemStateImpl>(this, _$identity);
}

abstract class _DeleteExtrasItemState extends DeleteExtrasItemState {
  const factory _DeleteExtrasItemState({final bool isLoading}) =
      _$DeleteExtrasItemStateImpl;
  const _DeleteExtrasItemState._() : super._();

  @override
  bool get isLoading;

  /// Create a copy of DeleteExtrasItemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteExtrasItemStateImplCopyWith<_$DeleteExtrasItemStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
