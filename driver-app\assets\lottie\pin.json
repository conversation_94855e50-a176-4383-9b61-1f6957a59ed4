{"ip": 0, "fr": 60, "v": "5.1.20", "assets": [], "layers": [{"ty": 4, "nm": "ic_green", "ip": 0, "st": 0, "ind": 6, "hix": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [0], "e": [0], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 28, "s": [0], "e": [100], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 29, "s": [100], "e": [100], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 108, "s": [100], "e": [0], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 109, "s": [0], "e": [0], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110}]}, "or": {"a": 0, "k": [0, 0, 0]}, "a": {"a": 0, "k": [5.5, 5.5, 0]}, "p": {"s": true, "x": {"a": 1, "k": [{"t": 0, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 28, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 29, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 108, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110}]}, "y": {"a": 1, "k": [{"t": 0, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 28, "s": [150], "e": [134], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 29, "s": [134], "e": [134], "i": {"x": [0.745], "y": [0.715]}, "o": {"x": [0.47], "y": [0]}}, {"t": 40, "s": [134], "e": [152], "i": {"x": [0.745], "y": [0.715]}, "o": {"x": [0.47], "y": [0]}}, {"t": 75, "s": [152], "e": [134], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 108, "s": [134], "e": [150], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 109, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110}]}}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100, 100]}}, "shapes": [{"ty": "gr", "nm": "ic_green shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [5, 5]}, "s": {"a": 0, "k": [10, 10]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 100}, "r": 2, "c": {"a": 0, "k": [0.5137254901960784, 0.9176470588235294, 0, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0.5, 0.5]}, "r": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "ic_green shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [5, 5]}, "s": {"a": 0, "k": [10, 10]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}], "op": 180}, {"ty": 4, "nm": "ic_mask", "ip": 0, "st": 0, "ind": 5, "hix": 5, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [100], "e": [100], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1, "s": [100], "e": [0], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 10, "s": [0], "e": [0], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 28, "s": [0], "e": [100], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 29, "s": [100], "e": [100], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 108, "s": [100], "e": [0], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 109, "s": [0], "e": [0], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110, "s": [0], "e": [0], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 135, "s": [0], "e": [0], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 140, "s": [0], "e": [100], "i": {"x": [0.745], "y": [0.715]}, "o": {"x": [0.47], "y": [0]}}, {"t": 160, "s": [100], "e": [0], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 180}]}, "or": {"a": 0, "k": [0, 0, 0]}, "a": {"a": 0, "k": [8, 8, 0]}, "p": {"s": true, "x": {"a": 1, "k": [{"t": 0, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 28, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 29, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110}]}, "y": {"a": 1, "k": [{"t": 0, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1, "s": [150], "e": [160], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 10, "s": [160], "e": [160], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 28, "s": [160], "e": [134], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 29, "s": [134], "e": [150], "i": {"x": [0.745], "y": [0.715]}, "o": {"x": [0.47], "y": [0]}}, {"t": 68, "s": [150], "e": [134], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 108, "s": [134], "e": [134], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110, "s": [134], "e": [134], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 135, "s": [134], "e": [150], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 136}]}}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100, 100]}}, "shapes": [{"ty": "gr", "nm": "ic_mask shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [151, 140]}, "s": {"a": 0, "k": [15, 15]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 100}, "r": 2, "c": {"a": 0, "k": [0.16862745098039217, 0.16862745098039217, 0.16862745098039217, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [-143, -132]}, "r": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "ic_mask shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [151, 140]}, "s": {"a": 0, "k": [15, 15]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}], "op": 180}, {"ty": 4, "nm": "ic_pin_main", "ip": 0, "st": 0, "ind": 4, "hix": 3, "ks": {"o": {"a": 0, "k": 100}, "or": {"a": 0, "k": [0, 0, 0]}, "a": {"a": 0, "k": [22.5, 22.5, 0]}, "p": {"s": true, "x": {"a": 1, "k": [{"t": 0, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 29, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 68, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 108, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 135}]}, "y": {"a": 1, "k": [{"t": 0, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1, "s": [150], "e": [158], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 8, "s": [158], "e": [134], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 21, "s": [134], "e": [134], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 29, "s": [134], "e": [150], "i": {"x": [0.745], "y": [0.715]}, "o": {"x": [0.47], "y": [0]}}, {"t": 68, "s": [150], "e": [134], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 108, "s": [134], "e": [134], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110, "s": [134], "e": [158], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 123, "s": [158], "e": [150], "i": {"x": [0.565], "y": [1]}, "o": {"x": [0.39], "y": [0.575]}}, {"t": 130, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 135}]}}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100, 100]}}, "shapes": [{"ty": "gr", "nm": "ic_pin_main shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [22, 22]}, "s": {"a": 0, "k": [10, 10]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 100}, "r": 2, "c": {"a": 0, "k": [0.5137254901960784, 0.9176470588235294, 0, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "ic_pin_main shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [22, 22]}, "s": {"a": 0, "k": [10, 10]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "ic_pin_main shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [22, 22]}, "s": {"a": 0, "k": [41, 41]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 100}, "r": 2, "c": {"a": 0, "k": [0.16862745098039217, 0.16862745098039217, 0.16862745098039217, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "ic_pin_main shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [22, 22]}, "s": {"a": 0, "k": [45, 45]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 100}, "r": 2, "c": {"a": 0, "k": [0, 0, 0, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "ic_pin_main shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [22, 22]}, "s": {"a": 0, "k": [45, 45]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}], "op": 180}, {"ty": 4, "nm": "ic_pin_foot", "ip": 0, "st": 0, "ind": 2, "hix": 4, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [100], "e": [100], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 13, "s": [100], "e": [0], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 14, "s": [0], "e": [0], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 108, "s": [0], "e": [0], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110, "s": [0], "e": [100], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 113, "s": [100], "e": [100], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 135}]}, "or": {"a": 0, "k": [0, 0, 0]}, "a": {"a": 0, "k": [1.5, 18, 0]}, "p": {"s": true, "x": {"a": 1, "k": [{"t": 0, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 29, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 68, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 108, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 135}]}, "y": {"a": 1, "k": [{"t": 0, "s": [176], "e": [176], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1, "s": [176], "e": [176], "i": {"x": [0.515], "y": [0.955]}, "o": {"x": [0.455], "y": [0.03]}}, {"t": 2, "s": [176], "e": [136], "i": {"x": [0.745], "y": [0.715]}, "o": {"x": [0.47], "y": [0]}}, {"t": 16, "s": [136], "e": [136], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 29, "s": [136], "e": [136], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 68, "s": [136], "e": [136], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 108, "s": [136], "e": [136], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 110, "s": [136], "e": [176], "i": {"x": [0.32], "y": [1]}, "o": {"x": [0.23], "y": [1]}}, {"t": 125, "s": [176], "e": [176], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 135}]}}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100, 100]}}, "shapes": [{"ty": "gr", "nm": "ic_pin_foot shape group", "it": [{"ty": "rc", "s": {"a": 0, "k": [2.7, 34.8870175]}, "r": {"a": 0, "k": 1}, "p": {"a": 0, "k": [1.5, 17.710175417]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 100}, "r": 2, "c": {"a": 0, "k": [0.17254901960784313, 0.17254901960784313, 0.17254901960784313, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "ic_pin_foot shape group", "it": [{"ty": "rc", "s": {"a": 0, "k": [2.7, 34.8870175]}, "r": {"a": 0, "k": 1}, "p": {"a": 0, "k": [1.5, 17.710175417]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}]}], "op": 180}, {"ty": 4, "nm": "ic_pin_shoes", "ip": 0, "st": 0, "ind": 1, "hix": 2, "ks": {"o": {"a": 0, "k": 100}, "or": {"a": 0, "k": [0, 0, 0]}, "a": {"a": 0, "k": [3.5, 1.5, 0]}, "p": {"s": true, "x": {"a": 1, "k": [{"t": 0, "s": [150], "e": [150], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1}]}, "y": {"a": 1, "k": [{"t": 0, "s": [193], "e": [193], "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"t": 1}]}}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "nm": "ic_pin_shoes shape group", "it": [{"ty": "el", "p": {"a": 0, "k": [150.5, 182]}, "s": {"a": 0, "k": [6.3, 2.68]}}, {"ty": "st", "o": {"a": 0, "k": 0}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 0]}, "lc": 3, "lj": 1, "ml": 1}, {"ty": "fl", "o": {"a": 0, "k": 100}, "r": 2, "c": {"a": 0, "k": [0.4823529411764706, 0.4823529411764706, 0.4823529411764706, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [-147, -181]}, "r": {"a": 0, "k": 0}}]}], "op": 180}], "op": 180, "w": 300, "h": 300}