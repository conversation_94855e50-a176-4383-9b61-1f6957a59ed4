{"version": 3, "sources": ["components/ScrollToTop.jsx", "service/FireBase.js", "context/AuthContext.js", "components/Social/SocialTwo.jsx", "components/Footer/Copyright/CopyRight.jsx", "redux/slices/auth.js", "components/Social/Social.jsx", "fake-data/MobileData.js", "fake-data/MobileDataLogin.js", "components/Header/Menu/MobileMenu.jsx", "components/DarkMode/index.js", "components/Header/HeaderDark.jsx", "components/List/FooterList.jsx", "components/Footer/FooterThreeDark.jsx", "views/NotFound.jsx", "components/ScrollTopBehaviour.jsx", "fake-data/Sunday-doc-menu.js", "components/Header/Menu/MobileSundayDoc.jsx", "components/Header/HeaderSunday.jsx", "fake-data/Foody-doc-menu.js", "components/DocMenu/foodyman.js", "views/Foodyman-doc/index.js", "views/Foodyman-doc/Navigation-btns.js", "views/Foodyman-doc/Introduction.js", "views/Foodyman-doc/Basic.js", "views/Foodyman-doc/Server.js", "views/Foodyman-doc/front.js", "views/Foodyman-doc/front-qr.js", "views/Foodyman-doc/Mandatory-setup-web-qr.js", "views/Foodyman-doc/admin.js", "views/Foodyman-doc/recommendations.js", "views/Foodyman-doc/local-front.js", "views/Foodyman-doc/Mobile-app.js", "views/Foodyman-doc/Flutter-SDK.js", "views/Foodyman-doc/Local-server.js", "views/Foodyman-doc/Install-on-server.js", "views/Foodyman-doc/Mandatory-setup.js", "views/Foodyman-doc/Customization.js", "views/Foodyman-doc/Mandatory-setup-mobile.js", "views/Foodyman-doc/Mandatory-setup-backend.js", "views/Uzmart-doc/Payment-Installation.js", "views/Foodyman-doc/image-settings.js", "views/Foodyman-doc/Moderator-setup-vendor.js", "views/Foodyman-doc/Moderator-setup-customer.js", "views/Foodyman-doc/Moderator-setup-pos.js", "views/Foodyman-doc/Moderator-setup-deliveryboy.js", "views/Foodyman-doc/Customization-mobile.js", "views/Foodyman-doc/Customization-mobile-delivery.js", "views/Foodyman-doc/Customization-mobile-customer.js", "views/Foodyman-doc/Customization-mobile-pos.js", "views/Foodyman-doc/App-build-release-customer.js", "views/Foodyman-doc/App-build-release-pos.js", "views/Foodyman-doc/App-build-release-vendor.js", "views/Foodyman-doc/App-build-release-deliveryboy.js", "views/Foodyman-doc/Mandatory-setup-web.js", "views/Foodyman-doc/Build-code-and-setup-on-server.js", "views/Foodyman-doc/Build-code-and-setup-on-server-backend.js", "views/Foodyman-doc/Update-admin-panel.js", "views/Foodyman-doc/Update-app-web.js", "components/Pricing/Pricing.jsx", "components/PricePlan/index.js", "views/Foodyman-doc/supportPlan.js", "views/Foodyman-doc/firebase-setup.js", "views/Foodyman-doc/Troubleshooting-backend.js", "views/Foodyman-doc/Troubleshooting-admin.js", "views/Foodyman-doc/update.js", "views/Foodyman-doc/Moderator-setup-kiosk.js", "views/Foodyman-doc/Customization-mobile-kiosk.js", "views/Foodyman-doc/App-build-release-kiosk.js", "views/Foodyman-doc/qr-code.js", "views/Foodyman-doc/install-qrcode.js", "router/Routing-foodyman.js", "router/Routing.js", "App.js", "reportWebVitals.js", "redux/rootReducer.js", "redux/store.js", "index.js"], "names": ["ScrollToTop", "isVisible", "setIsVisible", "useState", "useEffect", "toggleVisibility", "window", "pageYOffset", "addEventListener", "removeEventListener", "_jsx", "_Fragment", "children", "className", "onClick", "scrollToTop", "scrollTo", "top", "behavior", "src", "alt", "app", "initializeApp", "<PERSON><PERSON><PERSON><PERSON>", "process", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "auth", "getAuth", "AuthContext", "createContext", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "theme", "localStorage", "getItem", "darkTheme", "setDarkTheme", "Provider", "value", "googleSignIn", "googleAuthProvider", "GoogleAuthProvider", "signInWithPopup", "facebookSignIn", "facebookAuthProvider", "FacebookAuthProvider", "socialList", "iconName", "link", "SocialTwo", "map", "val", "i", "concat", "target", "rel", "href", "CopyRight", "_jsxs", "Date", "getFullYear", "authSlice", "createSlice", "name", "initialState", "user", "reducers", "setUserData", "state", "action", "payload", "clearUser", "actions", "SocialShare", "iconClass", "Social", "closeMenu", "dispatch", "useDispatch", "navigate", "useNavigate", "useSelector", "shallowEqual", "icon", "style", "flexGrow", "event", "preventDefault", "removeItem", "menuContent", "routerPath", "menuContentLogin", "MobileMenu", "ProSidebar", "<PERSON>bar<PERSON><PERSON>nt", "<PERSON><PERSON>", "item", "_item$dropDownItems", "_item$dropDownItems2", "dropDownItems", "length", "SubMenu", "title", "index", "MenuItem", "Link", "to", "ThemeChanger", "useContext", "document", "documentElement", "setAttribute", "setItem", "removeAttribute", "undefined", "handleToggle", "HiOutlineSun", "size", "BsFillMoonFill", "HeaderDark", "menu", "setMenu", "handleClickMenu", "setOpenUser", "openUser", "DarkMode", "height", "width", "loading", "handleClickUser", "display", "FaUserEdit", "handleLogout", "AiOutlineLogout", "FooterList", "FooterThreeDark", "NotFound", "<PERSON><PERSON><PERSON>", "ScrollTopBehaviour", "pathname", "useLocation", "sundaymartDocMenu", "MobileVendorMenu", "docMenu", "MobileDocument", "foodymanDocMenu", "DocumentationMenu", "defaultOpen", "NavLink", "Documentation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Outlet", "allMenus", "flatMap", "NavigationBtns", "windowPath", "location", "hash", "split", "currentMenuIdx", "findIndex", "currentMenus", "idx", "Introduction", "role", "AiOutlineInfoCircle", "frameBorder", "allow", "allowFullScreen", "SimpleReactLightbox", "SRLWrapper", "Basic", "GiCampfire", "Server", "Front", "MandatorySetupWeb", "text", "setText", "copyToClipBoard", "async", "copyMe", "id", "navigator", "clipboard", "writeText", "err", "Recommendations", "LocalFront", "MobileApp", "pageTitle", "FcIdea", "FlutterSdk", "LocalServer", "InstallOnServer", "MandatorySetup", "Customization", "MandatorySetupMobile", "MandatorySetupBackend", "PaymentInstallation", "referrerPolicy", "ImageSettings", "MandatorSetupVendor", "MandatorySetupCustomer", "MandatorySetupPos", "MandatorySetupDeliveryboy", "CustomizationMobile", "CustomizationMobileDelivery", "CustomizationMobileCustomer", "AppBuildReleaseCustomer", "AppBuildReleasePos", "AppBuildReleaseVendor", "AppBuildReleaseDelivery", "BuildCodeAndSetupOnServer", "BuildCodeAndSetupOnServerBackend", "UpdateAdminPanel", "UpdateAppWeb", "Pricing", "data", "price", "dangerouslySetInnerHTML", "__html", "description", "PricePlan", "pricing", "SupportPlan", "FirebaseSetup", "margin", "TroubleshootingBackend", "class", "type", "TroubleshootingAdmin", "UpdateFooyman", "MandatorySetupKiosk", "CustomizationMobileKiosk", "AppBuildReleaseSingleKiosk", "QRCode", "Foodyman", "Route", "path", "element", "FoodyDoc", "FoodyIntroduction", "FoodyBasic", "FoodyServer", "FoodyFront", "FoodyFrontQr", "FoodyMandatorySetupWebQr", "FoodyAdmin", "FoodyLocalFront", "FoodyMobileApp", "FoodyFlutterSDK", "FoodyLocalServer", "FoodyInstallOnServer", "FoodyMandatorySetup", "FoodyCustomization", "FoodyMandatorySetupMobile", "FoodyMandatorySetupBackend", "FoodyImageSettings", "FoodyMandatorySetupVendor", "FoodyMandatorySetupCustomer", "FoodyMandatorySetupPos", "FoodyMandatorySetupDeliveryboy", "FoodyCustomizationMobile", "CustomizationMobilePos", "FoodyMandatorySetupWeb", "FoodyBuildCodeAndSetupOnServer", "FoodyBuildCodeAndSetupOnServerBackend", "FoodyUpdateAdminPanel", "FoodyUpdateAppWeb", "FoodySupportPlan", "FoodyFirebaseSetup", "FoodyQRApp", "FoodyInstallQRcode", "Routing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "App", "AOS", "init", "duration", "content", "ToastContainer", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "reportWebVitals", "onPerfEntry", "Function", "then", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB", "rootReducer", "authPersistConfig", "key", "storage", "whitelist", "persistedReducer", "combineReducers", "persistReducer", "store", "configureStore", "reducer", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions", "FLUSH", "REHYDRATE", "PAUSE", "PERSIST", "PURGE", "REGISTER", "persistStore", "Root", "render", "getElementById"], "mappings": "yJAEe,SAASA,IACtB,MAAOC,EAAWC,GAAgBC,oBAAS,GA0B3C,OAfAC,qBAAU,KAER,MAAMC,EAAmBA,KACnBC,OAAOC,YAAc,IACvBL,GAAa,GAEbA,GAAa,EACf,EAKF,OAFAI,OAAOE,iBAAiB,SAAUH,GAE3B,IAAMC,OAAOG,oBAAoB,SAAUJ,EAAiB,GAClE,IAGDK,cAAAC,WAAA,CAAAC,SACGX,GACCS,cAAA,OAAAE,SACEF,cAAA,UAAQG,UAAU,cAAcC,QA1BpBC,KAClBT,OAAOU,SAAS,CACdC,IAAK,EACLC,SAAU,UACV,EAsByDN,SACnDF,cAAA,OAAKS,IAAI,8BAA8BC,IAAI,cAMvD,C,iECrCA,MASMC,EAAMC,YATW,CACrBC,OAAQC,0CACRC,WAAYD,gCACZE,UAAWF,gBACXG,cAAeH,4BACfI,kBAAmBJ,eACnBK,MAAOL,4CACPM,cAAeN,iBAGJO,EAAOC,YAAQX,GCLfY,EAAcC,0BAMpB,SAASC,EAAYC,GAAgB,IAAf,SAAExB,GAAUwB,EACvC,MAAMC,EAAQC,aAAaC,QAAQ,UAC5BC,EAAWC,GAAgBtC,mBAAmB,KAAVkC,GAY3C,OACE3B,cAACuB,EAAYS,SAAQ,CACnBC,MAAO,CAAEC,aAZb,WACE,MAAMC,EAAqB,IAAIC,IAC/B,OAAOC,YAAgBhB,EAAMc,EAC/B,EAS2BG,eAP3B,WACE,MAAMC,EAAuB,IAAIC,IACjC,OAAOH,YAAgBhB,EAAMkB,EAC/B,EAI2CR,eAAcD,aAAY5B,SAEhEA,GAGP,C,yBCjCA,MAAMuC,EAAa,CACjB,CACEC,SAAU,mBACVC,KAAM,oCACNxC,UAAW,YAEb,CACEuC,SAAU,mBACVC,KAAM,wCACNxC,UAAW,kBAsBAyC,MAlBGA,IAEd5C,cAAAC,WAAA,CAAAC,SACGuC,EAAWI,KAAI,CAACC,EAAKC,IACpB/C,cAAA,KACEG,UAAS,4CAAA6C,OAA8CF,EAAI3C,WAC3D8C,OAAO,SACPC,IAAI,sBACJC,KAAML,EAAIH,KAAKzC,SAGfF,cAAA,KAAGG,UAAW2C,EAAIJ,YAFbK,OCHAK,MAlBGA,IAEdC,eAAA,OAAKlD,UAAU,gDAA+CD,SAAA,CAC5DF,cAAA,OAAKG,UAAU,8CAA6CD,SAC1DmD,eAAA,KAAGlD,UAAU,uCAAsCD,SAAA,CAAC,QAChD,IAAIoD,MAAOC,cAAc,IAACvD,cAAA,QAAAE,SAAM,aAAe,8BAIrDF,cAAA,OAAKG,UAAU,mDAAkDD,SAC/DF,cAAA,OAAKG,UAAU,sCAAqCD,SAClDF,cAAC4C,EAAS,W,uCCZpB,MAIMY,EAAYC,YAAY,CAC5BC,KAAM,OACNC,aANmB,CACnBC,KAAM,MAMNC,SAAU,CACRC,YAAYC,EAAOC,GACjB,MAAM,QAAEC,GAAYD,EACpBD,EAAMH,KAAOK,CACf,EACAC,UAAUH,GACRA,EAAMH,KAAO,IACf,MAIS,YAAEE,EAAW,UAAEI,GAAcV,EAAUW,QACrCX,QAAiB,QChBhC,MAAMY,EAAc,CAClB,CACE1B,SAAU,mBACVC,KAAM,4CACN0B,UAAW,iBAEb,CACE3B,SAAU,mBACVC,KAAM,gCACN0B,UAAW,aAgDAC,MA5CA5C,IAA+B,IAA9B,UAAE6C,EAAYA,UAAU7C,EACtC,MAAM8C,EAAWC,cACXC,EAAWC,eACX,KAAEf,GAASgB,aAAab,GAAUA,EAAM1C,MAAMwD,KAUpD,OACExB,eAAA,OAAKlD,UAAU,iDAAgDD,SAAA,CAC5DkE,EAAYvB,KAAI,CAACiC,EAAM/B,IACtB/C,cAAA,KACEG,UAAS,4CAAA6C,OAA8C8B,EAAKT,WAC5DlB,KAAM2B,EAAKnC,KACXM,OAAO,SACPC,IAAI,sBAAqBhD,SAGzBF,cAAA,KAAGG,UAAW2E,EAAKpC,YAFdK,KAKRa,GACC5D,cAAA,OACEG,UAAU,wDACV4E,MAAO,CAAEC,SAAU,GAAI9E,SAEvBF,cAAA,OAAKG,UAAU,wBAAuBD,SACpCF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCF,cAAA,KAAGmD,KAAK,UAAU/C,QA5BV6E,IAClBA,EAAMC,iBACNtD,aAAauD,WAAW,SACxBX,EAASN,KACTK,IACAG,EAAS,IAAI,EAuBmCxE,SAAC,mBAO3C,EC1DH,MAAMkF,EAAc,CAKzB,CACE1B,KAAM,WACN2B,WAAY,aAMd,CACE3B,KAAM,SACN2B,WAAY,YCfHC,EAAmB,CAK9B,CACE5B,KAAM,WACN2B,WAAY,aAMd,CACE3B,KAAM,SACN2B,WAAY,YC0CDE,MA1CI7D,IAAoB,IAAnB,UAAE6C,GAAW7C,EAC/B,MAAM,KAAEkC,GAASgB,aAAab,GAAUA,EAAM1C,MAAMwD,KAEpD,OACExB,eAAApD,WAAA,CAAAC,SAAA,CACEF,cAAA,OAAKG,UAAU,iCAAgCD,SAC7CF,cAACwF,IAAU,CAAAtF,SACTF,cAACyF,IAAc,CAAAvF,SACbF,cAAC0F,IAAI,CAACvF,UAAU,uBAAsBD,UAClC0D,EAAOwB,EAAcE,GAAkBzC,KAAI,CAAC8C,EAAM5C,KAAC,IAAA6C,EAAAC,EAAA,OACnD7F,cAAA,OAAAE,UACqB,QAAlB0F,EAAAD,EAAKG,qBAAa,IAAAF,OAAA,EAAlBA,EAAoBG,QAAS,EAC5B/F,cAACgG,IAAO,CAACC,MAAON,EAAKjC,KAAKxD,SACL,QADK2F,EACvBF,EAAKG,qBAAa,IAAAD,OAAA,EAAlBA,EAAoBhD,KAAI,CAACC,EAAKoD,IAC7BlG,cAACmG,IAAQ,CAAAjG,SACPF,cAACoG,IAAI,CAACC,GAAIvD,EAAIuC,WAAWnF,SAAE4C,EAAIY,QADlBwC,MAFanD,GAQhC/C,cAACmG,IAAQ,CAAChG,UAAU,eAAcD,SAChCF,cAACoG,IAAI,CAACC,GAAIV,EAAKN,WAAWnF,SAAEyF,EAAKjC,UAX7BX,EAcJ,YAOhBM,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCmD,eAAA,KAAGlD,UAAU,gCAA+BD,SAAA,CAAC,KACzC,IAAIoD,MAAOC,cAAc,IAACvD,cAAA,QAAAE,SAAM,aAAe,4BAGnDF,cAACsE,EAAM,CAACC,UAAWA,SAEpB,E,gBChBQ+B,MAhCMA,KACjB,MAAM,UAACxE,EAAS,aAAEC,GAAgBwE,qBAAWhF,GAa7C,OAVA7B,qBAAU,KACFoC,GACA0E,SAASC,gBAAgBC,aAAa,aAAc,QACpD9E,aAAa+E,QAAQ,QAAS,UAE9BH,SAASC,gBAAgBG,gBAAgB,cACzChF,aAAa+E,QAAQ,QAAS,SAClC,GACD,CAAC7E,IAGA9B,cAAA,OAAAE,cACmB2G,IAAd/E,GACG9B,cAAA,UACIG,UAAU,aACVC,QAjBK0G,IAAM/E,GAAcD,GAiBH5B,SAEpB4B,EAGE9B,cAAC+G,IAAY,CAACC,KAAM,GAAI7G,UAAU,eAFlCH,cAACiH,IAAc,CAACD,KAAM,GAAI7G,UAAU,kBAO9C,E,eC2DC+G,MApFIA,KACjB,MAAM1C,EAAWC,eACV0C,EAAMC,GAAW3H,oBAAS,IAC3B,KAAEmE,GAASgB,aAAab,GAAUA,EAAM1C,MAAMwD,KAC9CwC,EAAkBA,KACtBD,GAASD,GACTG,GAAY,EAAM,GAEbC,EAAUD,GAAe7H,oBAAS,GAMzC,OACE4D,eAAApD,WAAA,CAAAC,SAAA,CACEF,cAAA,UAAQG,UAAU,yCAAwCD,SACxDF,cAAA,OAAKG,UAAW,iDAAiDD,SAC/DF,cAAA,OAAKG,UAAU,gBAAeD,SAC5BmD,eAAA,OAAKlD,UAAU,mBAAkBD,SAAA,CAC/BF,cAACoG,IAAI,CAACjG,UAAU,kBAAkBkG,GAAG,IAAGnG,SACtCF,cAAA,MAAIG,UAAU,YAAWD,SAAC,eAG5BmD,eAAA,QAAMlD,UAAU,mDAAkDD,SAAA,CAChEF,cAAA,QAAAE,SACEF,cAACwH,EAAQ,MAEXxH,cAAA,OACEG,UAAU,6DACVC,QAASiH,EAAgBnH,SAEzBF,cAAA,KAAGG,UAAU,uCAQzBkD,eAAA,OACElD,UAAWgH,EAAO,6BAA+B,sBAAsBjH,SAAA,CAEvEmD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACxC0D,EACCP,eAAA,OAAKlD,UAAU,iBAAgBD,SAAA,CAC7BF,cAAA,OACES,IAAI,4DACJN,UAAU,iBACVsH,OAAO,KACPC,MAAM,KACNhH,IAAI,SACJiH,QAAQ,OACRvH,QA3CUwH,IAAMN,GAAaC,KA6C/BlE,eAAA,OACElD,UAAU,qBACV4E,MAAO,CAAE8C,QAASN,EAAW,QAAU,QAASrH,SAAA,CAEhDmD,eAAC+C,IAAI,CAACC,GAAG,gBAAgBlG,UAAU,gBAAeD,SAAA,CAChDF,cAAC8H,IAAU,CAAC3H,UAAU,kBAAmB,IACzCH,cAAA,QAAAE,SAAM,kBAGRmD,eAAC+C,IAAI,CAACC,GAAG,IAAIlG,UAAU,gBAAgBC,QApDhC2H,IAAMvD,EAASN,KAoDuChE,SAAA,CAC3DF,cAACgI,IAAe,CAAC7H,UAAU,kBAC3BH,cAAA,QAAAE,SAAM,oBAIV,KACJF,cAAA,QACEG,UAAU,mDACVC,QAASiH,EAAgBnH,SAEzBF,cAAA,KAAGG,UAAU,yBAGjBH,cAACuF,EAAU,CAAChB,UAAW8C,SAExB,ECrEQY,MAdIA,KACjB,MAAM,KAAErE,GAASgB,aAAab,GAAUA,EAAM1C,MAAMwD,KAEpD,OACE7E,cAAA,MAAAE,UACK0D,EAAqB0B,EAAdF,GAAgCvC,KAAI,CAAC8C,EAAM5C,IACnD/C,cAAA,MAAAE,SACEF,cAACoG,IAAI,CAACC,GAAIV,EAAKN,WAAWnF,SAAEyF,EAAKjC,QAD1BX,MAIR,ECiCMmF,MA7CSA,IAEpB7E,eAAA,OAAKlD,UAAU,kBAAiBD,SAAA,CAC9BF,cAAA,OAAKG,UAAU,gBAAeD,SAC5BF,cAAA,OAAKG,UAAU,qBAAqB,WAAS,OAAO,iBAAe,IAAGD,SACpEF,cAACoG,IAAI,CAACC,GAAG,IAAGnG,SACVF,cAAA,MAAIG,UAAU,YAAWD,SAAC,mBAKhCF,cAAA,OAAKG,UAAU,gBAAeD,SAC5BF,cAAA,OACEG,UAAU,qBACV,WAAS,OACT,iBAAe,MAAKD,SAEpBF,cAAA,OAAKG,UAAU,8CAA6CD,SAC1DF,cAACiI,EAAU,UAKjBjI,cAAA,OAAKG,UAAU,gBAAeD,SAC5BF,cAAA,OACEG,UAAU,qBACV,WAAS,OACT,iBAAe,MAAKD,SAEpBmD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAAA,KAAGG,UAAU,kBAAkBgD,KAAK,8BAA6BjD,SAAC,yBAGlEF,cAAA,SACAA,cAAA,KAAGG,UAAU,kBAAkBgD,KAAK,mBAAkBjD,SAAC,mBAGvDF,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,wBCiF/CoD,MAnHEA,IAEb9E,eAAA,OAAAnD,SAAA,CACEF,cAACoI,IAAM,CAAAlI,SACLF,cAAA,SAAAE,SAAO,0BAITF,cAACkH,EAAU,IAGXlH,cAAA,OAAKG,UAAU,wBAAuBD,SACpCF,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,WAASlD,UAAU,aAAYD,SAAA,CAE7BF,cAAA,OACEG,UAAU,aACV4E,MAAO,CAAE,YAAa,YAAa,WAAY,eAEjD/E,cAAA,OAAKG,UAAU,gBAAeD,SAC5BmD,eAAA,OAAKlD,UAAU,MAAKD,SAAA,CAClBmD,eAAA,OAAKlD,UAAU,8CAA6CD,SAAA,CAE1DF,cAAA,OACEG,UAAU,qBACV,WAAS,OACT,iBAAe,MAAKD,SAEpBF,cAAA,OACES,IAAI,mCACJC,IAAI,QACJiH,QAAQ,WAIZ3H,cAAA,OACEG,UAAU,aACV4E,MAAO,CACL,WAAY,WACZ,WAAY,kBAIlB1B,eAAA,OAAKlD,UAAU,WAAUD,SAAA,CAEvBF,cAAA,OACEG,UAAU,qBACV,WAAS,OACT,iBAAe,IAAGD,SAElBmD,eAAA,MAAIlD,UAAU,2BAA0BD,SAAA,CAAC,YAC9BF,cAAA,SAAM,sBAKnBA,cAAA,OACEG,UAAU,aACV4E,MAAO,CAAE,YAAa,UAAW,WAAY,cAG/C/E,cAAA,OACEG,UAAU,sBACV,WAAS,OACT,iBAAe,MAAKD,SAEpBF,cAAA,KAAAE,SAAG,oEAGLF,cAAA,OACEG,UAAU,aACV4E,MAAO,CAAE,YAAa,OAAQ,WAAY,YAG5C/E,cAAA,OACEG,UAAU,sBACV,WAAS,OACT,iBAAe,MAAKD,SAEpBF,cAACoG,IAAI,CAACC,GAAG,IAAIlG,UAAU,aAAYD,SAAC,2BAQ5CF,cAAA,OACEG,UAAU,aACV4E,MAAO,CAAE,YAAa,YAAa,WAAY,sBASvD/E,cAAA,UAAQG,UAAU,iCAAgCD,SAChDmD,eAAA,OAAKlD,UAAU,gBAAeD,SAAA,CAC5BF,cAAA,OAAKG,UAAU,kBAAiBD,SAC9BF,cAACkI,EAAe,MAIlBlI,cAAA,OAAKG,UAAU,qBAAoBD,SACjCF,cAACoD,EAAS,cC9GP,SAASiF,IACtB,MAAM,SAAEC,GAAaC,cAMrB,OAJA7I,qBAAU,KACRE,OAAOU,SAAS,EAAG,EAAE,GACpB,CAACgI,IAEG,IACT,CC0GO,MAAME,EAAoB,CAC/B,CACE9E,KAAM,eACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,0CAEd,CACE3B,KAAM,sBACN2B,WAAY,+CAIlB,CACE3B,KAAM,cACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,oCAEd,CACE3B,KAAM,eACN2B,WAAY,qDAEd,CACE3B,KAAM,kBACN2B,WAAY,uDAIlB,CACE3B,KAAM,cACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,mCAEd,CACE3B,KAAM,eACN2B,WAAY,+CAEd,CACE3B,KAAM,kBACN2B,WAAY,qDAIlB,CACE3B,KAAM,mBACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,mCAEd,CACE3B,KAAM,eACN2B,WAAY,iDAEd,CACE3B,KAAM,4BACN2B,WAAY,kDAQlB,CACE3B,KAAM,eACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,wCAEd,CACE3B,KAAM,eACN2B,WAAY,sDAEd,CACE3B,KAAM,gBACN2B,WAAY,oDAEd,CACE3B,KAAM,sBACN2B,WAAY,0DAQlB,CACE3B,KAAM,aACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,wCAEd,CACE3B,KAAM,eACN2B,WAAY,oDAEd,CACE3B,KAAM,gBACN2B,WAAY,kDAEd,CACE3B,KAAM,sBACN2B,WAAY,wDAQlB,CACE3B,KAAM,aACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,wCAEd,CACE3B,KAAM,eACN2B,WAAY,yDAEd,CACE3B,KAAM,gBACN2B,WAAY,uDAEd,CACE3B,KAAM,sBACN2B,WAAY,6DAQlB,CACE3B,KAAM,UACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,qCAEd,CACE3B,KAAM,eACN2B,WAAY,iDAEd,CACE3B,KAAM,gBACN2B,WAAY,+CAEd,CACE3B,KAAM,sBACN2B,WAAY,qDAQlB,CACE3B,KAAM,iBACN2B,WAAY,sCAEd,CACE3B,KAAM,SACN2B,WAAY,qCC/ODoD,MAzCU/G,IAAuD,IAAtD,gBAAE2F,EAAe,QAAEqB,EAAUF,GAAmB9G,EACxE,OACE2B,eAAApD,WAAA,CAAAC,SAAA,CACEF,cAAA,OAAKG,UAAU,iCAAgCD,SAC7CF,cAACwF,IAAU,CAAAtF,SACTF,cAACyF,IAAc,CAAAvF,SACbF,cAAC0F,IAAI,CAACvF,UAAU,uBAAsBD,SACnCwI,EAAQ7F,KAAI,CAAC8C,EAAM5C,KAAC,IAAA6C,EAAAC,EAAA,OACnB7F,cAAA,OAAAE,UACqB,QAAlB0F,EAAAD,EAAKG,qBAAa,IAAAF,OAAA,EAAlBA,EAAoBG,QAAS,EAC5B/F,cAACgG,IAAO,CAACC,MAAON,EAAKjC,KAAKxD,SACL,QADK2F,EACvBF,EAAKG,qBAAa,IAAAD,OAAA,EAAlBA,EAAoBhD,KAAI,CAACC,EAAKoD,IAC7BlG,cAACmG,IAAQ,CAAAjG,SACPF,cAACoG,IAAI,CAACC,GAAIvD,EAAIuC,WAAYjF,QAASiH,EAAgBnH,SAChD4C,EAAIY,QAFMwC,MAFanD,GAUhC/C,cAACmG,IAAQ,CAAChG,UAAU,eAAcD,SAChCF,cAACoG,IAAI,CAACC,GAAIV,EAAKN,WAAWnF,SAAEyF,EAAKjC,UAb7BX,EAgBJ,YAOhB/C,cAAA,OAAKG,UAAU,6BAA4BD,SACzCmD,eAAA,KAAGlD,UAAU,gCAA+BD,SAAA,CAAC,KACzC,IAAIoD,MAAOC,cAAc,IAACvD,cAAA,QAAAE,SAAM,aAAe,0BACvCF,cAAA,gBAGb,ECGQkH,MA/CIxF,IAAkB,IAAjB,QAAEgH,GAAShH,EAC7B,MAAOyF,EAAMC,GAAW3H,oBAAS,GAC3B4H,EAAkBA,IAAMD,GAASD,GAEvC,OACE9D,eAAApD,WAAA,CAAAC,SAAA,CACEF,cAAA,UAAQG,UAAU,yCAAwCD,SACxDF,cAAA,OAAKG,UAAW,iDAAiDD,SAC/DF,cAAA,OAAKG,UAAU,gBAAeD,SAC5BmD,eAAA,OAAKlD,UAAU,mBAAkBD,SAAA,CAC/BF,cAACoG,IAAI,CAACjG,UAAU,kBAAkBkG,GAAG,IAAGnG,SACtCF,cAAA,MAAIG,UAAU,aAAYD,SAAC,eAE7BF,cAACwH,EAAQ,IACTxH,cAAA,OACEG,UAAU,kFACVC,QAASiH,EAAgBnH,SAEzBF,cAAA,KAAGG,UAAU,2BAEfH,cAAA,OACEG,UAAU,6DACVC,QAASiH,aAOnBhE,eAAA,OACElD,UAAWgH,EAAO,6BAA+B,sBAAsBjH,SAAA,CAEvEmD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAAA,OAAKG,UAAU,0BACfH,cAAA,QACEG,UAAU,mDACVC,QAASiH,EAAgBnH,SAEzBF,cAAA,KAAGG,UAAU,yBAGjBH,cAAC2I,EAAc,CAACD,QAASA,EAASrB,gBAAiBA,SAEpD,EC2IA,MAAMuB,EAAkB,CAC7B,CACElF,KAAM,eACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,wCAEd,CACE3B,KAAM,sBACN2B,WAAY,6CAIlB,CACE3B,KAAM,cACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,kCAEd,CACE3B,KAAM,eACN2B,WAAY,mDAEd,CACE3B,KAAM,uBACN2B,WAAY,gDAEd,CACE3B,KAAM,iBACN2B,WAAY,0CAEd,CACE3B,KAAM,kBACN2B,WAAY,qDAIlB,CACE3B,KAAM,cACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,iCAEd,CACE3B,KAAM,eACN2B,WAAY,6CAEd,CACE3B,KAAM,kBACN2B,WAAY,mDAIlB,CACE3B,KAAM,mBACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,iCAEd,CACE3B,KAAM,eACN2B,WAAY,iDAQlB,CACE3B,KAAM,iBACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,oCAEd,CACE3B,KAAM,eACN2B,WAAY,oDAQlB,CACE3B,KAAM,eACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,sCAEd,CACE3B,KAAM,eACN2B,WAAY,oDAEd,CACE3B,KAAM,gBACN2B,WAAY,kDAEd,CACE3B,KAAM,sBACN2B,WAAY,wDAQlB,CACE3B,KAAM,aACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,sCAEd,CACE3B,KAAM,eACN2B,WAAY,kDAEd,CACE3B,KAAM,gBACN2B,WAAY,gDAEd,CACE3B,KAAM,sBACN2B,WAAY,sDAQlB,CACE3B,KAAM,aACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,sCAEd,CACE3B,KAAM,eACN2B,WAAY,uDAEd,CACE3B,KAAM,gBACN2B,WAAY,qDAEd,CACE3B,KAAM,sBACN2B,WAAY,2DAQlB,CACE3B,KAAM,UACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,mCAEd,CACE3B,KAAM,eACN2B,WAAY,+CAEd,CACE3B,KAAM,gBACN2B,WAAY,6CAEd,CACE3B,KAAM,sBACN2B,WAAY,mDAQlB,CACE3B,KAAM,YACNoC,cAAe,CACb,CACEpC,KAAM,eACN2B,WAAY,qCAEd,CACE3B,KAAM,eACN2B,WAAY,iDAEd,CACE3B,KAAM,gBACN2B,WAAY,+CAEd,CACE3B,KAAM,sBACN2B,WAAY,qDAqBlB,CACE3B,KAAM,iBACN2B,WAAY,oCAEd,CACE3B,KAAM,SACN2B,WAAY,mCCpXDwD,MArCWA,IAEtB7I,cAAA,OAAKG,UAAU,UAASD,SACtBF,cAACwF,IAAU,CAACrF,UAAU,cAAaD,SACjCF,cAACyF,IAAc,CAAAvF,SACbF,cAAC0F,IAAI,CAACvF,UAAU,uBAAsBD,SACnC0I,EAAgB/F,KAAI,CAAC8C,EAAM5C,KAAC,IAAA6C,EAAAC,EAAA,OAC3B7F,cAAA,OAAAE,UACqB,QAAlB0F,EAAAD,EAAKG,qBAAa,IAAAF,OAAA,EAAlBA,EAAoBG,QAAS,EAC5B/F,cAACgG,IAAO,CAACC,MAAON,EAAKjC,KAAcoF,aAAa,EAAK5I,SAChC,QADgC2F,EAClDF,EAAKG,qBAAa,IAAAD,OAAA,EAAlBA,EAAoBhD,KAAI,CAACC,EAAKoD,IAC7BlG,cAACmG,IAAQ,CAAAjG,SACPF,cAAC+I,IAAO,CACN5I,UAAU,kBACVkG,GAAIvD,EAAIuC,WAAWnF,SAElB4C,EAAIY,QALMwC,MAFanD,GAahC/C,cAACmG,IAAQ,CAAChG,UAAU,eAAcD,SAChCF,cAAC+I,IAAO,CAAC1C,GAAIV,EAAKN,WAAYlF,UAAU,kBAAiBD,SACtDyF,EAAKjC,UAjBJX,EAqBJ,YCOLiG,MAvCOA,IAElB3F,eAAA,OAAKlD,UAAU,eAAcD,SAAA,CAC3BF,cAACoI,IAAM,CAAAlI,SACLF,cAAA,SAAAE,SAAO,wCAETF,cAACiJ,EAAY,CAACP,QAASE,IACvBvF,eAAA,OAAKlD,UAAU,WAAUD,SAAA,CACvBF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,WAAAE,SACEF,cAAA,OAAKG,UAAU,gBAAeD,SAC5BmD,eAAA,OAAKlD,UAAU,MAAKD,SAAA,CAClBF,cAAA,OAAKG,UAAU,WAAUD,SACvBF,cAAA,OAAKG,UAAU,mBAAkBD,SAC/BF,cAAC6I,EAAiB,QAGtB7I,cAAA,OAAKG,UAAU,sBAAqBD,SAClCF,cAACkJ,IAAM,eAMjBlJ,cAAA,OACEG,UAAU,aACV4E,MAAO,CAAE,YAAa,OAAQ,WAAY,eAI9C/E,cAAA,UAAQG,UAAU,gDAA+CD,SAC/DF,cAAA,OAAKG,UAAU,gBAAeD,SAC5BF,cAACoD,EAAS,W,OCrCpB,MAAM+F,EAAWP,EAAgBQ,SAASjC,GACpCA,EAAK9B,WAAmB8B,EACrBA,EAAKrB,gBA4CCuD,MAzCf,WACE,MAAMC,EAAa1J,OAAO2J,SAASC,KAAKC,MAAM,KAAK,GAC7CC,EAAiBP,EAASQ,WAC7BxC,GAASA,EAAK9B,aAAeiE,IAEhC,IAAwB,IAApBI,EACF,OACE1J,cAAA,OAAKG,UAAU,wBAAuBD,SACpCmD,eAAC+C,IAAI,CACHC,GAAG,0CACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,+BAK5B,MAAM0J,EACmB,IAAnBF,EAA6B,CAAC,KAAMP,EAAS,IAC7CO,IAAmBP,EAASpD,OAAS,EAChC,CAACoD,EAASA,EAASpD,OAAS,GAAI,MAElC,CAACoD,EAASO,EAAiB,GAAIP,EAASO,EAAiB,IAGlE,OACE1J,cAAA,OAAKG,UAAU,wBAAuBD,SACnC0J,EAAa/G,KACZ,CAACsE,EAAM0C,IACL1C,GACE9D,eAAC+C,IAAI,CAACC,GAAIc,EAAK9B,WAAYlF,UAAU,gBAAeD,SAAA,CAClDF,cAAA,KAAAE,SAAY,IAAR2J,EAAY,UAAY,SAC5BxG,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,IAAEiH,EAAKzD,KAAK,aAM9C,EC4HeoG,MArKMA,IAEjBzG,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,+IAKRF,cAAA,MAAIG,UAAU,QAAOD,SAAC,iBACtBmD,eAAA,UAAQlD,UAAU,wBAAuBD,SAAA,CAAC,iCAExCF,cAAA,UAAQG,UAAU,SAAQD,SAAC,gBAE7BF,cAAA,OAAKG,UAAU,sBAAqBD,SAClCF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNC,iBAAe,MAGnB9G,eAAA,UAAQlD,UAAU,wBAAuBD,SAAA,CAAC,iCAExCF,cAAA,UAAQG,UAAU,SAAQD,SAAC,mBAE7BF,cAAA,OAAKG,UAAU,sBAAqBD,SAClCF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNC,iBAAe,MAGnB9G,eAAA,UAAQlD,UAAU,wBAAuBD,SAAA,CAAC,iCAExCF,cAAA,UAAQG,UAAU,SAAQD,SAAC,gCAE7BF,cAAA,OAAKG,UAAU,sBAAqBD,SAClCF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNC,iBAAe,MAGnB9G,eAAA,UAAQlD,UAAU,wBAAuBD,SAAA,CAAC,iCAExCF,cAAA,UAAQG,UAAU,SAAQD,SAAC,yBAE7BF,cAAA,OAAKG,UAAU,sBAAqBD,SAClCF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNC,iBAAe,MAGnBnK,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,aACnCmD,eAAA,UAAQlD,UAAU,wBAAuBD,SAAA,CAAC,gBAC3BF,cAAA,UAAQG,UAAU,SAAQD,SAAC,iBAG1CmD,eAAA,OAAAnD,SAAA,CAAK,2JAGSF,cAAA,SAAM,8LAG+BA,cAAA,SAAM,yKAIvDA,cAAA,SAAM,2JAINA,cAAA,SAAM,yLAINA,cAAA,OAAKG,UAAU,eAAe,sEAE9BH,cAAA,SACAqD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,8BAKlBH,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,2CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,2CACJC,IAAI,WACJiH,QAAQ,OACRxH,UAAU,iCAMpBH,cAAA,OAAKG,UAAU,eACfH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,mCAErC,+BACkBF,cAAA,SAAM,kDACaA,cAAA,SAAM,6CACXA,cAAA,SACrCA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,mDAErC,yBAELF,cAAA,SAAM,4BACcA,cAAA,SAAM,0BACRA,cAAA,SAAM,8BACFA,cAAA,SAAM,iCACHA,cAAA,SACzBA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,oDAErC,4BACeF,cAAA,SACpBA,cAAA,OAAKG,UAAU,kBAGjBH,cAACqJ,EAAc,O,SCtGNiB,OA3DDA,IAEVjH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,UAAU,6NAKhCF,cAAA,SACAA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,UAAU,iHAEpBF,cAAA,SAAM,sFAE7BA,cAAA,SAAM,4EACqDA,cAAA,SACpEA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,cAAc,wFAE9CF,cAAA,SAAM,yDACiCA,cAAA,SAAM,8EACAA,cAAA,SACvDqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,2XAQRmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YAE5CF,cAAA,KAAGG,UAAU,KAAID,SAAC,yDAEpBmD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,uCACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,sBAEtBmD,eAAC+C,IAAI,CAACC,GAAG,iCAAiClG,UAAU,YAAWD,SAAA,CAC7DF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,uBCOfsK,OA3DAA,IAEXnH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,yBACtBmD,eAAA,MAAAnD,SAAA,CACEF,cAAA,MAAAE,SAAI,eACJF,cAAA,MAAAE,SAAI,iBAENF,cAAA,MAAAE,SACEmD,eAAA,MAAAnD,SAAA,CAAI,oEAEFmD,eAAA,MAAAnD,SAAA,CACEF,cAAA,MAAAE,SAAI,YACJF,cAAA,MAAAE,SAAI,aACJF,cAAA,MAAAE,SAAI,OACJF,cAAA,MAAAE,SAAI,SACJF,cAAA,MAAAE,SAAI,WACJF,cAAA,MAAAE,SAAI,gBAIVF,cAAA,SAAM,mBACKA,cAAA,SAGXA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,UAAU,sFAE3CF,cAAA,SACTA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,cAAc,yDACPF,cAAA,SACjDqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,2XAQRF,cAAA,OAAKG,UAAU,SACfkD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,gFAIRF,cAACqJ,EAAc,OCbNoB,OAzCDA,IAEVpH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,0BAA0B,sBAClCF,cAAA,SAAM,sBACNA,cAAA,SAAM,uBACLA,cAAA,SACfA,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,SACfH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,UAAU,4EACgBF,cAAA,SACpEA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,cAAc,8EACDF,cAAA,SACvDqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,2XAQRmD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,gFAIRF,cAACqJ,EAAc,OCMNoB,OA1CDA,IAEVpH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,0BAA0B,qBAEhDF,cAAA,SAAM,mBACKA,cAAA,SAAM,uBACFA,cAAA,SACfA,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,SACfH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,UAAU,4EACgBF,cAAA,SACpEA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,cAAc,oDAC3BF,cAAA,SAC7BqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,2XAQRmD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,gFAIRF,cAACqJ,EAAc,OC2GNqB,OA/IWA,KACxB,MAAOC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAEF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mCACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,qEAEjEF,cAAA,SAAM,+GAGNA,cAAA,SACAA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAKd,kEAENH,cAAA,SAAM,mCACqBA,cAAA,SAC3BA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,SACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,OAAQ,GAAG3K,SAEhC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,iDACmCA,cAAA,SACzCA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,eACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,aAAc,GAAG3K,SAEtC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,+BACiBA,cAAA,SACvBA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,uCACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,gBAAiB,GAAG3K,SAEzC,IAATyK,EAAa,UAAY,cAIhCtH,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,+HAE+C,IACrDF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,qCACaF,cAAA,SAAM,+CAG1CA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,6EACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,cAGzB,IAAI,4HAIb3K,cAAA,MAAAE,SAAI,OACJmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,4EAGNF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,2EAIvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,wEACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,iBAKlC3K,cAACqJ,EAAc,MACX,ECnGKoB,OAzCDA,IAEVpH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,gCAAgC,qBAEtDF,cAAA,SAAM,wBACUA,cAAA,SAChBA,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,SACfH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,UAAU,4EACgBF,cAAA,SACpEA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,cAAc,oEACXF,cAAA,SAC7CqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,2XAQRmD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,gFAIRF,cAACqJ,EAAc,OCTNgC,OA3BSA,IAEpBhI,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,oBAAoB,+CACHF,cAAA,SAAM,mBAClCA,cAAA,SAAM,mBACNA,cAAA,SAAM,iEACwCA,cAAA,SACzDA,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,SACfkD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,gFAIRF,cAACqJ,EAAc,OC+BNiC,OAvDIA,IAEfjI,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,aACtBF,cAAA,KAAGG,UAAU,aAAYD,SAAC,gFAGtB,WACK,IACTF,cAAA,KACEmD,KAAK,yDACLhD,UAAU,oBAAmBD,SAC9B,2DAGDF,cAAA,SAAM,OACD,IACLA,cAAA,KACEmD,KAAK,qDACLhD,UAAU,oBAAmBD,SAC9B,uDAGDF,cAAA,SAAM,SACC,IACPA,cAAA,KACEmD,KAAK,kEACLhD,UAAU,oBAAmBD,SAC9B,oEAGDF,cAAA,SACAA,cAAA,OAAKG,UAAU,SACfkD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,sCACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,oBAEtBmD,eAAC+C,IAAI,CAACC,GAAG,uCAAuClG,UAAU,YAAWD,SAAA,CACnEF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,uB,SCffqL,OA/BG7J,IAAmC,IAAlC,UAAE8J,EAAY,cAAc9J,EAC7C,OACE2B,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAEsL,IAAe,yBACrBxL,cAAA,SACjBqD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACyL,KAAM,CAACzE,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACzB,kDACyCF,cAAA,SAC/CA,cAAA,KAAGmD,KAAK,sJAAqJjD,SAAC,2JAG1J,yCAC4B,IAClCF,cAAA,UAAQG,UAAU,SAAQD,SAAC,WAAe,KAAEF,cAAA,SAAM,kCACxBA,cAAA,SAAM,gCACRA,cAAA,SAAM,iDACWA,cAAA,SAAM,mCACpBA,cAAA,SAAM,uCACFA,cAAA,SAC/BA,cAACqJ,EAAc,MACX,ECsBKqC,OAnDIA,IAEfrI,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,eACtBF,cAAA,KAAGG,UAAU,aAAYD,SAAC,gIAGtB,WACK,IACTF,cAAA,KACEmD,KAAK,uDACLhD,UAAU,oBAAmBD,SAC9B,yDAGDF,cAAA,SAAM,OACD,IACLA,cAAA,KACEmD,KAAK,qDACLhD,UAAU,oBAAmBD,SAC9B,uDAGDF,cAAA,SAAM,SACC,IACPA,cAAA,KACEmD,KAAK,qDACLhD,UAAU,oBAAmBD,SAC9B,uDAGDF,cAAA,SACAqD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CAACC,GAAG,qCAAqClG,UAAU,gBAAeD,SAAA,CACrEF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,oBAEtBmD,eAAC+C,IAAI,CAACC,GAAG,sCAAsClG,UAAU,YAAWD,SAAA,CAClEF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,8BCgBfyL,OA5DKA,IAEhBtI,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,YACtBmD,eAAA,KAAGlD,UAAU,aAAYD,SAAA,CACtB,IAAI,wFAEU,OAEjBF,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,iBAAgBD,SAC7BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,+BACNiE,MAAM,sGACNC,iBAAe,MAGnBnK,cAAA,OAAKG,UAAU,SACfH,cAAA,SACAA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,YAAa,IACvDF,cAAA,KACEmD,KAAK,8CACLhD,UAAU,oBAAmBD,SAC9B,gDAGDF,cAAA,SACAA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,gBAAiB,IAC3DF,cAAA,KAAGmD,KAAK,iCAAiChD,UAAU,oBAAmBD,SAAC,mCAGvEF,cAAA,SACAqD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,sCACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,gBAEtBmD,eAAC+C,IAAI,CACHC,GAAG,kDACHlG,UAAU,YAAWD,SAAA,CAErBF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,iCCsNf0L,OAxQSA,IAEpBvI,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,uBACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,4CAGRmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,yFAIRmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eACtC,uIAGF,2CAC6BF,cAAA,SAAM,+BACzCA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,wBAAwB,oFAGlEF,cAAA,SACAA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,qCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,qCACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAKd,kFAENH,cAAA,SACAA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,mCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,mCACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,sCACJC,IAAI,iBACJiH,QAAQ,OACRxH,UAAU,gCAKd,uCAENH,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,6BAA6B,mBAAcD,IAAI,WAAUhD,SAC/DF,cAAA,OACES,IAAI,6BACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,qBACLF,cAAA,UAAAE,SAAQ,UAAc,WAErCmD,eAAA,KAAAnD,SAAA,CAAG,6CACyCF,cAAA,UAAAE,SAAQ,UAAc,+FAE1CF,cAAA,UAAAE,SAAQ,kBAEhCmD,eAAA,KAAAnD,SAAA,CAAG,oEAEDF,cAAA,UAAAE,SAAQ,WAAe,uJAIzBF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,kCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,kCACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAGG,UAAU,OAAMD,SAAC,6BACpBF,cAAA,KAAAE,SAAG,yDACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,sCACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD1B,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,yCACeF,cAAA,UAAAE,SAAQ,mBAE3CmD,eAAA,KAAAnD,SAAA,CAAG,mGAEuBF,cAAA,UAAAE,SAAQ,2BAA+B,6EAGjEF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,uCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,uCACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,KAAAnD,SAAA,CAAG,+EAEIF,cAAA,UAAAE,SAAQ,oBAEfF,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD1B,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,gCACMF,cAAA,UAAAE,SAAQ,cAAkB,WAEpDmD,eAAA,KAAAnD,SAAA,CAAG,uEAEDF,cAAA,UAAAE,SAAQ,cAAkB,iBAAcF,cAAA,UAAAE,SAAQ,gBAAoB,oCACnCF,cAAA,UAAAE,SAAQ,UAAc,8CAC5BF,cAAA,UAAAE,SAAQ,kBAErCF,cAAA,KAAAE,SAAG,oEACHF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,KAAAnD,SAAA,4BAEEF,cAAA,SAAM,oBAENA,cAAA,SAAM,iBAENA,cAAA,SAAM,iCAENA,cAAA,SAAM,sCAENA,cAAA,SAAM,sCAENA,cAAA,SAAM,sCAENA,cAAA,SAAM,gCAENA,cAAA,SAAM,mBAIVA,cAAA,KAAAE,SAAG,mBACHF,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD1B,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,yEACiE,IACvEmD,eAAA,KAAGF,KAAK,yCAAyCF,OAAO,SAAQ/C,SAAA,CAC9DF,cAAA,UAAAE,SAAQ,SAAc,UAG1BF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,iCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,iCACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,UACE0H,MAAM,OACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNC,iBAAe,IAEjBnK,cAACqJ,EAAc,OC9INwC,OAxHQA,IAEnBxI,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,qBACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,uBACtDF,cAAA,UAAQG,UAAU,SAAQD,SAAC,YAAgB,4DAEtDmD,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,+CAEF,sTAKSF,cAAA,UAAQG,UAAU,SAAQD,SAAC,0BAE/B,IAAI,QAEdF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,2BAA2B,iTAMrEF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,8BAA8B,sMAIxEF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,4BAC1CF,cAAA,QAAMG,UAAU,eAAcD,SAC5BF,cAAA,UACES,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,KACZC,MAAM,+FAGV7G,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,iCAGPF,cAAA,QAAMG,UAAU,eAAcD,SAC5BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,2FACNC,iBAAe,MAGnB9G,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,iCAGPF,cAAA,QAAMG,UAAU,eAAcD,SAC5BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,2FACNC,iBAAe,MAGnB9G,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,yCAGPF,cAAA,QAAMG,UAAU,eAAcD,SAC5BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,2FACNC,iBAAe,MAGnB9G,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,wCAGPF,cAAA,QAAMG,UAAU,eAAcD,SAC5BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,2FACNC,iBAAe,MAGnB9G,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,4CACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,2BAEtBmD,eAAC+C,IAAI,CAACC,GAAG,wCAAwClG,UAAU,YAAWD,SAAA,CACpEF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,4BCxCf4L,OAxEOA,IAElBzI,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,kBACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,2BAA2B,8EACA,IACrEF,cAAA,SACAqD,eAAA,QAAMlD,UAAU,sBAAqBD,SAAA,CAAC,WAC3B,IAAI,wBACR,8CAEPF,cAAA,SACAqD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SAETF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,UACJiH,QAAQ,OACRxH,UAAU,8BAKlBH,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,UACJiH,QAAQ,OACRxH,UAAU,iCAMpBkD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,0CACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,qCAEtBmD,eAAC+C,IAAI,CACHC,GAAG,gDACHlG,UAAU,YAAWD,SAAA,CAErBF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,oCCmNf6L,OAlRcA,KAC3B,MAAOpB,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,qBACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,6CAGRmD,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,yCACkC,OAEzCF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAAsB,oHAEIF,cAAA,SAAM,oBAC7D,IACbA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,+BAAiC,2DACbF,cAAA,SAAM,sBAC3DA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,2EACEF,cAAA,SAC3DA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAC1CmD,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,mCAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAGpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,8CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAAE,SAAM,2BACNF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,+BAA2B,GAAG3K,SAEnD,IAATyK,EAAa,UAAY,kBAKlCtH,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,0CAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,4BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,2BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAAnD,SAAA,2BAC6B,IAACF,cAAA,SAAM,6BAGpCA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,iDACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,2KAG1C,IACrBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,mCAAqC,sDACrB,IACpDmD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAChC,IAAI,gDAEA,wDAEPmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,+CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,+BAErBF,cAAA,QAAMG,UAAU,aAAYD,SAAC,iCAE/BF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EAAgB,kCAAmC,GACpD3K,SAES,IAATyK,EAAa,UAAY,eAIhC3K,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,gCAAgC,mBAAcD,IAAI,WAAUhD,SAClEF,cAAA,OACES,IAAI,gCACJC,IAAI,UACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,+EAGjEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,sCAE7B,4EAEPF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oBAAsB,uHAEhB,IAC1CF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wBAA0B,kGAExC,IACtBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,WACtDF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,yCAErC,oFACmE,IACxEF,cAAA,SAAM,+CAAwC,IAC9CA,cAAA,UAAQG,UAAU,SAAQD,SAAC,yCAA6C,4EACE,IAC1EF,cAAA,SACAqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,mKAIF,+EACkE,IACxEF,cAAA,SAAM,2CACNA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,0BACpCF,cAAA,SAAM,4EAAqE,IAC3EqD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAChC,IAAI,yCAEA,oEAC0DF,cAAA,SAAM,yEACP,IAChEA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qBAEnEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,iBAAmB,IAACF,cAAA,SAAM,4EAE9DqD,eAAA,UAAQlD,UAAU,SAAQD,SAAA,CACvB,IAAI,gEACyD,OAEhEF,cAAA,SAAM,mHACmC,IACzCqD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAAC,4BACR,KAAK,kBAAgB,KAAK,cAAY,OAElEF,cAAA,SAAM,4OAKNA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,2CAC1CF,cAAA,OACIS,IAAI,2CACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,wBAEdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,4BAA4B,2IAEHF,cAAA,SAAM,mJAEPA,cAAA,SAAM,4IAEdA,cAAA,SAAM,sGAE1CA,cAAA,SAAM,oBAE5BA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAE5B,IAAI,yBACW,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oCACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,+CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAElB,+GAGJF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,6GACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,eAIhCtH,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CAAC,gBACN,IACdF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,kCAAoC,yBACjD,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,kCACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,4DAGvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,0DACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlCtH,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,yDACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,yCAEtBmD,eAAC+C,IAAI,CACHC,GAAG,+CACHlG,UAAU,YAAWD,SAAA,CAErBF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,uCAGpB,ECoOK8L,OAlfeA,IAE1B3I,eAAA,OACElD,UAAU,eACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,8BACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,2FAIRmD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eACtC,2FAIRF,cAAA,KAAAE,SAAG,6GAIHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,mCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,mCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,qKAKHF,cAAA,KAAAE,SAAG,gCACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,iIAIHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,sCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,wBACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,wCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,wCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,+BACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,qCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,qCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,oQAMHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,yCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,yCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,qTAOHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,mCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,mCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,oNAKHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,qCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,qCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,mDACHF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCF,cAAA,KAAAE,SAAG,uBAELF,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,qCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,qCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,8BAKlBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,qKAKHF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCF,cAAA,KAAAE,SAAG,+BAELF,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,qCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,qCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,8BAKlBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD1B,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,+FAIRmD,eAAA,MAAAnD,SAAA,CACEF,cAAA,MAAAE,SAAI,oDACJF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,gCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,gCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAAE,SACEF,cAAA,UAAAE,SAAQ,wEAKZF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,6CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,6CACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,oBAAoB,mBAAcD,IAAI,WAAUhD,SACtDF,cAAA,OACES,IAAI,oBACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YAE5CmD,eAAA,MAAAnD,SAAA,CACEF,cAAA,MAAAE,SAAI,0BACJF,cAAA,MAAAE,SAAI,6CAGRF,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD1B,eAAA,KAAAnD,SAAA,CAAG,kDAEDF,cAAA,SAAM,uBAAoBA,cAAA,UAAAE,SAAQ,0BAEzB,OAAK,IACdF,cAAA,UAAAE,SAAQ,6DAGRF,cAAA,SAAM,sBAAmBA,cAAA,UAAAE,SAAQ,0BACjCF,cAAA,SAAM,wCAGRA,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,8BAKlBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,sCACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,8BAKlBH,cAAA,OAAKG,UAAU,oBAAmBD,SAChCF,cAAA,KAAAE,SAAG,2BAELF,cAAA,KAAAE,SAAG,qBACHmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAGG,UAAU,OAAMD,SAAC,6BACpBF,cAAA,KAAAE,SAAG,kCAELF,cAAA,KAAAE,SAAG,8BACHmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,sBACtC,4CACmCF,cAAA,SAAM,4CAC9BA,cAAA,SAAM,yCAAsCA,cAAA,SAAM,sDAGrEA,cAAA,MAAAE,SAAI,oBACJmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCmD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,8BACSF,cAAA,SAAM,uBAEnCqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,+BACUF,cAAA,SAAM,qBAEpCqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,4CACuBF,cAAA,SAAM,6BAEjDqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,kBACHF,cAAA,SAAM,oCAEvBqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,8BACSF,cAAA,SAAM,sCAGnCqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,sBACCF,cAAA,SAAM,mEAG3BqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,+BACUF,cAAA,SAAM,wEAGpCqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,uBACEF,cAAA,SAAM,kDAE5BqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,mBACFF,cAAA,SAAM,6BAExBqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,kBACHF,cAAA,SAAM,8BAEvBqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,4BACOF,cAAA,SAAM,4BAAyBA,cAAA,SAAM,8BAGhEqD,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,4BACOF,cAAA,SAAM,6BAA0BA,cAAA,SAAM,kCAInEA,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,+DACHF,cAAA,UACE0H,MAAM,OACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNC,iBAAe,IAEjBnK,cAAA,SACAA,cAAA,SACAA,cAAA,KAAAE,SAAG,iJAIHF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,2CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,2CACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,8BAKlBH,cAAA,SACAA,cAAA,KAAAE,SAAG,+GAIHF,cAAA,SACAA,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,2CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,2CACJC,IAAI,QACJiH,QAAQ,OACRxH,UAAU,8BAKlBH,cAACqJ,EAAc,OC1ZN4C,OArFaA,IAExB5I,eAAA,OACElD,UAAU,eACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,qBACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,oIAIRmD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eACtC,iBACQF,cAAA,SAAM,YAAU,IAC9BA,cAAA,KAAAE,SAAG,2DAA0D,IAACF,cAAA,SAAM,cAC1D,IACVA,cAAA,KAAAE,SAAG,6DAA4D,IAACF,cAAA,SAAM,cAC5D,IACVA,cAAA,KAAAE,SAAG,6DAA4D,IAACF,cAAA,SAAM,cAC7DA,cAAA,KAAAE,SAAG,4DAA4D,IACxEF,cAAA,SAAM,iBAAe,IACrBA,cAAA,KAAAE,SAAG,wDAAuD,IAACF,cAAA,SAAM,aACzDA,cAAA,KAAAE,SAAG,2DAA2D,IACtEF,cAAA,SAAM,kBAAgB,IACtBA,cAAA,KAAAE,SAAG,iEAAiE,IACpEF,cAAA,SAAM,aAAW,IACjBA,cAAA,KAAAE,SAAG,6DAA4D,IAACF,cAAA,SAAM,aAC9DA,cAAA,KAAAE,SAAG,2DAA2D,IACtEF,cAAA,YAEFA,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,+BACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,UACE0H,MAAM,OACND,OAAO,MACPhH,IAAI,gEACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNgC,eAAe,kCACf/B,iBAAe,MAGnBnK,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,iCACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,UACE0H,MAAM,OACND,OAAO,MACPhH,IAAI,gEACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNgC,eAAe,kCACf/B,iBAAe,MAGnBnK,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAAE,SAAG,iCACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,UACE0H,MAAM,OACND,OAAO,MACPhH,IAAI,gEACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNgC,eAAe,kCACf/B,iBAAe,MAGnBnK,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAACqJ,EAAc,OC2BN8C,OAzGOA,IAElB9I,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,oBACtBmD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,8DAGRF,cAAA,MAAAE,SAAI,wEAGJF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,MAAAnD,SAAA,CACEmD,eAAA,MAAAnD,SAAA,CAAI,6BACwBF,cAAA,UAAAE,SAAQ,gBAAoB,oCAGxDF,cAAA,MAAAE,SAAI,0CACJF,cAAA,MAAAE,SAAI,mGAIJF,cAAA,MAAAE,SAAI,uGAKNF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,gCAOpBH,cAAA,MAAAE,SAAI,8EAIJmD,eAAA,MAAAnD,SAAA,CACEF,cAAA,MAAAE,SAAI,iCACJF,cAAA,MAAAE,SAAI,kFAIJF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,2CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,2CACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAAE,SAAI,uEAENF,cAACqJ,EAAc,OCmJN+C,OAtPaA,KAC1B,MAAOzB,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,qBACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,6CAGRmD,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,yCACkC,OAEzCF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAAsB,oHAEIF,cAAA,SAAM,oBAC7D,IACbA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,+BAAiC,2DACbF,cAAA,SAAM,sBAC3DA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,2EACEF,cAAA,SAC3DA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAC1CmD,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,mCAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAGpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,8CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAAE,SAAM,2BACNF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,+BAA2B,GAAG3K,SAEnD,IAATyK,EAAa,UAAY,kBAKlCtH,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,0CAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,4BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,2BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAAnD,SAAA,2BAC6B,IAACF,cAAA,SAAM,6BAGpCA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,iDACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,2KAG1C,IACrBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,mCAAqC,sDACrB,IACpDF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,wDAEnEmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,4BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,+BAErBF,cAAA,QAAMG,UAAU,aAAYD,SAAC,iCAE/BF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EAAgB,kCAAmC,GACpD3K,SAES,IAATyK,EAAa,UAAY,eAIhC3K,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,iCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,iCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,+EAGjEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,sCAE7B,4EAEPF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oBAAsB,uHAEhB,IAC1CF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wBAA0B,kGAExC,IACtBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,WACtDF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,mBAAmB,qFACAF,cAAA,SAAM,+CAC7B,IACtCA,cAAA,UAAQG,UAAU,SAAQD,SAAC,yCAA6C,4EACE,IAC1EF,cAAA,SACAqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,mKAIF,+EACkE,IACxEF,cAAA,SAAM,2CACNA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,0BACpCF,cAAA,SAAM,4EAAqE,IAC3EqD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAChC,IAAI,yCAEA,oEAC0DF,cAAA,SAAM,yEACP,IAChEA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qBAEnEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,iBAAmB,IAACF,cAAA,SAAM,4EAE9DqD,eAAA,UAAQlD,UAAU,SAAQD,SAAA,CACvB,IAAI,gEACyD,OAEhEF,cAAA,SAAM,4OAKNA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,4BAA4B,2IAEHF,cAAA,SAAM,mJAEPA,cAAA,SAAM,4IAEdA,cAAA,SAAM,sGAE1CA,cAAA,SAAM,oBAE5BA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAE5B,IAAI,yBACW,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oCACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,+CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAElB,+GAGJF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,6GACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,eAIhCtH,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CAAC,gBACN,IACdF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,kCAAoC,yBACjD,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,kCACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,4DAGvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,0DACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAACqJ,EAAc,MACX,ECIKgD,OAtPgBA,KAC7B,MAAO1B,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,qBACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,6CAGRmD,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,yCACkC,OAEzCF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAAsB,oHAEIF,cAAA,SAAM,oBAC7D,IACbA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,+BAAiC,2DACbF,cAAA,SAAM,sBAC3DA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,2EACEF,cAAA,SAC3DA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAC1CmD,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,mCAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAGpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,8CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAAE,SAAM,2BACNF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,+BAA2B,GAAG3K,SAEnD,IAATyK,EAAa,UAAY,kBAKlCtH,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,0CAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,4BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,2BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAAnD,SAAA,2BAC6B,IAACF,cAAA,SAAM,6BAGpCA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,iDACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,2KAG1C,IACrBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,mCAAqC,sDACrB,IACpDF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,wDAEnEmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,4BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,+BAErBF,cAAA,QAAMG,UAAU,aAAYD,SAAC,iCAE/BF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EAAgB,kCAAmC,GACpD3K,SAES,IAATyK,EAAa,UAAY,eAIhC3K,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,iCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,iCACJC,IAAI,UACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,+EAGjEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,sCAE7B,4EAEPF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oBAAsB,uHAEhB,IAC1CF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wBAA0B,kGAExC,IACtBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,WACtDF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,mBAAmB,qFACAF,cAAA,SAAM,+CAC7B,IACtCA,cAAA,UAAQG,UAAU,SAAQD,SAAC,yCAA6C,4EACE,IAC1EF,cAAA,SACAqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,mKAIF,+EACkE,IACxEF,cAAA,SAAM,2CACNA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,0BACpCF,cAAA,SAAM,4EAAqE,IAC3EqD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAChC,IAAI,yCAEA,oEAC0DF,cAAA,SAAM,yEACP,IAChEA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qBAEnEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,iBAAmB,IAACF,cAAA,SAAM,4EAE9DqD,eAAA,UAAQlD,UAAU,SAAQD,SAAA,CACvB,IAAI,gEACyD,OAEhEF,cAAA,SAAM,4OAKNA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,4BAA4B,2IAEHF,cAAA,SAAM,mJAEPA,cAAA,SAAM,4IAEdA,cAAA,SAAM,sGAE1CA,cAAA,SAAM,oBAE5BA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAE5B,IAAI,yBACW,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oCACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,+CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAElB,+GAGJF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,6GACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,eAIhCtH,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CAAC,gBACN,IACdF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,kCAAoC,yBACjD,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,kCACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,4DAGvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,0DACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAACqJ,EAAc,MACX,ECcKiD,OAhQWA,KACxB,MAAO3B,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,eACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,6CAGRmD,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,yCACkC,OAEzCF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAAsB,oHAEIF,cAAA,SAAM,oBAC7D,IACbA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,+BAAiC,2DACbF,cAAA,SAAM,sBAC3DA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,2EACEF,cAAA,SAC3DA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAC1CmD,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,mCAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAGpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,8CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAAE,SAAM,2BACNF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,+BAA2B,GAAG3K,SAEnD,IAATyK,EAAa,UAAY,kBAKlCtH,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,0CAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,4BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,2BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAAnD,SAAA,2BAC6B,IAACF,cAAA,SAAM,6BAGpCA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,iDACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,2KAG1C,IACrBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,mCAAqC,sDACrB,IACpDmD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAChC,IAAI,8CAEA,wDAEPmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,6CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,+BAErBF,cAAA,QAAMG,UAAU,aAAYD,SAAC,iCAE/BF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EAAgB,kCAAmC,GACpD3K,SAES,IAATyK,EAAa,UAAY,eAIhC3K,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,wCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,wCACJC,IAAI,UACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,+EAGjEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,sCAE7B,4EAEPF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oBAAsB,uHAEhB,IAC1CF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wBAA0B,kGAExC,IACtBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,WACtDF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,yCAErC,oFACmE,IACxEF,cAAA,SAAM,+CAAwC,IAC9CA,cAAA,UAAQG,UAAU,SAAQD,SAAC,yCAA6C,4EACE,IAC1EF,cAAA,SACAqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,mKAIF,+EACkE,IACxEF,cAAA,SAAM,2CACNA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,0BACpCF,cAAA,SAAM,4EAAqE,IAC3EqD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAChC,IAAI,yCAEA,oEAC0DF,cAAA,SAAM,yEACP,IAChEA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qBAEnEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,iBAAmB,IAACF,cAAA,SAAM,4EAE9DqD,eAAA,UAAQlD,UAAU,SAAQD,SAAA,CACvB,IAAI,gEACyD,OAEhEF,cAAA,SAAM,mHACmC,IACzCqD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAAC,4BACR,KAAK,kBAAgB,KAAK,cAAY,OAElEF,cAAA,SAAM,4OAKNA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,4BAA4B,2IAEHF,cAAA,SAAM,mJAEPA,cAAA,SAAM,4IAEdA,cAAA,SAAM,sGAE1CA,cAAA,SAAM,oBAE5BA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAE5B,IAAI,yBACW,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oCACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,+CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAElB,+GAGJF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,6GACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,eAIhCtH,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CAAC,gBACN,IACdF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,kCAAoC,yBACjD,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,kCACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,4DAGvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,0DACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAACqJ,EAAc,MACX,ECIKkD,OAhQmBA,KAChC,MAAO5B,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,qBACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,6CAGRmD,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,yCACkC,OAEzCF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAAsB,oHAEIF,cAAA,SAAM,oBAC7D,IACbA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,+BAAiC,2DACbF,cAAA,SAAM,sBAC3DA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,2EACEF,cAAA,SAC3DA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAC1CmD,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,mCAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAGpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,8CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAAE,SAAM,2BACNF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,+BAA2B,GAAG3K,SAEnD,IAATyK,EAAa,UAAY,kBAKlCtH,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,0CAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,4BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,2BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAAnD,SAAA,2BAC6B,IAACF,cAAA,SAAM,6BAGpCA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,iDACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,2KAG1C,IACrBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,mCAAqC,sDACrB,IACpDF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,wDAEnEmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,4BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,+BAErBF,cAAA,QAAMG,UAAU,aAAYD,SAAC,iCAE/BF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EAAgB,kCAAmC,GACpD3K,SAES,IAATyK,EAAa,UAAY,eAIhC3K,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,iCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,iCACJC,IAAI,UACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,+EAGjEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,sCAE7B,4EAEPF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oBAAsB,uHAEhB,IAC1CF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wBAA0B,kGAExC,IACtBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,WACtDF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,mBAAmB,qFACAF,cAAA,SAAM,+CAC7B,IACtCA,cAAA,UAAQG,UAAU,SAAQD,SAAC,yCAA6C,4EACE,IAC1EF,cAAA,SACAqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,mKAIF,+EACkE,IACxEF,cAAA,SAAM,2CACNA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,0BACpCF,cAAA,SAAM,4EAAqE,IAC3EqD,eAAA,QAAMlD,UAAU,oBAAmBD,SAAA,CAChC,IAAI,yCAEA,oEAC0DF,cAAA,SAAM,yEACP,IAChEA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qBAEnEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,iBAAmB,IAACF,cAAA,SAAM,4EAE9DqD,eAAA,UAAQlD,UAAU,SAAQD,SAAA,CACvB,IAAI,gEACyD,OAEhEF,cAAA,SAAM,4OAKNqD,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,2CAGPF,cAAA,OACES,IAAI,iCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,wBAEZH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,4BAA4B,2IAEHF,cAAA,SAAM,mJAEPA,cAAA,SAAM,4IAEdA,cAAA,SAAM,sGAE1CA,cAAA,SAAM,oBAE5BA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAE5B,IAAI,yBACW,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oCACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,+CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAElB,+GAGJF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,6GACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,eAIhCtH,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CAAC,gBACN,IACdF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,kCAAoC,yBACjD,IACvBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,kCACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,4DAGvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,0DACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAACqJ,EAAc,MACX,EC/MKmD,OAhDaA,IAExBnJ,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mBACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,yBAAyB,qGAE9CF,cAAA,SACrBqD,eAAA,QAAMlD,UAAU,sBAAqBD,SAAA,CAAC,YAC1B,IAAI,wBACT,6CAEPmD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,mBAEZH,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,sBAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,cAAO,IACtEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wCAE7B,uCAC6BF,cAAA,SACpCA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,OACES,IAAI,6BACJC,IAAI,MACJP,UAAU,0BAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,oBAAoB,+GAG9DF,cAACqJ,EAAc,OCKNoD,OAhDqBA,IAEhCpJ,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mBACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,yBAAyB,qGAE9CF,cAAA,SACrBqD,eAAA,QAAMlD,UAAU,sBAAqBD,SAAA,CAAC,YAC1B,IAAI,wBACT,6CAEPmD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,mBAEZH,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,sBAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,cAAO,IACtEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wCAE7B,uCAC6BF,cAAA,SACpCA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,OACES,IAAI,8BACJC,IAAI,MACJP,UAAU,0BAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,oBAAoB,+GAG9DF,cAACqJ,EAAc,OCKNqD,OAhDqBA,IAEhCrJ,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mBACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,yBAAyB,qGAE9CF,cAAA,SACrBqD,eAAA,QAAMlD,UAAU,sBAAqBD,SAAA,CAAC,YAC1B,IAAI,wBACT,6CAEPmD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,mBAEZH,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,sBAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,cAAO,IACtEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,2CAE7B,uCAC6BF,cAAA,SACpCA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,OACES,IAAI,8BACJC,IAAI,MACJP,UAAU,0BAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,oBAAoB,+GAG9DF,cAACqJ,EAAc,OCKNqD,OAhDqBA,IAEhCrJ,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mBACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,yBAAyB,qGAE9CF,cAAA,SACrBqD,eAAA,QAAMlD,UAAU,sBAAqBD,SAAA,CAAC,YAC1B,IAAI,wBACT,6CAEPmD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,mBAEZH,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,sBAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,cAAO,IACtEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wCAE7B,uCAC6BF,cAAA,SACpCA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,OACES,IAAI,oDACJC,IAAI,MACJP,UAAU,0BAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,oBAAoB,+GAG9DF,cAACqJ,EAAc,OC8BNsD,OAzEiBA,KAC9B,MAAOhC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,wBACtBF,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,sBAAsB,uCAEzDF,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,sBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,oBAAqB,GAAG3K,SAE7C,IAATyK,EAAa,UAAY,cAG1B,wFAGN3K,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,8FAIvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,4FACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,cAG1B,uBAEN3K,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qDAEnEF,cAAA,UAAQG,UAAU,SAAQD,SAAC,gDAG3BF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,kBAAkB,uQAK5DF,cAAA,UAAQG,UAAU,SAAQD,SAAC,4CAG3BF,cAACqJ,EAAc,MACX,ECOKuD,OA5EYA,KACzB,MAAOjC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,wBACtBF,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,sBAAsB,uCAEzDF,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,sBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,oBAAqB,GAAG3K,SAE7C,IAATyK,EAAa,UAAY,cAG1B,wFAGN3K,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,8FAIvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,4FACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,cAG1B,uBAEN3K,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qDAEnEF,cAAA,UAAQG,UAAU,SAAQD,SAAC,gDAG3BF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,kBAAkB,uQAK5DF,cAAA,UAAQG,UAAU,SAAQD,SAAC,4CAG3BF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAAsB,iHAGhEF,cAACqJ,EAAc,MACX,ECCKwD,OAzEeA,KAC5B,MAAOlC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,wBACtBF,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,sBAAsB,uCAEzDF,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,sBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,oBAAqB,GAAG3K,SAE7C,IAATyK,EAAa,UAAY,cAG1B,wFAGN3K,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,8FAIvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,4FACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,cAG1B,uBAEN3K,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qDAEnEF,cAAA,UAAQG,UAAU,SAAQD,SAAC,gDAG3BF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,kBAAkB,uQAK5DF,cAAA,UAAQG,UAAU,SAAQD,SAAC,4CAG3BF,cAACqJ,EAAc,MACX,ECIKyD,OAzEiBA,KAC9B,MAAOnC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,wBACtBF,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,sBAAsB,uCAEzDF,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,sBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,oBAAqB,GAAG3K,SAE7C,IAATyK,EAAa,UAAY,cAG1B,wFAGN3K,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,8FAIvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,4FACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,cAG1B,uBAEN3K,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qDAEnEF,cAAA,UAAQG,UAAU,SAAQD,SAAC,gDAG3BF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,kBAAkB,uQAK5DF,cAAA,UAAQG,UAAU,SAAQD,SAAC,4CAG3BF,cAACqJ,EAAc,MACX,EC6CKqB,OAhHWA,KACxB,MAAOC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAEF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mCACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,qEAEjEF,cAAA,SAAM,+GAGNA,cAAA,SACAA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBmD,eAACgH,IAAU,CAAAnK,SAAA,CACTF,cAAA,KACEmD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,sCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,0BAGdH,cAAA,KACEmD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,sCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,iCAKd,kEAENH,cAAA,SAAM,mCACqBA,cAAA,SAC3BA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,SACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,OAAQ,GAAG3K,SAEhC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,iDACmCA,cAAA,SACzCA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,eACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,aAAc,GAAG3K,SAEtC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,+BACiBA,cAAA,SACvBA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,2BACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,aAAc,GAAG3K,SAEtC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SACAA,cAAA,OAAKG,UAAU,sBAAqBD,SAClCF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,uBACNgE,YAAY,IACZC,MAAM,sGACNC,iBAAe,MAGnBnK,cAACqJ,EAAc,MACX,EC0DK0D,OAvKmBA,KAChC,MAAOpC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAEF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mCACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,qEAEjEF,cAAA,SAAM,+GAGNA,cAAA,SACAA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBmD,eAACgH,IAAU,CAAAnK,SAAA,CACTF,cAAA,KACEmD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,sCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,0BAGdH,cAAA,KACImD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEhBF,cAAA,OACIS,IAAI,sCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,iCAKhB,kEAENH,cAAA,SAAM,mCACqBA,cAAA,SAC3BA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,SACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,OAAQ,GAAG3K,SAEhC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,KAAGG,UAAU,OAAMD,SAAC,OACpBF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,gBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,cAAe,GAAG3K,SAEvC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,iDACmCA,cAAA,SACzCA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,eACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,aAAc,GAAG3K,SAEtC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,KAAGG,UAAU,OAAMD,SAAC,OACpBF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,kBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,gBAAiB,GAAG3K,SAEzC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,+BACiBA,cAAA,SACvBA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,8BACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,gBAAiB,GAAG3K,SAEzC,IAATyK,EAAa,UAAY,cAIhCtH,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,+HAE+C,IACrDF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,qCACaF,cAAA,SAAM,+CAG1CA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,6EACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,cAGzB,IAAI,4HAIbtH,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,8CACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,6BAEtBmD,eAAC+C,IAAI,CACHC,GAAG,mDACHlG,UAAU,YAAWD,SAAA,CAErBF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,uCAGpB,ECfK8M,OApJ0BA,KACvC,MAAOrC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAEF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mCACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,wBAAwB,uEAElEF,cAAA,SACAA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,6BAA6B,mBAAcD,IAAI,WAAUhD,SAC/DF,cAAA,OACES,IAAI,6CACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAKd,kEAENH,cAAA,SAAM,mCACqBA,cAAA,SAC3BA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,SACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,OAAQ,GAAG3K,SAEhC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,KAAGG,UAAU,OAAMD,SAAC,OACpBF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,gBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,cAAe,GAAG3K,SAEvC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,iDACmCA,cAAA,SACzCA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,eACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,aAAc,GAAG3K,SAEtC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,KAAGG,UAAU,OAAMD,SAAC,OACpBF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,kBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,gBAAiB,GAAG3K,SAEzC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,+BACiBA,cAAA,SACvBA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,8BACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,gBAAiB,GAAG3K,SAEzC,IAATyK,EAAa,UAAY,cAIhCtH,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,+HAE+C,IACrDF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,qCACaF,cAAA,SAAM,+CAG1CA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,6EACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,cAGzB,IAAI,4HAIbtH,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,yDACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,wCAEtBmD,eAAC+C,IAAI,CACHC,GAAG,mDACHlG,UAAU,YAAWD,SAAA,CAErBF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,uCAGpB,EClGK+M,OA/CUA,IAErB5J,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,gBACtBF,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,WAAW,0IAG9CF,cAAA,OAAKG,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAC,kEAGtDF,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,iBAAgBD,SAC7BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,wBACNiE,MAAM,sGACNC,iBAAe,MAGnB9G,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,yGAIRmD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CAACC,GAAG,mCAAmClG,UAAU,gBAAeD,SAAA,CACnEF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,kBAEtBmD,eAAC+C,IAAI,CAACC,GAAG,yCAAyClG,UAAU,YAAWD,SAAA,CACrEF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,yBCiFfgN,OAzHMA,KACnB,MAAOvC,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAEF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,cACtBF,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,sBAAsB,8DAEzDF,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,sBAAsB,uDAEzDF,cAAA,SAAM,mCACqBA,cAAA,SAC3BA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,SACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,OAAQ,GAAG3K,SAEhC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,KAAGG,UAAU,OAAMD,SAAC,OACpBF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,gBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,cAAe,GAAG3K,SAEvC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,iDACmCA,cAAA,SACzCA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,oBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,aAAc,GAAG3K,SAEtC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,KAAGG,UAAU,OAAMD,SAAC,OACpBF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,mBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,gBAAiB,GAAG3K,SAEzC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,SAAM,mCACqBA,cAAA,SAC3BA,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,6BACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,aAAc,GAAG3K,SAEtC,IAATyK,EAAa,UAAY,cAIhC3K,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,iBAAgBD,SAC7BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,wBACNiE,MAAM,sGACNC,iBAAe,MAGnB9G,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,yGAIRmD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,6CACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,qBAEtBmD,eAAC+C,IAAI,CAACC,GAAG,uCAAuClG,UAAU,YAAWD,SAAA,CACnEF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,2BAGpB,EC5EKiN,OA1CCzL,IAAoB,IAAnB,KAAE0L,EAAO,IAAI1L,EAC5B,OACE1B,cAAAC,WAAA,CAAAC,SACGkN,EAAKvK,KAAI,CAACC,EAAKC,IACd/C,cAAA,OAAKG,UAAU,yBAAwBD,SACrCF,cAAA,OACEG,UAAU,2BACV,WAAS,OACT,iBAAoB,IAAJ4C,EAAQ7C,SAExBmD,eAAA,OAAKlD,UAAU,0BAAyBD,SAAA,CAC/B,IAAN6C,EAAU/C,cAAA,OAAKG,UAAU,QAAOD,SAAC,YAAgB,GAClDF,cAAA,OAAKG,UAAU,4BAA2BD,SACxCF,cAAA,MAAIG,UAAU,2BAA0BD,SAAE4C,EAAImD,UAEhD5C,eAAA,OAAKlD,UAAU,2BAA0BD,SAAA,CACvCF,cAAA,QAAMG,UAAU,WAAUD,SAAC,MAC3BF,cAAA,QAAMG,UAAU,QAAOD,SAAE4C,EAAIuK,QAC7BrN,cAAA,QAAMG,UAAU,SAAQD,SAAC,eAE3BF,cAAA,OAAKG,UAAU,iCAAgCD,SAAC,UAChDF,cAAA,OACEG,UAAU,6BACVmN,wBAAyB,CAAEC,OAAQzK,EAAI0K,eAEzCxN,cAAA,OAAKG,UAAU,4BAA2BD,SACxCF,cAACoG,IAAI,CACHjG,UAAU,0CACVkG,GAAG,SACHjG,QAAU6E,GAAUA,EAAMC,iBAAiBhF,SAC5C,qBA1BoC6C,MAkC9C,ECTQ0K,OA7BG/L,IAAe,IAAd,KAAE0L,GAAM1L,EACzB,OACE2B,eAAA,WAAAnD,SAAA,CACEF,cAAA,OACEG,UAAU,aACV4E,MAAO,CAAE,YAAa,UAAW,WAAY,cAE/C1B,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CACxBF,cAAA,OAAKG,UAAU,qBAAqB,WAAS,OAAO,iBAAe,IAAGD,SACpEF,cAAA,MAAIG,UAAU,8BAA6BD,SAAC,qBAE9CF,cAAA,OACEG,UAAU,aACV4E,MAAO,CAAE,YAAa,UAAW,WAAY,iBAGjD/E,cAAA,OAAKG,UAAU,YAAWD,SACxBF,cAAA,OAAKG,UAAU,MAAM4E,MAAO,CAAE,gBAAiB,QAAS7E,SACtDF,cAACmN,GAAO,CAACC,KAAMA,QAGnBpN,cAAA,OACEG,UAAU,aACV4E,MAAO,CAAE,YAAa,UAAW,WAAY,gBAEvC,ECzBd,MAAM2I,GAAU,CACd,CACEzH,MAAO,SACPoH,MAAO,MACPG,YAAa,gDAEf,CACEvH,MAAO,SACPoH,MAAO,MACPG,YACE,gNAEJ,CACEvH,MAAO,SACPoH,MAAO,MACPG,YACE,wHAEJ,CACEvH,MAAO,SACPoH,MAAO,MACPG,YACE,6MAEJ,CACEvH,MAAO,SACPoH,MAAO,OACPG,YACE,uFA2DSG,OAvDKA,IAEhBtK,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,YACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,2CAG1CF,cAAA,OAAAE,SAAK,gPAMLF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,kCAG1CmD,eAAA,OAAAnD,SAAA,CAAK,4UAMHF,cAAA,SAAM,ifASNA,cAAA,SAAM,iCAENA,cAAA,SAAM,mDAGRA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,kBAC1CmD,eAAA,OAAAnD,SAAA,CAAK,4IAGHF,cAAA,SAAM,sIAKRA,cAACyN,GAAS,CAACL,KAAMM,QCKRE,OAtFOA,IAElBvK,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,aACtBF,cAAA,KAAGG,UAAU,aAAYD,SAAC,iJAK1BF,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,iBAAgBD,SAC7BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,yBACNiE,MAAM,sGACNC,iBAAe,MAGnBnK,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,iBAAgBD,SAC7BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,yCACNiE,MAAM,sGACNC,iBAAe,MAInBnK,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,iBAAgBD,SAC7BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,8BACNiE,MAAM,sGACNC,iBAAe,MAGnBnK,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,iBAAgBD,SAC7BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,gEACJwF,MAAM,kBACNiE,MAAM,sGACNC,iBAAe,MAGnB9G,eAAA,OAAKlD,UAAU,GAAED,SAAA,CACfF,cAAA,KAAG+E,MAAO,CAAE8I,OAAQ,QAAS3N,SAAC,qIAI9BF,cAAA,OACES,IAAG,wCACHC,IAAI,OACJqE,MAAO,CAAE2C,MAAO,aAGpB1H,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,iBAAgBD,SAC7BF,cAAA,UACE0H,MAAM,MACND,OAAO,MACPhH,IAAI,4CACJwF,MAAM,YACNiE,MAAM,sGACNC,iBAAe,MAGnBnK,cAACqJ,EAAc,OC0MNyE,OA1RgBA,IAE3BzK,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,4BAEtBmD,eAAA,OAAK0K,MAAM,YAAY/C,GAAG,mBAAkB9K,SAAA,CAC1CmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,aAAY9K,SAC1CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,eACf,gBAAc,OACd,gBAAc,cAAa9N,SAC5B,wBAIHF,cAAA,OACEgL,GAAG,cACH+C,MAAM,mCACN,kBAAgB,aAChB,iBAAe,oBAAmB7N,SAElCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,KAAAE,SAAG,kIAIHF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,oCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,oCACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,oBAAmBD,SAChCF,cAAA,KAAGG,UAAU,OAAMD,SAAC,+BAEtBF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCF,cAAA,KAAAE,SAAG,yCAKXmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,aAAY9K,SAC1CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,eACf,gBAAc,OACd,gBAAc,cAAa9N,SAC5B,wBAIHF,cAAA,OACEgL,GAAG,cACH+C,MAAM,mCACN,kBAAgB,aAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,KAAAE,SAAG,+GAIHF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,4CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,4CACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,oBAAmBD,SAChCF,cAAA,KAAGG,UAAU,OAAMD,SAAC,iCAEtBF,cAAA,OAAKG,UAAU,oBAAmBD,SAChCF,cAAA,KAAGG,UAAU,OAAMD,SAAC,kCAK5BmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,eAAc9K,SAC5CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,iBACf,gBAAc,OACd,gBAAc,gBAAe9N,SAC9B,wBAIHF,cAAA,OACEgL,GAAG,gBACH+C,MAAM,mCACN,kBAAgB,eAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBmD,eAAA,KAAAnD,SAAA,CAAG,4GAGDmD,eAAA,MAAAnD,SAAA,CACEF,cAAA,MAAAE,SAAI,YACJF,cAAA,MAAAE,SAAI,aACJF,cAAA,MAAAE,SAAI,OACJF,cAAA,MAAAE,SAAI,SACJF,cAAA,MAAAE,SAAI,WACJF,cAAA,MAAAE,SAAI,cAGRF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,mCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,mCACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,wCAS1BkD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,cAAa9K,SAC3CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,gBACf,gBAAc,OACd,gBAAc,eAAc9N,SAC7B,wBAIHF,cAAA,OACEgL,GAAG,eACH+C,MAAM,mCACN,kBAAgB,cAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAIG,UAAU,YAAWD,SAAC,gNAKpB,IACNF,cAAA,SAAM,IAACA,cAAA,SACPA,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,6CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,6CACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,wCAS1BkD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,aAAY9K,SAC1CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,eACf,gBAAc,OACd,gBAAc,cAAa9N,SAC5B,wBAIHF,cAAA,OACEgL,GAAG,cACH+C,MAAM,mCACN,kBAAgB,aAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBmD,eAAA,KAAAnD,SAAA,CAAG,6ZAOkC,IAAI,4EACmB,IAAI,eACpD,IAAI,wJACsF,IACnG,OAEHF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,8CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,8CACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,OAAAE,SACEF,cAAA,QAAAE,SAAM,wDAERF,cAAA,OAAAE,SACEF,cAAA,QAAAE,SAAM,+CAOlBF,cAACqJ,EAAc,OCiEN4E,OAtVcA,KAC3B,MAAOtD,EAAMC,GAAWnL,mBAAS,MAWjC,OACE4D,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,0BAEtBmD,eAAA,OAAK0K,MAAM,YAAY/C,GAAG,mBAAkB9K,SAAA,CAC1CmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,aAAY9K,SAC1CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,eACf,gBAAc,OACd,gBAAc,cAAa9N,SAC5B,wBAIHF,cAAA,OACEgL,GAAG,cACH+C,MAAM,mCACN,kBAAgB,aAChB,iBAAe,oBAAmB7N,SAElCmD,eAAA,OAAK0K,MAAM,iCAAgC7N,SAAA,CACzCF,cAAA,KAAAE,SAAG,wHAIHF,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,8CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,8CACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,oBAAmBD,SAChCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAAE,SAAM,6BACNF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAlEH0K,OAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GA6DkBC,CAAgB,2BAA4B,GAC7C3K,SAES,IAATyK,EAAa,UAAY,sBAOtCtH,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,eAAc9K,SAC5CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,iBACf,gBAAc,OACd,gBAAc,gBAAe9N,SAC9B,wBAIHF,cAAA,OACEgL,GAAG,gBACH+C,MAAM,mCACN,kBAAgB,eAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CAAC,+DAE1BmD,eAAA,KAAAnD,SAAA,CAAG,uCACmCF,cAAA,SAAM,wDACWA,cAAA,SAAM,+BAG7DA,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,iCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,iCACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,wCAS1BkD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,cAAa9K,SAC3CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,gBACf,gBAAc,OACd,gBAAc,eAAc9N,SAC7B,wBAIHF,cAAA,OACEgL,GAAG,eACH+C,MAAM,mCACN,kBAAgB,cAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CAAC,4JAGGF,cAAA,SAAM,IAACA,cAAA,SACpCA,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SAAC,y4BA0BtBmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,cAAa9K,SAC3CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,gBACf,gBAAc,OACd,gBAAc,eAAc9N,SAC7B,wBAIHF,cAAA,OACEgL,GAAG,eACH+C,MAAM,mCACN,kBAAgB,cAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAIG,UAAU,YAAWD,SAAC,8EAGpB,IACNF,cAAA,SAAM,IAACA,cAAA,SACPA,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBmD,eAACgH,IAAU,CAAAnK,SAAA,CACTF,cAAA,KACEmD,KAAK,kDACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,kDACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,0BAGdH,cAAA,KACEmD,KAAK,4CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,4CACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,yCAS1BkD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,cAAa9K,SAC3CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,gBACf,gBAAc,OACd,gBAAc,eAAc9N,SAC7B,wBAIHF,cAAA,OACEgL,GAAG,eACH+C,MAAM,mCACN,kBAAgB,cAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBmD,eAAA,MAAIlD,UAAU,YAAWD,SAAA,CAAC,yEAEd,IACVF,cAAA,UAAAE,SACEF,cAAA,KACEmD,KAAK,yCACLF,OAAO,SAAQ/C,SAChB,cAIC,IACNF,cAAA,SAAM,IAACA,cAAA,SACPA,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,iCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,iCACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,wCAS1BkD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBF,cAAA,MAAI+N,MAAM,mBAAmB/C,GAAG,cAAa9K,SAC3CF,cAAA,UACE+N,MAAM,mBACNC,KAAK,SACL,iBAAe,WACf,iBAAe,gBACf,gBAAc,OACd,gBAAc,eAAc9N,SAC7B,wBAIHF,cAAA,OACEgL,GAAG,eACH+C,MAAM,mCACN,kBAAgB,cAChB,iBAAe,qBAAoB7N,SAEnCmD,eAAA,OAAK0K,MAAM,iBAAgB7N,SAAA,CACzBmD,eAAA,MAAIlD,UAAU,YAAWD,SAAA,CAAC,gIAEmC,OACvD,IACNF,cAAA,SAAM,IAACA,cAAA,SACPA,cAAA,OAAKG,UAAU,kCAAiCD,SAC9CF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,2CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,2CACJC,IAAI,gBACJiH,QAAQ,OACRxH,UAAU,2CAU5BH,cAACqJ,EAAc,MACX,ECkIK6E,OAldOA,IAElB7K,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,oDACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,kBAC1CmD,eAAA,KAAAnD,SAAA,CAAG,qPAIsCF,cAAA,SAAM,8HAI/CA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,8DAG1CF,cAAA,KAAAE,SAAG,6HAIHF,cAAA,KAAAE,SAAG,uEACHF,cAAA,MAAIG,UAAU,QAAOD,SAAC,4EAGtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,wCAG1CmD,eAAA,KAAAnD,SAAA,CAAG,8CAEDF,cAAA,KAAGmD,KAAK,2BAA0BjD,SAAC,gCAErCF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,2CAG1CF,cAAA,KAAAE,SAAG,oFAIHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,8BAA8B,mBAAcD,IAAI,WAAUhD,SAChEF,cAAA,OACES,IAAI,8BACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,gCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,gCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,oDAG1CF,cAAA,KAAAE,SAAG,+IAIHmD,eAAA,KAAAnD,SAAA,CACEF,cAAA,UAAAE,SAAQ,YAAgB,yEAG1BF,cAAA,KAAAE,SAAG,6DACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,gCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,gCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,kCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,kCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,KAAAE,SAAG,4EAGHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,kCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,kCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,MAAIlD,UAAU,6BAA4BD,SAAA,CAAC,mDACOF,cAAA,SAAM,8EAGxDA,cAAA,KAAAE,SAAG,oHAIHF,cAAA,KAAAE,SACEF,cAAA,UAAAE,SAAQ,aAEVF,cAAA,KAAAE,SAAG,0CACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,kCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,kCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,6BAA4BD,SAAC,6CAG3CF,cAAA,KAAAE,SAAG,6IAIHF,cAAA,KAAAE,SAAG,kCACHF,cAAA,MAAAE,SACEF,cAAA,MAAAE,SAAI,sDAENF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,qCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,qCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,MAAAnD,SAAA,CACEF,cAAA,MAAAE,SAAI,iEACJF,cAAA,MAAAE,SAAI,sDACJF,cAAA,MAAAE,SAAI,gDAENF,cAAA,KAAAE,SACEF,cAAA,UAAAE,SAAQ,YAEVF,cAAA,KAAAE,SAAG,6EACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,+BAA+B,mBAAcD,IAAI,WAAUhD,SACjEF,cAAA,OACES,IAAI,+BACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,kCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,kCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,MAAAnD,SAAA,CACEF,cAAA,MAAAE,SAAI,0DACJF,cAAA,MAAAE,SAAI,sEAENF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,gCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,gCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAAE,SACEF,cAAA,MAAAE,SAAI,qDAENF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,4BAA4B,mBAAcD,IAAI,WAAUhD,SAC9DF,cAAA,OACES,IAAI,4BACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,aAAYD,SAAC,sEAG3BmD,eAAA,KAAAnD,SAAA,CACEF,cAAA,UAAAE,SAAQ,WAAe,iLAIzBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,iEAG1CF,cAAA,KAAAE,SAAG,+FAIHF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,8EAI1CF,cAAA,KAAAE,SAAG,oHAIHF,cAAA,KAAAE,SACEF,cAAA,UAAAE,SAAQ,YAEVF,cAAA,KAAAE,SAAG,0CACHF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,8CAG1CF,cAAA,KAAAE,SAAG,yDACHF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,iCAG1CF,cAAA,KAAAE,SAAG,kGAIHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,+BAA+B,mBAAcD,IAAI,WAAUhD,SACjEF,cAAA,OACES,IAAI,+BACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,KAAAE,SAAG,6EACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,+BAA+B,mBAAcD,IAAI,WAAUhD,SACjEF,cAAA,OACES,IAAI,+BACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,KAAAE,SAAG,iDACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,4BAA4B,mBAAcD,IAAI,WAAUhD,SAC9DF,cAAA,OACES,IAAI,4BACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,KAAAE,SAAG,uDACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KAAGmD,KAAK,4BAA4B,mBAAcD,IAAI,WAAUhD,SAC9DF,cAAA,OACES,IAAI,4BACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,KAAAE,SAAG,4IAIHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,kCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,kCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,KAAAE,SAAG,yCACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,gCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,gCACJC,IAAI,SACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,6EAGRmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,2EAGRF,cAACqJ,EAAc,OCnNN8E,OA9NaA,KAC1B,MAAOxD,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,iBACtBmD,eAAA,MAAIlD,UAAU,4BAA2BD,SAAA,CACtC,IAAI,yCACkC,OAEzCF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAAsB,oHAEIF,cAAA,SAAM,oBAC7D,IACbA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,+BAAiC,2DACbF,cAAA,SAAM,sBAC3DA,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,2EACEF,cAAA,SAC3DA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,sBAC1CmD,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,mCAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,8CAGpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,8CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAAE,SAAM,2BACNF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,+BAA2B,GAAG3K,SAEnD,IAATyK,EAAa,UAAY,kBAKlCtH,eAAA,OAAKlD,UAAU,YAAWD,SAAA,CAAC,0CAEzBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,4BACpCmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,2BACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAAnD,SAAA,2BAC6B,IAACF,cAAA,SAAM,6BAGpCA,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,iDACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,kBAKlC3K,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,2KAG1C,IACrBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,mCAAqC,sDACrB,IACpDF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,wDAEnEmD,eAAA,OAAKlD,UAAU,oBAAmBD,SAAA,CAChCF,cAAA,KAAAE,SAAG,6CACHF,cAAA,SACAqD,eAAA,OAAAnD,SAAA,CACEmD,eAAA,QAAMlD,UAAU,OAAMD,SAAA,CAAC,+BAErBF,cAAA,QAAMG,UAAU,aAAYD,SAAC,iCAE/BF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EAAgB,kCAAmC,GACpD3K,SAES,IAATyK,EAAa,UAAY,eAIhC3K,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,0CACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,0CACJC,IAAI,UACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,uBAAuB,+EAGjEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,sCAE7B,4EAEPF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,oBAAsB,uHAEhB,IAC1CF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,wBAA0B,kGAExC,IACtBF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,gBAAkB,WACtDF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,mBAAmB,iBAC9C,IACfF,cAAA,KACEG,UAAU,SACVgD,KAAK,wDACLF,OAAO,SAAQ/C,SAChB,cAGDF,cAAA,SAAM,qBACa,IACnBA,cAAA,KACEG,UAAU,SACVgD,KAAK,4DACLF,OAAO,SAAQ/C,SAChB,kBAGDF,cAAA,SAgEAA,cAACqJ,EAAc,MACX,EC7KK+E,OAhDkBA,IAE7B/K,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,mBACtBF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,yBAAyB,qGAE9CF,cAAA,SACrBqD,eAAA,QAAMlD,UAAU,sBAAqBD,SAAA,CAAC,YAC1B,IAAI,wBACT,6CAEPmD,eAAA,OAAKlD,UAAU,6BAA4BD,SAAA,CACzCF,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,mBAEZH,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,sBAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,qBAAqB,cAAO,IACtEF,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,+CAE7B,uCAC6BF,cAAA,SACpCA,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAAA,OACES,IAAI,oCACJC,IAAI,MACJP,UAAU,0BAGdH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,oBAAoB,+GAG9DF,cAACqJ,EAAc,OC8BNgF,OAzEoBA,KACjC,MAAO1D,EAAMC,GAAWnL,mBAAS,MAE3BoL,EAAkBC,MAAOC,EAAQC,KACrC,UACQC,UAAUC,UAAUC,UAAUJ,GACpCH,EAAQI,EACV,CAAE,MAAOI,GACPR,EAAQ,kBACV,GAGF,OACEvH,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,0BACtBF,cAAA,MAAIG,UAAU,qBAAoBD,SAAC,sBAAsB,uCAEzDF,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,sBACvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IAAMyK,EAAgB,oBAAqB,GAAG3K,SAE7C,IAATyK,EAAa,UAAY,cAG1B,wFAGN3K,cAAA,OAAKG,UAAU,yBAAwBD,SACrCmD,eAAA,OAAAnD,SAAA,CACEF,cAAA,QAAMG,UAAU,OAAMD,SAAC,8FAIvBF,cAAA,QACEG,UAAoB,IAATwK,EAAa,kBAAoB,OAC5CvK,QAASA,IACPyK,EACE,4FACA,GAEH3K,SAES,IAATyK,EAAa,UAAY,cAG1B,uBAEN3K,cAAA,QAAMG,UAAU,oBAAmBD,SAAC,6BAA+B,qDAEnEF,cAAA,UAAQG,UAAU,SAAQD,SAAC,gDAG3BF,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,kBAAkB,uQAK5DF,cAAA,UAAQG,UAAU,SAAQD,SAAC,4CAG3BF,cAACqJ,EAAc,MACX,EChBKiF,OArDAA,IAEXjL,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,yBAAyB,qBAE/CF,cAAA,SAAM,wBACUA,cAAA,SAChBA,cAAA,OAAKG,UAAU,SACfH,cAAA,OAAKG,UAAU,SACfH,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,UAAU,4EACgBF,cAAA,SACpEA,cAAA,MAAIG,UAAU,4BAA2BD,SAAC,cAAc,6CAClCF,cAAA,SACtBqD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,2XAQRmD,eAAA,OAAKlD,UAAU,0BAA0B4J,KAAK,QAAO7J,SAAA,CACnDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACuK,KAAU,CAACvD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,eAC7B,gFAIRmD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,uDACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,4BAEtBmD,eAAC+C,IAAI,CAACC,GAAG,iCAAiClG,UAAU,YAAWD,SAAA,CAC7DF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,4BC2Ff0L,OAxISA,IAEpBvI,eAAA,OACElD,UAAU,gBACV,WAAS,aACT,iBAAe,MACf,oBAAkB,OAAMD,SAAA,CAExBF,cAAA,MAAIG,UAAU,QAAOD,SAAC,qBACtBmD,eAAA,OAAKlD,UAAU,2BAA2B4J,KAAK,QAAO7J,SAAA,CACpDmD,eAAA,OAAKlD,UAAU,OAAMD,SAAA,CACnBF,cAACgK,IAAmB,CAAChD,KAAM,KAAM,IAAChH,cAAA,UAAAE,SAAQ,YACtC,4CAgBE,2CACyBF,cAAA,SAAM,+BACzCA,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD1B,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,qBACLF,cAAA,UAAAE,SAAQ,SAAa,WAEpCmD,eAAA,KAAAnD,SAAA,CAAG,6CACyCF,cAAA,UAAAE,SAAQ,UAAc,+FAE1CF,cAAA,UAAAE,SAAQ,eAAmB,KACjDF,cAAA,UAAAE,SAAQ,gBAAoB,QAAKF,cAAA,UAAAE,SAAQ,cAAkB,kBAE7DmD,eAAA,KAAAnD,SAAA,CAAG,oEAEDF,cAAA,UAAAE,SAAQ,UAAc,sJAIxBF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,wCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,wCACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD/E,cAAA,KAAGG,UAAU,OAAMD,SAAC,6BACpBF,cAAA,KAAAE,SAAG,yDACHF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,sCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,sCACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAMpBH,cAAA,OAAKG,UAAU,aAAa4E,MAAO,CAAE,YAAa,aAClD1B,eAAA,KAAGlD,UAAU,OAAMD,SAAA,CAAC,yCACeF,cAAA,UAAAE,SAAQ,mBAE3CmD,eAAA,KAAAnD,SAAA,CAAG,kGAEuBF,cAAA,UAAAE,SAAQ,0BAA8B,4EAGhEF,cAAA,OAAKG,UAAU,6BAA4BD,SACzCF,cAACoK,IAAmB,CAAAlK,SAClBF,cAACqK,IAAU,CAAAnK,SACTF,cAAA,KACEmD,KAAK,yCACL,mBACAD,IAAI,WAAUhD,SAEdF,cAAA,OACES,IAAI,yCACJC,IAAI,eACJiH,QAAQ,OACRxH,UAAU,gCAMpBkD,eAAA,KAAAnD,SAAA,CAAG,8EAEIF,cAAA,UAAAE,SAAQ,oBAEfmD,eAAA,OAAKlD,UAAU,wBAAuBD,SAAA,CACpCmD,eAAC+C,IAAI,CACHC,GAAG,wCACHlG,UAAU,gBAAeD,SAAA,CAEzBF,cAAA,KAAAE,SAAG,aACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,qBAEtBmD,eAAC+C,IAAI,CACHC,GAAG,0CACHlG,UAAU,YAAWD,SAAA,CAErBF,cAAA,KAAAE,SAAG,SACHF,cAAA,KAAGG,UAAU,OAAMD,SAAC,8BC8GfqO,OAlMEA,IAEblL,eAACmL,IAAK,CAACC,KAAK,IAAIC,QAAS1O,cAAC2O,EAAQ,IAAIzO,SAAA,CACpCF,cAACwO,IAAK,CAACC,KAAK,IAAIvI,OAAK,EAACwI,QAAS1O,cAAC4O,EAAiB,MACjD5O,cAACwO,IAAK,CACJC,KAAK,uCACLC,QAAS1O,cAAC4O,EAAiB,MAE7B5O,cAACwO,IAAK,CAACC,KAAK,gCAAgCC,QAAS1O,cAAC6O,GAAU,MAChE7O,cAACwO,IAAK,CAACC,KAAK,iCAAiCC,QAAS1O,cAAC8O,GAAW,MAClE9O,cAACwO,IAAK,CAACC,KAAK,gCAAgCC,QAAS1O,cAAC+O,GAAU,MAChE/O,cAACwO,IAAK,CACJC,KAAK,mCACLC,QAAS1O,cAACgP,GAAY,MAExBhP,cAACwO,IAAK,CACJC,KAAK,iDACLC,QAAS1O,cAACiP,GAAwB,MAEpCjP,cAACwO,IAAK,CAACC,KAAK,gCAAgCC,QAAS1O,cAACkP,GAAU,MAChElP,cAACwO,IAAK,CACJC,KAAK,0CACLC,QAAS1O,cAACqL,GAAe,MAE3BrL,cAACwO,IAAK,CACJC,KAAK,sCACLC,QAAS1O,cAACmP,GAAe,MAE3BnP,cAACwO,IAAK,CACJC,KAAK,qCACLC,QAAS1O,cAACoP,GAAc,CAAC5D,UAAU,mBAErCxL,cAACwO,IAAK,CACJC,KAAK,qCACLC,QAAS1O,cAACoP,GAAc,CAAC5D,UAAU,iBAErCxL,cAACwO,IAAK,CACJC,KAAK,qCACLC,QAAS1O,cAACoP,GAAc,CAAC5D,UAAU,iBAErCxL,cAACwO,IAAK,CACJC,KAAK,kCACLC,QAAS1O,cAACoP,GAAc,CAAC5D,UAAU,cAErCxL,cAACwO,IAAK,CACJC,KAAK,sCACLC,QAAS1O,cAACqP,GAAe,MAE3BrP,cAACwO,IAAK,CACJC,KAAK,uCACLC,QAAS1O,cAACsP,GAAgB,MAE5BtP,cAACwO,IAAK,CACJC,KAAK,4CACLC,QAAS1O,cAACuP,GAAoB,MAEhCvP,cAACwO,IAAK,CACJC,KAAK,0CACLC,QAAS1O,cAACwP,GAAmB,MAE/BxP,cAACwO,IAAK,CACJC,KAAK,wCACLC,QAAS1O,cAACyP,GAAkB,MAE9BzP,cAACwO,IAAK,CACJC,KAAK,iDACLC,QAAS1O,cAAC0P,GAAyB,MAErC1P,cAACwO,IAAK,CACJC,KAAK,kDACLC,QAAS1O,cAAC2P,GAA0B,MAEtC3P,cAACwO,IAAK,CACJC,KAAK,+CACLC,QAAS1O,cAACiM,GAAmB,MAE/BjM,cAACwO,IAAK,CACJC,KAAK,yCACLC,QAAS1O,cAAC4P,GAAkB,MAE9B5P,cAACwO,IAAK,CACJC,KAAK,iDACLC,QAAS1O,cAAC6P,GAAyB,MAErC7P,cAACwO,IAAK,CACJC,KAAK,mDACLC,QAAS1O,cAAC8P,GAA2B,MAEvC9P,cAACwO,IAAK,CACJC,KAAK,8CACLC,QAAS1O,cAAC+P,GAAsB,MAElC/P,cAACwO,IAAK,CACJC,KAAK,sDACLC,QAAS1O,cAACgQ,GAA8B,MAE1ChQ,cAACwO,IAAK,CACJC,KAAK,+CACLC,QAAS1O,cAACiQ,GAAwB,MAEpCjQ,cAACwO,IAAK,CACJC,KAAK,oDACLC,QAAS1O,cAACyM,GAA2B,MAEvCzM,cAACwO,IAAK,CACJC,KAAK,iDACLC,QAAS1O,cAAC0M,GAA2B,MAEvC1M,cAACwO,IAAK,CACJC,KAAK,4CACLC,QAAS1O,cAACkQ,GAAsB,MAElClQ,cAACwO,IAAK,CACJC,KAAK,qDACLC,QAAS1O,cAAC2M,GAAuB,MAEnC3M,cAACwO,IAAK,CACJC,KAAK,gDACLC,QAAS1O,cAAC4M,GAAkB,MAE9B5M,cAACwO,IAAK,CACJC,KAAK,mDACLC,QAAS1O,cAAC6M,GAAqB,MAEjC7M,cAACwO,IAAK,CACJC,KAAK,wDACLC,QAAS1O,cAAC8M,GAAuB,MAEnC9M,cAACwO,IAAK,CACJC,KAAK,8CACLC,QAAS1O,cAACmQ,GAAsB,MAElCnQ,cAACwO,IAAK,CACJC,KAAK,yDACLC,QAAS1O,cAACoQ,GAA8B,MAE1CpQ,cAACwO,IAAK,CACJC,KAAK,iEACLC,QAAS1O,cAACqQ,GAAqC,MAEjDrQ,cAACwO,IAAK,CACJC,KAAK,6CACLC,QAAS1O,cAACsQ,GAAqB,MAEjCtQ,cAACwO,IAAK,CACJC,KAAK,yCACLC,QAAS1O,cAACuQ,GAAiB,MAE7BvQ,cAACwO,IAAK,CACJC,KAAK,uCACLC,QAAS1O,cAACwQ,GAAgB,MAE5BxQ,cAACwO,IAAK,CACJC,KAAK,mCACLC,QAAS1O,cAACyQ,GAAkB,MAG9BzQ,cAACwO,IAAK,CACJC,KAAK,kDACLC,QAAS1O,cAAC8N,GAAsB,MAElC9N,cAACwO,IAAK,CACJC,KAAK,gDACLC,QAAS1O,cAACiO,GAAoB,MAEhCjO,cAACwO,IAAK,CACJC,KAAK,iCACLC,QAAS1O,cAACkO,GAAa,MAEzBlO,cAACwO,IAAK,CACJC,KAAK,oCACLC,QAAS1O,cAACoP,GAAc,CAAC5D,UAAU,gBAErCxL,cAACwO,IAAK,CACJC,KAAK,gDACLC,QAAS1O,cAACmO,GAAmB,MAE/BnO,cAACwO,IAAK,CACJC,KAAK,8CACLC,QAAS1O,cAACoO,GAAwB,MAEpCpO,cAACwO,IAAK,CACJC,KAAK,kDACLC,QAAS1O,cAACqO,GAA0B,MAEtCrO,cAACwO,IAAK,CAACC,KAAK,iCAAiCC,QAAS1O,cAAC0Q,GAAU,MACjE1Q,cAACwO,IAAK,CACJC,KAAK,yCACLC,QAAS1O,cAAC2Q,GAAkB,SCzNrBC,OAfCA,IAEZ5Q,cAAAC,WAAA,CAAAC,SACEmD,eAACwN,IAAU,CAAA3Q,SAAA,CACTF,cAACqI,EAAkB,IACnBhF,eAACyN,IAAM,CAAA5Q,SAAA,CACJqO,KAEDvO,cAACwO,IAAK,CAACC,KAAK,IAAIC,QAAS1O,cAACmI,EAAQ,cC+B7B4I,OAjCHA,KACVrR,qBAAU,KACRsR,IAAIC,KAAK,CACPC,SAAU,MACV,GACD,IAGDlR,cAACyB,EAAY,CAAAvB,SACXmD,eAAApD,WAAA,CAAAC,SAAA,CACEmD,eAAC+E,IAAM,CAAAlI,SAAA,CACLF,cAAA,SAAAE,SAAO,aACPF,cAAA,QAAM0D,KAAK,cAAcyN,QAAQ,aACjCnR,cAAA,QACE0D,KAAK,WACLyN,QAAQ,mJAGZnR,cAACV,EAAW,IACZU,cAACoR,IAAc,CACbC,SAAS,YACTC,UAAW,IACXC,iBAAe,EACfC,cAAY,EACZC,cAAY,EACZC,WAAS,IAEX1R,cAAC4Q,GAAO,UC3BDe,OAZSC,IAClBA,GAAeA,aAAuBC,UACxC,6BAAqBC,MAAKpQ,IAAkD,IAAjD,OAAEqQ,EAAM,OAAEC,EAAM,OAAEC,EAAM,OAAEC,EAAM,QAAEC,GAASzQ,EACpEqQ,EAAOH,GACPI,EAAOJ,GACPK,EAAOL,GACPM,EAAON,GACPO,EAAQP,EAAY,GAExB,E,mCCHaQ,OAJK,CAClB/Q,QCYF,MAAMgR,GAAoB,CACxBC,IAAK,OACLC,Q,QAAAA,EACAC,UAAW,CAAC,SAIRC,GAAmBC,aAAgB,IACpCN,GACH/Q,KAAMsR,aAAeN,GAAmBD,GAAY/Q,QAGzCuR,GAAQC,YAAe,CAClCC,QAASL,GACTM,WAAaC,GACXA,EAAqB,CACnBC,kBAAmB,CACjBC,eAAgB,CAACC,KAAOC,KAAWC,KAAOC,KAASC,KAAOC,WAKzCC,aAAab,I,kBCzBtC,MAAMc,GAAOA,IACX1T,cAACgC,IAAQ,CAAC4Q,MAAOA,GAAM1S,SACrBF,cAAC+Q,GAAG,MAIR4C,iBAAO3T,cAAC0T,GAAI,IAAKlN,SAASoN,eAAe,SAKzCjC,I", "file": "static/js/main.a9a8a195.chunk.js", "sourcesContent": ["import React, { useEffect, useState } from \"react\";\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  // Top: 0 takes us all the way back to the top of the page\n  // Behavior: smooth keeps it smooth!\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: \"smooth\",\n    });\n  };\n\n  useEffect(() => {\n    // Button is displayed after scrolling for 500 pixels\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 500) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", toggleVisibility);\n\n    return () => window.removeEventListener(\"scroll\", toggleVisibility);\n  }, []);\n\n  return (\n    <>\n      {isVisible && (\n        <div>\n          <button className=\"scroll-top \" onClick={scrollToTop}>\n            <img src=\"../assets/img/chevron-w.png\" alt=\"icon\" />\n          </button>\n        </div>\n      )}\n    </>\n  );\n}\n", "import { getAuth } from \"firebase/auth\";\nimport { initializeApp } from \"firebase/app\";\n\nconst firebaseConfig = {\n  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,\n  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.REACT_APP_FIREBASE_APP_ID,\n  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,\n};\nconst app = initializeApp(firebaseConfig);\nexport const auth = getAuth(app);\nexport { app as default };\n", "import { createContext, useContext, useState } from \"react\";\nimport {\n  GoogleAuthProvider,\n  signInWithPopup,\n  FacebookAuthProvider,\n} from \"firebase/auth\";\nimport { auth } from \"../service/FireBase\";\n\nexport const AuthContext = createContext();\n\nexport function useAuth() {\n  return useContext(AuthContext);\n}\n\nexport function AuthProvider({ children }) {\n  const theme = localStorage.getItem(\"theme\");\n  const [darkTheme, setDarkTheme] = useState(theme === \"\");\n\n  function googleSignIn() {\n    const googleAuthProvider = new GoogleAuthProvider();\n    return signInWithPopup(auth, googleAuthProvider);\n  }\n\n  function facebookSignIn() {\n    const facebookAuthProvider = new FacebookAuthProvider();\n    return signInWithPopup(auth, facebookAuthProvider);\n  }\n\n  return (\n    <AuthContext.Provider\n      value={{ googleSignIn, facebookSignIn, setDarkTheme, darkTheme }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n}\n", "import React from \"react\";\n\nconst socialList = [\n  {\n    iconName: \"socicon-facebook\",\n    link: \"https://www.facebook.com/githubit\",\n    className: \"facebook\"\n  },\n  {\n    iconName: \"socicon-linkedin\",\n    link: \"https://linkedin.com/company/githubit\",\n    className: \"linkedin-icon\"\n  },\n];\n\nconst SocialTwo = () => {\n  return (\n    <>\n      {socialList.map((val, i) => (\n        <a\n          className={`ptf-social-icon ptf-social-icon--style-3 ${val.className}`}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          href={val.link}\n          key={i}\n        >\n          <i className={val.iconName} />\n        </a>\n      ))}\n    </>\n  );\n};\n\nexport default SocialTwo;\n", "import React from \"react\";\nimport SocialTwo from \"../../Social/SocialTwo\";\n\nconst CopyRight = () => {\n  return (\n    <div className=\"row align-items-center justify-content-center\">\n      <div className=\"col-12 col-md text-md-center text-lg-center\">\n        <p className=\"ptf-footer-copyright has-black-color\">\n          ©{new Date().getFullYear()} <span>Githubit</span>. All Rights\n          Reserved.\n        </p>\n      </div>\n      <div className=\"col-12 col-lg text-md-center text-lg-center mt-4\">\n        <div className=\"ptf-footer-socials has-black-color \">\n          <SocialTwo />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CopyRight;\n", "import { createSlice } from '@reduxjs/toolkit';\n\nconst initialState = {\n  user: null,\n};\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    setUserData(state, action) {\n      const { payload } = action;\n      state.user = payload;\n    },\n    clearUser(state) {\n      state.user = null;\n    },\n  },\n});\n\nexport const { setUserData, clearUser } = authSlice.actions;\nexport default authSlice.reducer;\n", "import React from \"react\";\nimport { shallowEqual, useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { clearUser } from \"../../redux/slices/auth\";\n\nconst SocialShare = [\n  {\n    iconName: \"socicon-linkedin\",\n    link: \"https://www.linkedin.com/company/githubit\",\n    iconClass: \"linkedin-icon\",\n  },\n  {\n    iconName: \"socicon-facebook\",\n    link: \"https://facebook.com/githubit\",\n    iconClass: \"facebook\",\n  },\n];\n\nconst Social = ({ closeMenu = () => {} }) => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.auth, shallowEqual);\n\n  const logoutUser = (event) => {\n    event.preventDefault();\n    localStorage.removeItem(\"token\");\n    dispatch(clearUser());\n    closeMenu();\n    navigate(\"/\");\n  };\n\n  return (\n    <div className=\"ptf-offcanvas-menu__socials align-items-center\">\n      {SocialShare.map((icon, i) => (\n        <a\n          className={`ptf-social-icon ptf-social-icon--style-4 ${icon.iconClass}`}\n          href={icon.link}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          key={i}\n        >\n          <i className={icon.iconName}></i>\n        </a>\n      ))}\n      {user && (\n        <div\n          className=\"ptf-offcanvas-menu__header d-flex justify-content-end\"\n          style={{ flexGrow: 1 }}\n        >\n          <div className=\"ptf-language-switcher\">\n            <div className=\"ptf-submit-button\">\n              <a href=\"/logout\" onClick={logoutUser}>\n                Logout\n              </a>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Social;\n", "export const menuContent = [\n  // {\n  //   name: \"Activate project\",\n  //   routerPath: \"/activator\",\n  // },\n  {\n    name: \"About us\",\n    routerPath: \"/about-us\",\n  },\n  // {\n  //   name: \"Contact\",\n  //   routerPath: \"/contact\",\n  // },\n  {\n    name: \"Career\",\n    routerPath: \"/career\",\n  },\n  // {\n  //   name: \"Open ticket\",\n  //   routerPath: \"/new-ticket\",\n  // },\n];\n", "export const menuContentLogin = [\n  // {\n  //   name: \"Activate project\",\n  //   routerPath: \"/activator\",\n  // },\n  {\n    name: \"About us\",\n    routerPath: \"/about-us\",\n  },\n  // {\n  //   name: \"Contact\",\n  //   routerPath: \"/contact\",\n  // },\n  {\n    name: \"Career\",\n    routerPath: \"/career\",\n  },\n  // {\n  //   name: \"Open ticket\",\n  //   routerPath: \"/login\",\n  // },\n];\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport \"react-pro-sidebar/dist/css/styles.css\";\nimport {\n  ProSidebar,\n  Menu,\n  MenuItem,\n  SubMenu,\n  SidebarContent,\n} from \"react-pro-sidebar\";\nimport Social from \"../../Social/Social\";\nimport { menuContent } from \"../../../fake-data/MobileData\";\nimport { menuContentLogin } from \"../../../fake-data/MobileDataLogin\";\nimport { shallowEqual, useSelector } from \"react-redux\";\n\nconst MobileMenu = ({ closeMenu }) => {\n  const { user } = useSelector((state) => state.auth, shallowEqual);\n\n  return (\n    <>\n      <div className=\"ptf-offcanvas-menu__navigation\">\n        <ProSidebar>\n          <SidebarContent>\n            <Menu className=\"sidebar-menu_wrapper\">\n              {(user ? menuContent : menuContentLogin).map((item, i) => (\n                <div key={i}>\n                  {item.dropDownItems?.length > 0 ? (\n                    <SubMenu title={item.name} key={i}>\n                      {item.dropDownItems?.map((val, index) => (\n                        <MenuItem key={index}>\n                          <Link to={val.routerPath}>{val.name}</Link>\n                        </MenuItem>\n                      ))}\n                    </SubMenu>\n                  ) : (\n                    <MenuItem className=\"sidebar-menu\">\n                      <Link to={item.routerPath}>{item.name}</Link>\n                    </MenuItem>\n                  )}\n                </div>\n              ))}\n            </Menu>\n          </SidebarContent>\n        </ProSidebar>\n      </div>\n\n      <div className=\"ptf-offcanvas-menu__footer\">\n        <p className=\"ptf-offcanvas-menu__copyright\">\n          @{new Date().getFullYear()} <span>Githubit</span>. All Rights\n          Reserved.\n        </p>\n        <Social closeMenu={closeMenu} />\n      </div>\n    </>\n  );\n};\n\nexport default MobileMenu;\n", "import React, {useEffect, useContext} from \"react\";\nimport {AuthContext} from \"../../context/AuthContext\";\nimport {HiOutlineSun} from \"react-icons/hi\";\nimport {BsFillMoonFill} from \"react-icons/bs\";\n\nconst ThemeChanger = () => {\n    const {darkTheme, setDarkTheme} = useContext(AuthContext);\n    const handleToggle = () => setDarkTheme(!darkTheme);\n\n    useEffect(() => {\n        if (darkTheme) {\n            document.documentElement.setAttribute(\"data-theme\", \"dark\");\n            localStorage.setItem(\"theme\", \"dark\");\n        } else {\n            document.documentElement.removeAttribute(\"data-theme\");\n            localStorage.setItem(\"theme\", \"light\");\n        }\n    }, [darkTheme]);\n\n    return (\n        <div>\n            {darkTheme !== undefined && (\n                <button\n                    className=\"dark-theme\"\n                    onClick={handleToggle}\n                >\n                    {!darkTheme ? (\n                        <BsFillMoonFill size={25} className=\"text-white\"/>\n                    ) : (\n                        <HiOutlineSun size={30} className=\"text-white\"/>\n                    )\n                    }\n                </button>\n            )}\n        </div>\n    );\n};\nexport default ThemeChanger;\n", "import React, { useState } from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport MobileMenu from \"./Menu/MobileMenu\";\nimport DarkMode from \"../DarkMode\";\nimport { FaUserEdit } from \"react-icons/fa\";\nimport { AiOutlineLogout } from \"react-icons/ai\";\nimport { shallowEqual, useDispatch, useSelector } from \"react-redux\";\nimport { clearUser } from \"../../redux/slices/auth\";\n\nconst HeaderDark = () => {\n  const dispatch = useDispatch();\n  const [menu, setMenu] = useState(false);\n  const { user } = useSelector((state) => state.auth, shallowEqual);\n  const handleClickMenu = () => {\n    setMenu(!menu);\n    setOpenUser(false);\n  };\n  const [openUser, setOpenUser] = useState(false);\n\n  const handleClickUser = () => setOpenUser(!openUser);\n\n  const handleLogout = () => dispatch(clearUser());\n\n  return (\n    <>\n      <header className=\"ptf-header--style-5 ptf-header--opaque\">\n        <div className={\"ptf-navbar ptf-navbar--main ptf-navbar--sticky\"}>\n          <div className=\"container-xxl\">\n            <div className=\"ptf-navbar-inner\">\n              <Link className=\"ptf-navbar-logo\" to=\"/\">\n                <h2 className=\"text-Logo\">Githubit</h2>\n              </Link>\n\n              <span className=\"d-flex justify-content-center align-items-center\">\n                <span>\n                  <DarkMode />\n                </span>\n                <div\n                  className=\"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle bar right\"\n                  onClick={handleClickMenu}\n                >\n                  <i className=\"lnir lnir-menu-alt-5\" />\n                </div>\n              </span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div\n        className={menu ? \"ptf-offcanvas-menu is-open\" : \"ptf-offcanvas-menu \"}\n      >\n        <div className=\"ptf-offcanvas-menu__header\">\n          {user ? (\n            <div className=\"user-container\">\n              <img\n                src=\"https://mdbcdn.b-cdn.net/img/Photos/Avatars/img (32).webp\"\n                className=\"rounded-circle\"\n                height=\"50\"\n                width=\"50\"\n                alt=\"Avatar\"\n                loading=\"lazy\"\n                onClick={handleClickUser}\n              />\n              <div\n                className=\"user-dropdown-menu\"\n                style={{ display: openUser ? \"block\" : \"none\" }}\n              >\n                <Link to=\"/user-profile\" className=\"dropdown-item\">\n                  <FaUserEdit className=\"dropdown-icon\" />{\" \"}\n                  <span>My profile</span>\n                </Link>\n\n                <Link to=\"/\" className=\"dropdown-item\" onClick={handleLogout}>\n                  <AiOutlineLogout className=\"dropdown-icon\" />\n                  <span>Logout</span>\n                </Link>\n              </div>\n            </div>\n          ) : null}\n          <span\n            className=\"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle\"\n            onClick={handleClickMenu}\n          >\n            <i className=\"lnir lnir-close\" />\n          </span>\n        </div>\n        <MobileMenu closeMenu={handleClickMenu} />\n      </div>\n    </>\n  );\n};\n\nexport default HeaderDark;\n", "import React from \"react\";\nimport { shallowEqual, useSelector } from \"react-redux\";\nimport { Link } from \"react-router-dom\";\nimport { menuContent } from \"../../fake-data/MobileData\";\nimport { menuContentLogin } from \"../../fake-data/MobileDataLogin\";\n\nconst FooterList = () => {\n  const { user } = useSelector((state) => state.auth, shallowEqual);\n\n  return (\n    <ul>\n      {(!user ? menuContent : menuContentLogin).map((item, i) => (\n        <li key={i}>\n          <Link to={item.routerPath}>{item.name}</Link>\n        </li>\n      ))}\n    </ul>\n  );\n};\n\nexport default FooterList;\n", "import React from \"react\";\nimport FooterList from \"../List/FooterList\";\nimport { Link } from \"react-router-dom\";\n\nconst FooterThreeDark = () => {\n  return (\n    <div className=\"row footer-back\">\n      <div className=\"col-12 col-lg\">\n        <div className=\"ptf-animated-block\" data-aos=\"fade\" data-aos-delay=\"0\">\n          <Link to=\"/\">\n            <h2 className=\"text-Logo\">Githubit</h2>\n          </Link>\n        </div>\n      </div>\n\n      <div className=\"col-12 col-lg\">\n        <div\n          className=\"ptf-animated-block\"\n          data-aos=\"fade\"\n          data-aos-delay=\"100\"\n        >\n          <div className=\"ptf-widget ptf-widget-links has-white-color\">\n            <FooterList />\n          </div>\n        </div>\n      </div>\n\n      <div className=\"col-12 col-lg\">\n        <div\n          className=\"ptf-animated-block\"\n          data-aos=\"fade\"\n          data-aos-delay=\"200\"\n        >\n          <div className=\"ptf-widget ptf-widget-text\">\n            <a className=\"fz-36 has-color\" href=\"mailto:<EMAIL>\">\n              <EMAIL>\n            </a>\n            <br />\n            <a className=\"fz-40 has-color\" href=\"tel:+12023401032\">\n              ****** 3401032\n            </a>\n            <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"0.625rem\" }} />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FooterThreeDark;\n", "import React from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport { Helm<PERSON> } from \"react-helmet\";\nimport CopyRight from \"../components/Footer/Copyright/CopyRight\";\nimport HeaderDark from \"../components/Header/HeaderDark\";\nimport FooterThreeDark from \"../components/Footer/FooterThreeDark\";\n\nconst NotFound = () => {\n  return (\n    <div>\n      <Helmet>\n        <title>GitHubit - 404 Page</title>\n      </Helmet>\n      {/* End Page SEO Content */}\n\n      <HeaderDark />\n      {/* End Header */}\n\n      <div className=\"ptf-main ptf-notFound\">\n        <div className=\"ptf-page ptf-page--404\">\n          <section className=\"min-vh-100\">\n            {/* <!--Spacer--> */}\n            <div\n              className=\"ptf-spacer\"\n              style={{ \"--ptf-xxl\": \"14.375rem\", \"--ptf-md\": \"7.1875rem\" }}\n            />\n            <div className=\"container-xxl\">\n              <div className=\"row\">\n                <div className=\"col-lg-5 offset-lg-1 order-lg-2 text-center\">\n                  {/* <!--Animated Block--> */}\n                  <div\n                    className=\"ptf-animated-block\"\n                    data-aos=\"fade\"\n                    data-aos-delay=\"300\"\n                  >\n                    <img\n                      src=\"../assets/img/root/404-robot.png\"\n                      alt=\"robot\"\n                      loading=\"lazy\"\n                    />\n                  </div>\n                  {/* <!--Spacer--> */}\n                  <div\n                    className=\"ptf-spacer\"\n                    style={{\n                      \"--ptf-lg\": \"5.625rem\",\n                      \"--ptf-md\": \"2.8125rem\",\n                    }}\n                  />\n                </div>\n                <div className=\"col-lg-6\">\n                  {/* <!--Animated Block--> */}\n                  <div\n                    className=\"ptf-animated-block\"\n                    data-aos=\"fade\"\n                    data-aos-delay=\"0\"\n                  >\n                    <h1 className=\"large-heading text-white\">\n                      Opps...! <br />\n                      Page not found\n                    </h1>\n                  </div>\n                  {/* <!--Spacer--> */}\n                  <div\n                    className=\"ptf-spacer\"\n                    style={{ \"--ptf-xxl\": \"3.75rem\", \"--ptf-md\": \"1.875rem\" }}\n                  />\n                  {/* <!--Animated Block--> */}\n                  <div\n                    className=\"ptf-animated-block \"\n                    data-aos=\"fade\"\n                    data-aos-delay=\"100\"\n                  >\n                    <p>You seem can’t to find the page you’re looking for.</p>\n                  </div>\n                  {/* <!--Spacer--> */}\n                  <div\n                    className=\"ptf-spacer\"\n                    style={{ \"--ptf-xxl\": \"5rem\", \"--ptf-md\": \"2.5rem\" }}\n                  />\n                  {/* <!--Animated Block--> */}\n                  <div\n                    className=\"ptf-animated-block \"\n                    data-aos=\"fade\"\n                    data-aos-delay=\"200\"\n                  >\n                    <Link to=\"/\" className=\"text-white\">\n                      Back to Home\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/* <!--Spacer--> */}\n            <div\n              className=\"ptf-spacer\"\n              style={{ \"--ptf-xxl\": \"14.375rem\", \"--ptf-md\": \"7.1875rem\" }}\n            />\n          </section>\n        </div>\n      </div>\n\n      {/*=============================================\n        Start Footer\n        ============================================== */}\n      <footer className=\"ptf-footer ptf-footer--style-3\">\n        <div className=\"container-xxl\">\n          <div className=\"ptf-footer__top\">\n            <FooterThreeDark />\n          </div>\n          {/* End .ptf-footer__top */}\n\n          <div className=\"ptf-footer__bottom\">\n            <CopyRight />\n          </div>\n          {/* End .ptf-footer__bottom */}\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default NotFound;\n", "import { useEffect } from \"react\";\nimport { useLocation } from \"react-router-dom\";\n\nexport default function ScrollTopBehaviour() {\n  const { pathname } = useLocation();\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n  }, [pathname]);\n\n  return null;\n}\n", "// export const sundaymartDocMenu = [\n//   {\n//     name: \"Introduction\",\n//     routerPath: \"/sundaymart-documentation/introduction\",\n//   },\n//   {\n//     name: \"Prerequisite\",\n//     dropDownItems: [\n//       {\n//         name: \"Basic\",\n//         routerPath: \"/sundaymart-documentation/basic\",\n//       },\n//       {\n//         name: \"Backend\",\n//         routerPath: \"/sundaymart-documentation/server\",\n//       },\n//       {\n//         name: \"Frontend\",\n//         routerPath: \"/sundaymart-documentation/front\",\n//       },\n//       {\n//         name: \"Mobile app\",\n//         routerPath: \"/sundaymart-documentation/mobile-app\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Local environment Setup\",\n//     dropDownItems: [\n//       {\n//         name: \"Mobile app\",\n//         routerPath: \"/sundaymart-documentation/flutter-sdk\",\n//       },\n//       {\n//         name: \"Frontend\",\n//         routerPath: \"/sundaymart-documentation/local-front\",\n//       },\n//       {\n//         name: \"Backend\",\n//         routerPath: \"/sundaymart-documentation/local-server\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Admin Panel\",\n//     dropDownItems: [\n//       {\n//         name: \"Install on server\",\n//         routerPath: \"/sundaymart-documentation/install-on-server\",\n//       },\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/sundaymart-documentation/mandatory-setup\",\n//       },\n//       {\n//         name: \"Customization\",\n//         routerPath: \"/sundaymart-documentation/customization\",\n//       },\n//       {\n//         name: \"Troubleshooting\",\n//         routerPath: \"/sundaymart-documentation/troubleshooting\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Mobile Apps\",\n//     dropDownItems: [\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/sundaymart-documentation/mandatory-setup-mobile\",\n//       },\n//       {\n//         name: \"Customization\",\n//         routerPath: \"/sundaymart-documentation/customization-mobile\",\n//       },\n//       {\n//         name: \"App build & release\",\n//         routerPath: \"/sundaymart-documentation/app-build-release\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Web App\",\n//     dropDownItems: [\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/sundaymart-documentation/mandatory-setup-web\",\n//       },\n//       {\n//         name: `Build code and setup on server`,\n//         routerPath: \"/sundaymart-documentation/build-code-and-setup-on-server\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Firebase setup\",\n//     routerPath: \"/sundaymart-documentation/firebase\",\n//   },\n//   {\n//     name: \"Update\",\n//     dropDownItems: [\n//       {\n//         name: \"Admin Panel\",\n//         routerPath: \"/sundaymart-documentation/update-admin-panel\",\n//       },\n//       {\n//         name: \"App & Web\",\n//         routerPath: \"/sundaymart-documentation/Update-app-web\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Support plan\",\n//     routerPath: \"/sundaymart-documentation/support-plan\",\n//   },\n// ];\n\nexport const sundaymartDocMenu = [\n  {\n    name: \"Introduction\",\n    dropDownItems: [\n      {\n        name: \"Introduction\",\n        routerPath: \"/sundaymart-documentation/introduction\",\n      },\n      {\n        name: \"Server requirements\",\n        routerPath: \"/sundaymart-documentation/recommendations\",\n      },\n    ],\n  },\n  {\n    name: \"Backend Api\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/sundaymart-documentation/server\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/sundaymart-documentation/mandatory-setup-backend\",\n      },\n      {\n        name: \"Troubleshooting\",\n        routerPath: \"/sundaymart-documentation/troubleshooting-backend\",\n      },\n    ],\n  },\n  {\n    name: \"Admin Panel\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/sundaymart-documentation/admin\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/sundaymart-documentation/install-on-server\",\n      },\n      {\n        name: \"Troubleshooting\",\n        routerPath: \"/sundaymart-documentation/troubleshooting-admin\",\n      },\n    ],\n  },\n  {\n    name: \"Customer Website\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/sundaymart-documentation/front\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/sundaymart-documentation/mandatory-setup-web\",\n      },\n      {\n        name: \"Change page's static data\",\n        routerPath: \"/sundaymart-documentation/change-static-data\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Customer App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/sundaymart-documentation/mobile-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/sundaymart-documentation/mandatory-setup-customer\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/sundaymart-documentation/customization-customer\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/sundaymart-documentation/customer-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Vendor App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/sundaymart-documentation/vendor-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/sundaymart-documentation/mandatory-setup-vendor\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/sundaymart-documentation/customization-vendor\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/sundaymart-documentation/vendor-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Driver App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/sundaymart-documentation/driver-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/sundaymart-documentation/mandatory-setup-deliveryboy\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/sundaymart-documentation/customization-deliveryboy\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/sundaymart-documentation/deliveryboy-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Pos App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/sundaymart-documentation/pos-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/sundaymart-documentation/mandatory-setup-pos\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/sundaymart-documentation/customization-pos\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/sundaymart-documentation/pos-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Firebase setup\",\n    routerPath: \"/sundaymart-documentation/firebase\",\n  },\n  {\n    name: \"Update\",\n    routerPath: \"/sundaymart-documentation/update\",\n  },\n];\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport \"react-pro-sidebar/dist/css/styles.css\";\nimport {\n  ProSidebar,\n  Menu,\n  MenuItem,\n  SubMenu,\n  SidebarContent,\n} from \"react-pro-sidebar\";\nimport { sundaymartDocMenu } from \"../../../fake-data/Sunday-doc-menu\";\n\nconst MobileVendorMenu = ({ handleClickMenu, docMenu = sundaymartDocMenu }) => {\n  return (\n    <>\n      <div className=\"ptf-offcanvas-menu__navigation\">\n        <ProSidebar>\n          <SidebarContent>\n            <Menu className=\"sidebar-menu_wrapper\">\n              {docMenu.map((item, i) => (\n                <div key={i}>\n                  {item.dropDownItems?.length > 0 ? (\n                    <SubMenu title={item.name} key={i}>\n                      {item.dropDownItems?.map((val, index) => (\n                        <MenuItem key={index}>\n                          <Link to={val.routerPath} onClick={handleClickMenu}>\n                            {val.name}\n                          </Link>\n                        </MenuItem>\n                      ))}\n                    </SubMenu>\n                  ) : (\n                    <MenuItem className=\"sidebar-menu\">\n                      <Link to={item.routerPath}>{item.name}</Link>\n                    </MenuItem>\n                  )}\n                </div>\n              ))}\n            </Menu>\n          </SidebarContent>\n        </ProSidebar>\n      </div>\n\n      <div className=\"ptf-offcanvas-menu__footer\">\n        <p className=\"ptf-offcanvas-menu__copyright\">\n          @{new Date().getFullYear()} <span>Githubit</span>. All Rights\n          Reserved. <br />\n        </p>\n      </div>\n    </>\n  );\n};\n\nexport default MobileVendorMenu;\n", "import React, { useState } from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport MobileDocument from \"./Menu/MobileSundayDoc\";\nimport DarkMode from \"../DarkMode\";\n\nconst HeaderDark = ({ docMenu }) => {\n  const [menu, setMenu] = useState(false);\n  const handleClickMenu = () => setMenu(!menu);\n\n  return (\n    <>\n      <header className=\"ptf-header--style-5 ptf-header--opaque\">\n        <div className={\"ptf-navbar ptf-navbar--main ptf-navbar--sticky\"}>\n          <div className=\"container-xxl\">\n            <div className=\"ptf-navbar-inner\">\n              <Link className=\"ptf-navbar-logo\" to=\"/\">\n                <h2 className=\"text-white\">Githubit</h2>\n              </Link>\n              <DarkMode />\n              <div\n                className=\"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle bar right mobile-document-menu\"\n                onClick={handleClickMenu}\n              >\n                <i className=\"lnir lnir-menu-alt-5\" />\n              </div>\n              <div\n                className=\"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle bar right\"\n                onClick={handleClickMenu}\n              />\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div\n        className={menu ? \"ptf-offcanvas-menu is-open\" : \"ptf-offcanvas-menu \"}\n      >\n        <div className=\"ptf-offcanvas-menu__header\">\n          <div className=\"ptf-language-switcher\"></div>\n          <span\n            className=\"ptf-offcanvas-menu-icon js-offcanvas-menu-toggle\"\n            onClick={handleClickMenu}\n          >\n            <i className=\"lnir lnir-close\" />\n          </span>\n        </div>\n        <MobileDocument docMenu={docMenu} handleClickMenu={handleClickMenu} />\n      </div>\n    </>\n  );\n};\n\nexport default HeaderDark;\n", "// export const foodymanDocMenu = [\n//   {\n//     name: \"Introduction\",\n//     routerPath: \"/foodyman-documentation/introduction\",\n//   },\n//   {\n//     name: \"Prerequisite\",\n//     dropDownItems: [\n//       {\n//         name: \"Basic\",\n//         routerPath: \"/foodyman-documentation/basic\",\n//       },\n//       {\n//         name: \"Backend\",\n//         routerPath: \"/foodyman-documentation/server\",\n//       },\n//       {\n//         name: \"Frontend\",\n//         routerPath: \"/foodyman-documentation/front\",\n//       },\n//       {\n//         name: \"Recommendations\",\n//         routerPath: \"/foodyman-documentation/recommendations\",\n//       },\n//       {\n//         name: \"Mobile app\",\n//         routerPath: \"/foodyman-documentation/mobile-app\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Local environment Setup\",\n//     dropDownItems: [\n//       {\n//         name: \"Mobile app\",\n//         routerPath: \"/foodyman-documentation/flutter-sdk\",\n//       },\n//       {\n//         name: \"Frontend\",\n//         routerPath: \"/foodyman-documentation/local-front\",\n//       },\n//       {\n//         name: \"Backend\",\n//         routerPath: \"/foodyman-documentation/local-server\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Backend\",\n//     dropDownItems: [\n//       {\n//         name: \"Install on server\",\n//         routerPath: \"/foodyman-documentation/mandatory-setup-backend\",\n//       },\n//       {\n//         name: \"Troubleshooting\",\n//         routerPath: \"/foodyman-documentation/troubleshooting-backend\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Admin Panel\",\n//     dropDownItems: [\n//       {\n//         name: \"Install on server\",\n//         routerPath: \"/foodyman-documentation/install-on-server\",\n//       },\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/foodyman-documentation/mandatory-setup\",\n//       },\n//       {\n//         name: \"Customization\",\n//         routerPath: \"/foodyman-documentation/customization\",\n//       },\n//       {\n//         name: \"Troubleshooting\",\n//         routerPath: \"/foodyman-documentation/troubleshooting-admin\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Web App\",\n//     dropDownItems: [\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/foodyman-documentation/mandatory-setup-web\",\n//       },\n//       {\n//         name: `Build code and setup on server`,\n//         routerPath: \"/foodyman-documentation/build-code-and-setup-on-server\",\n//       },\n//     ],\n//   },\n\n//   {\n//     name: \"Customer Apps\",\n//     dropDownItems: [\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/foodyman-documentation/mandatory-setup-customer\",\n//       },\n//       {\n//         name: \"Customization\",\n//         routerPath: \"/foodyman-documentation/customization-customer\",\n//       },\n//       {\n//         name: \"App build & release\",\n//         routerPath: \"/foodyman-documentation/customer-app-build-release\",\n//       },\n//     ],\n//   },\n\n//   {\n//     name: \"Vendor Apps\",\n//     dropDownItems: [\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/foodyman-documentation/mandatory-setup-vendor\",\n//       },\n//       {\n//         name: \"Customization\",\n//         routerPath: \"/foodyman-documentation/customization-vendor\",\n//       },\n//       {\n//         name: \"App build & release\",\n//         routerPath: \"/foodyman-documentation/vendor-app-build-release\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Deliveryboy Apps\",\n//     dropDownItems: [\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/foodyman-documentation/mandatory-setup-deliveryboy\",\n//       },\n//       {\n//         name: \"Customization\",\n//         routerPath: \"/foodyman-documentation/customization-deliveryboy\",\n//       },\n//       {\n//         name: \"App build & release\",\n//         routerPath: \"/foodyman-documentation/deliveryboy-app-build-release\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"POS system\",\n//     dropDownItems: [\n//       {\n//         name: \"Mandatory setup\",\n//         routerPath: \"/foodyman-documentation/mandatory-setup-pos\",\n//       },\n//       {\n//         name: \"Customization\",\n//         routerPath: \"/foodyman-documentation/customization-pos\",\n//       },\n//       {\n//         name: \"App build & release\",\n//         routerPath: \"/foodyman-documentation/pos-app-build-release\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Firebase setup\",\n//     routerPath: \"/foodyman-documentation/firebase\",\n//   },\n//   {\n//     name: \"Update\",\n//     dropDownItems: [\n//       {\n//         name: \"Admin Panel\",\n//         routerPath: \"/foodyman-documentation/update-admin-panel\",\n//       },\n//       {\n//         name: \"App & Web\",\n//         routerPath: \"/foodyman-documentation/Update-app-web\",\n//       },\n//     ],\n//   },\n//   {\n//     name: \"Support plan\",\n//     routerPath: \"/foodyman-documentation/support-plan\",\n//   },\n// ];\n\nexport const foodymanDocMenu = [\n  {\n    name: \"Introduction\",\n    dropDownItems: [\n      {\n        name: \"Introduction\",\n        routerPath: \"/foodyman-documentation/introduction\",\n      },\n      {\n        name: \"Server requirements\",\n        routerPath: \"/foodyman-documentation/recommendations\",\n      },\n    ],\n  },\n  {\n    name: \"Backend Api\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/server\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/mandatory-setup-backend\",\n      },\n      {\n        name: \"Payment installation\",\n        routerPath: \"/foodyman-documentation/payment-installation\",\n      },\n      {\n        name: \"Image settings\",\n        routerPath: \"/foodyman-documentation/image-settings\",\n      },\n      {\n        name: \"Troubleshooting\",\n        routerPath: \"/foodyman-documentation/troubleshooting-backend\",\n      },\n    ],\n  },\n  {\n    name: \"Admin Panel\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/admin\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/install-on-server\",\n      },\n      {\n        name: \"Troubleshooting\",\n        routerPath: \"/foodyman-documentation/troubleshooting-admin\",\n      },\n    ],\n  },\n  {\n    name: \"Customer Website\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/front\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/mandatory-setup-web\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Qrcode Website\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/front-qr\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/mandatory-setup-web-qr\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Customer App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/mobile-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/mandatory-setup-customer\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/foodyman-documentation/customization-customer\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/foodyman-documentation/customer-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Vendor App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/vendor-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/mandatory-setup-vendor\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/foodyman-documentation/customization-vendor\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/foodyman-documentation/vendor-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Driver App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/driver-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/mandatory-setup-deliveryboy\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/foodyman-documentation/customization-deliveryboy\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/foodyman-documentation/deliveryboy-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Pos App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/pos-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/mandatory-setup-pos\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/foodyman-documentation/customization-pos\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/foodyman-documentation/pos-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  {\n    name: \"Kiosk App\",\n    dropDownItems: [\n      {\n        name: \"Requirements\",\n        routerPath: \"/foodyman-documentation/kiosk-app\",\n      },\n      {\n        name: \"Installation\",\n        routerPath: \"/foodyman-documentation/mandatory-setup-kiosk\",\n      },\n      {\n        name: \"Customization\",\n        routerPath: \"/foodyman-documentation/customization-kiosk\",\n      },\n      {\n        name: \"App build & release\",\n        routerPath: \"/foodyman-documentation/kiosk-app-build-release\",\n      },\n      // {\n      //   name: \"Troubleshooting\",\n      //   routerPath: \"/\",\n      // },\n    ],\n  },\n  // {\n  //   name: \"QR code App\",\n  //   dropDownItems: [\n  //     {\n  //       name: \"Requirements\",\n  //       routerPath: \"/foodyman-documentation/qr-app\",\n  //     },\n  //     {\n  //       name: \"Installation\",\n  //       routerPath: \"/foodyman-documentation/install-qrcode\",\n  //     }\n  //   ],\n  // },\n  {\n    name: \"Firebase setup\",\n    routerPath: \"/foodyman-documentation/firebase\",\n  },\n  {\n    name: \"Update\",\n    routerPath: \"/foodyman-documentation/update\",\n  },\n];\n", "import React from \"react\";\nimport \"react-pro-sidebar/dist/css/styles.css\";\nimport {\n  ProSidebar,\n  Menu,\n  MenuItem,\n  SubMenu,\n  SidebarContent,\n} from \"react-pro-sidebar\";\nimport { NavLink } from \"react-router-dom\";\nimport { foodymanDocMenu } from \"../../fake-data/Foody-doc-menu\";\n\nconst DocumentationMenu = () => {\n  return (\n    <div className=\"docMenu\">\n      <ProSidebar className=\"pro-sidebar\">\n        <SidebarContent>\n          <Menu className=\"sidebar-menu_wrapper\">\n            {foodymanDocMenu.map((item, i) => (\n              <div key={i}>\n                {item.dropDownItems?.length > 0 ? (\n                  <SubMenu title={item.name} key={i} defaultOpen={true}>\n                    {item.dropDownItems?.map((val, index) => (\n                      <MenuItem key={index}>\n                        <NavLink\n                          className=\"nav-link-active\"\n                          to={val.routerPath}\n                        >\n                          {val.name}\n                        </NavLink>\n                      </MenuItem>\n                    ))}\n                  </SubMenu>\n                ) : (\n                  <MenuItem className=\"sidebar-menu\">\n                    <NavLink to={item.routerPath} className=\"nav-link-active\">\n                      {item.name}\n                    </NavLink>\n                  </MenuItem>\n                )}\n              </div>\n            ))}\n          </Menu>\n        </SidebarContent>\n      </ProSidebar>\n    </div>\n  );\n};\n\nexport default DocumentationMenu;\n", "import React from \"react\";\nimport { Helmet } from \"react-helmet\";\nimport CopyRight from \"../../components/Footer/Copyright/CopyRight\";\nimport HeaderSunday from \"../../components/Header/HeaderSunday\";\nimport DocumentationMenu from \"../../components/DocMenu/foodyman\";\nimport { foodymanDocMenu } from \"../../fake-data/Foody-doc-menu\";\nimport { Outlet } from \"react-router-dom\";\n\nconst Documentation = () => {\n  return (\n    <div className=\"docContainer\">\n      <Helmet>\n        <title>GitHubit - Foodyman Documentation</title>\n      </Helmet>\n      <HeaderSunday docMenu={foodymanDocMenu} />\n      <div className=\"ptf-main\">\n        <div className=\"ptf-page ptf-page--contact\">\n          <section>\n            <div className=\"container-xxl\">\n              <div className=\"row\">\n                <div className=\"col-lg-3\">\n                  <div className=\"docMenuContainer\">\n                    <DocumentationMenu />\n                  </div>\n                </div>\n                <div className=\"col-lg-9 docContent\">\n                  <Outlet />\n                </div>\n              </div>\n            </div>\n          </section>\n        </div>\n        <div\n          className=\"ptf-spacer\"\n          style={{ \"--ptf-xxl\": \"3rem\", \"--ptf-md\": \"2.5rem\" }}\n        />\n      </div>\n\n      <footer className=\"ptf-footer ptf-footer--style-5 ptf-footer-doc\">\n        <div className=\"container-xxl\">\n          <CopyRight />\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Documentation;\n", "import { Link } from \"react-router-dom\";\nimport { foodymanDocMenu } from \"../../fake-data/Foody-doc-menu\";\n\nconst allMenus = foodymanDocMenu.flatMap((menu) => {\n  if (menu.routerPath) return menu;\n  return menu.dropDownItems;\n});\n\nfunction NavigationBtns() {\n  const windowPath = window.location.hash.split(\"#\")[1];\n  const currentMenuIdx = allMenus.findIndex(\n    (menu) => menu.routerPath === windowPath\n  );\n  if (currentMenuIdx === -1)\n    return (\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/recommendations\"\n          className=\"btn  previous\"\n        >\n          <p>Next</p>\n          <p className=\"link\"> Server requirements </p>\n        </Link>\n      </div>\n    );\n\n  const currentMenus = (() => {\n    if (currentMenuIdx === 0) return [null, allMenus[1]];\n    if (currentMenuIdx === allMenus.length - 1)\n      return [allMenus[allMenus.length - 2], null];\n\n    return [allMenus[currentMenuIdx - 1], allMenus[currentMenuIdx + 1]];\n  })();\n\n  return (\n    <div className=\"center-page-container\">\n      {currentMenus.map(\n        (menu, idx) =>\n          menu && (\n            <Link to={menu.routerPath} className=\"btn  previous\">\n              <p>{idx === 0 ? \"Previos\" : \"Next\"}</p>\n              <p className=\"link\"> {menu.name} </p>\n            </Link>\n          )\n      )}\n    </div>\n  );\n}\n\nexport default NavigationBtns;\n", "import React from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { <PERSON> } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst Introduction = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <div className=\"alert alert-primary mb-5\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Keep /configs/credential.php and /configs/init.php file while updating.\n        If you don’t keep them, you may have a problem with lisence.\n      </div>\n\n      <h1 className=\"title\">Introduction</h1>\n      <strong className=\"introduction-subTitle\">\n        Build Your Business with our -\n        <strong className=\"strong\"> Website</strong>\n      </strong>\n      <div className=\"iframe-wrapper mb-5\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/678jGWxBI0k\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        />\n      </div>\n      <strong className=\"introduction-subTitle\">\n        Build Your Business with our -\n        <strong className=\"strong\"> Mobile app</strong>\n      </strong>\n      <div className=\"iframe-wrapper mb-5\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/NKv9g64_qnM\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        />\n      </div>\n      <strong className=\"introduction-subTitle\">\n        Build Your Business with our -\n        <strong className=\"strong\"> Restaurant manager app </strong>\n      </strong>\n      <div className=\"iframe-wrapper mb-5\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/MgbVA06C_NI\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        />\n      </div>\n      <strong className=\"introduction-subTitle\">\n        Build Your Business with our -\n        <strong className=\"strong\"> Delivery boy app</strong>\n      </strong>\n      <div className=\"iframe-wrapper mb-5\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/IHK-mF0P50Q\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        />\n      </div>\n      <h3 className=\"introduction-title\">Overview</h3>\n      <strong className=\"introduction-subTitle\">\n        Welcome to - <strong className=\"strong\">Foodyman.</strong>\n      </strong>\n\n      <div>\n        Foodyman is a multi restaurant marketplace, full solution ( apps, web,\n        admin) developed by applying Laravel, React Js , Next Js and Flutter\n        Frameworks. <br />\n        Laravel is an open-source PHP Framework intended for the development of\n        web applications which is a complete toolset, robust, yet easy to\n        understand with an excellent, expressive syntax. <br />\n        Next.js is an open-source web development framework created by Vercel\n        enabling React-based web applications with server-side rendering and\n        generating static websites.\n        <br />\n        React (also known as React.js or ReactJs) is a free and open-source\n        front-end JavaScript library[3] for building user interfaces based on UI\n        components.\n        <br />\n        Flutter is an open-source framework for mobile application development\n        created by Google. It is applied to develop multi-platform, natively\n        compiled applications for Android and iOS.\n        <div className=\"line-break\" />\n        Foodyman consists of 2 Mobile Applications, 5 Web Panel, 1 website:\n        <br />\n        <div className=\"introduction-img-container\">\n          <SimpleReactLightbox>\n            <SRLWrapper>\n              <a\n                href=\"./assets/img/doc/admin-banner.jpg\"\n                data-fancybox\n                rel=\"nofollow\"\n              >\n                <img\n                  src=\"./assets/img/doc/admin-banner.jpg\"\n                  alt=\"admin\"\n                  loading=\"lazy\"\n                  className=\"img-responsive-full\"\n                />\n              </a>\n            </SRLWrapper>\n          </SimpleReactLightbox>\n          <SimpleReactLightbox>\n            <SRLWrapper>\n              <a\n                href=\"./assets/img/project/foodyman-banner.jpg\"\n                data-fancybox\n                rel=\"nofollow\"\n              >\n                <img\n                  src=\"./assets/img/project/foodyman-banner.jpg\"\n                  alt=\"web main\"\n                  loading=\"lazy\"\n                  className=\"img-responsive-full\"\n                />\n              </a>\n            </SRLWrapper>\n          </SimpleReactLightbox>\n        </div>\n        <div className=\"line-break\" />\n        <h3 className=\"introduction-contentTitle\">\n          3 Apps developed using Flutter\n        </h3>\n        • Customer application <br />\n        • Delivery Boy application (not included) <br />\n        • Manager application (not included) <br />\n        <h3 className=\"introduction-contentTitle\">\n          5 web panel developed using Laravel & React Js\n        </h3>\n        • Admin web panel\n        <br />\n        • Manager web panel <br />\n        • Store web panel <br />\n        • Moderator web panel <br />\n        • Delivery boy web panel <br />\n        <h3 className=\"introduction-contentTitle\">\n          1 web site developed using Typescript & Next Js\n        </h3>\n        • Customer web site <br />\n        <div className=\"line-break\" />\n      </div>\n\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default Introduction;\n", "import React from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { GiCampfire } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\n\nconst Basic = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Basic</h1>\n      As mentioned above, Foodyman is a multi-shop/seller & single vendor\n      marketplace, full solution ( apps, web, admin). To set up the project, you\n      should have some programming knowledge and tool installed on your\n      computer.\n      <br />\n      <h3 className=\"introduction-contentTitle\">Tools</h3>\n      • For Mobile development: Visual Studio Code or Android studio , SDK and\n      JDK with path setup in your IDE <br />\n      • For Backend development: Open Server Panel , XAMPP , Wamp Server ,\n      PhpStorm <br />\n      • For Frontend development: Nodejs , Visual Studio Code or WebStorm <br />\n      <h3 className=\"introduction-contentTitle\">Knowledge</h3>\n      • For Mobile development: Dart , Flutter , basic Java and basic Swift\n      knowledge <br />\n      • For Backend development: PHP , MySQL , Laravel <br />• For Frontend\n      development: Next Js , React Js, Google map , Firebase <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        We would like to notify that the Envato price doesn’t include any kind\n        of installation and app publishing support. We kindly ask you to follow\n        the documentation step by step for installation, setup and other\n        branding related changes. Please note that, we bear no responsibility\n        for your mistake. You are fully in charge for any kind of customizations\n        made by your own.\n      </div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>Note</strong>\n        </div>\n        <p className=\"h5\">The terminal in your server should support nodejs</p>\n      </div>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/introduction\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Introduction </p>\n        </Link>\n        <Link to=\"/foodyman-documentation/server\" className=\"btn  next\">\n          <p>Next</p>\n          <p className=\"link\"> Backend </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default Basic;\n", "import React from \"react\";\nimport { Gi<PERSON>ampfire } from \"react-icons/gi\";\nimport { <PERSON> } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst Server = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Backend requirements</h1>\n      <ul>\n        <li>VPS server</li>\n        <li>PHP v8.1+</li>\n      </ul>\n      <ul>\n        <li>\n          Following extension have to be installed and enabled in your php:\n          <ul>\n            <li>openssl</li>\n            <li>fileinfo</li>\n            <li>gd</li>\n            <li>curl</li>\n            <li>sodium</li>\n            <li>zip</li>\n          </ul>\n        </li>\n      </ul>\n      <br />\n      • MySQL 8+ <br />\n      {/*<p className=\"mt-3\">In most servers these extensions are enabled by default, but you should check with your*/}\n      {/*    hosting provider.</p>*/}\n      <h3 className=\"introduction-contentTitle\">Tools</h3>\n      • For Backend development: Open Server Panel , XAMPP , Wamp Server ,\n      PhpStorm <br />\n      <h3 className=\"introduction-contentTitle\">Knowledge</h3>\n      • For Backend development: PHP , MySQL , Laravel <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        We would like to notify that the Envato price doesn’t include any kind\n        of installation and app publishing support. We kindly ask you to follow\n        the documentation step by step for installation, setup and other\n        branding related changes. Please note that, we bear no responsibility\n        for your mistake. You are fully in charge for any kind of customizations\n        made by your own.\n      </div>\n      <div className=\"mt-4\" />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do this very carefully. We bear no responsibility for your\n        mistake.\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default Server;\n", "import React from \"react\";\nimport { Gi<PERSON>ampfire } from \"react-icons/gi\";\nimport { <PERSON> } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst Front = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Frontend requirements</h1>\n      • NodeJS v16+ <br />\n      • NextJS v12+ <br />\n      • ReactJS v17- <br />\n      <div className=\"mt-4\" />\n      <div className=\"mt-4\" />\n      <h3 className=\"introduction-contentTitle\">Tools</h3>\n      • For Frontend development: Nodejs , Visual Studio Code or WebStorm <br />\n      <h3 className=\"introduction-contentTitle\">Knowledge</h3>• For Frontend\n      development: Next Js , React Js, Google map , Firebase <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        We would like to notify that the Envato price doesn’t include any kind\n        of installation and app publishing support. We kindly ask you to follow\n        the documentation step by step for installation, setup and other\n        branding related changes. Please note that, we bear no responsibility\n        for your mistake. You are fully in charge for any kind of customizations\n        made by your own.\n      </div>\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do this very carefully. We bear no responsibility for your\n        mistake.\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default Front;\n", "import React from \"react\";\nimport { G<PERSON><PERSON>amp<PERSON> } from \"react-icons/gi\";\nimport { <PERSON> } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst Front = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Frontend requirements</h1>\n      • NodeJS v16+\n      <br />\n      • Vite v4+ <br />\n      • ReactJS v18+ <br />\n      <div className=\"mt-4\" />\n      <div className=\"mt-4\" />\n      <h3 className=\"introduction-contentTitle\">Tools</h3>\n      • For Frontend development: Nodejs , Visual Studio Code or WebStorm <br />\n      <h3 className=\"introduction-contentTitle\">Knowledge</h3>• For Frontend\n      development: Vite , React Js <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        We would like to notify that the Envato price doesn’t include any kind\n        of installation and app publishing support. We kindly ask you to follow\n        the documentation step by step for installation, setup and other\n        branding related changes. Please note that, we bear no responsibility\n        for your mistake. You are fully in charge for any kind of customizations\n        made by your own.\n      </div>\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do this very carefully. We bear no responsibility for your\n        mistake.\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default Front;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MandatorySetupWeb = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Build code and setup on server</h1>\n      <h3 className=\"introduction-contentTitle\"> Frontend website </h3>\n      • Open /.env and change every single credential with your own\n      <br />\n      • If there is no .env file, please create the one in root folder and fill\n      as the same as in the example\n      <br />\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/qrcode-front.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/qrcode-front.jpg\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      For building web data for deployment, you have to run commands:\n      <br />\n      • Install required package <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn</span>\n          <span\n            className={text === 3 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn\", 3)}\n          >\n            {text === 3 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Build frontend using following command <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn build</span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn build\", 5)}\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Run project with pm2 <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">pm2 start “yarn preview”</span>\n          <span\n            className={text === 6 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm run start\", 6)}\n          >\n            {text === 6 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        You have to configure your server for front website. Front website runs\n        in port 3000. open server configuration file and add{\" \"}\n        <div className=\"introduction-code\">\n          <div>\n            <span className=\"ps-0\">\n              ProxyPass / http://localhost:4173 <br /> ProxyPassReverse /\n              http://localhost:4173\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"ProxyPass / http://localhost:3000  ProxyPassReverse /http://localhost:3000\",\n                  7\n                )\n              }\n            >\n              {text === 7 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>{\" \"}\n        in your domain configuration section. Rewrite mode should be enabled in\n        your server. After adding, restart your server\n      </div>\n      <h4>Or</h4>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Create .htaccess file in main webiste directory and put following\n        content\n        <div className=\"introduction-code\">\n          <div>\n            <span className=\"ps-0\">\n              RewriteEngine on RewriteRule (.*) http://localhost:4173/$1\n              [P,L,R=301]\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"RewriteEngine on RewriteRule (.*) http://localhost:3000/$1[P,L,R=301]\",\n                  7\n                )\n              }\n            >\n              {text === 7 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MandatorySetupWeb;\n", "import React from \"react\";\nimport { Gi<PERSON>amp<PERSON> } from \"react-icons/gi\";\nimport { <PERSON> } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst Front = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Admin frontend requirements</h1>\n      • NodeJS v16+\n      <br />\n      • React js v17- <br />\n      <div className=\"mt-4\" />\n      <div className=\"mt-4\" />\n      <h3 className=\"introduction-contentTitle\">Tools</h3>\n      • For Frontend development: Nodejs , Visual Studio Code or WebStorm <br />\n      <h3 className=\"introduction-contentTitle\">Knowledge</h3>• For Frontend\n      development: React Js, Google map , Firebase <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        We would like to notify that the Envato price doesn’t include any kind\n        of installation and app publishing support. We kindly ask you to follow\n        the documentation step by step for installation, setup and other\n        branding related changes. Please note that, we bear no responsibility\n        for your mistake. You are fully in charge for any kind of customizations\n        made by your own.\n      </div>\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do this very carefully. We bear no responsibility for your\n        mistake.\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default Front;\n", "import React from \"react\";\nimport { GiCampfire } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst Recommendations = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Recommendations</h1>\n      • Operating system: Ubuntu 22.04 64bit <br />\n      • Cores: 4 <br />\n      • RAM: 4GB <br />\n      •  VPS only, shared hosting servers are not recommended. <br />\n      <div className=\"mt-4\" />\n      <div className=\"mt-4\" />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do this very carefully. We bear no responsibility for your\n        mistake.\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default Recommendations;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst LocalFront = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Frontend</h1>\n      <p className=\"inner-text\">\n        Please, to setup frontend, download nodeJs and install it in your\n        computer.\n      </p>\n      Windows:{\" \"}\n      <a\n        href=\"https://nodejs.org/dist/v16.17.0/node-v16.17.0-x64.msi\"\n        className=\"introduction-link\"\n      >\n        https://nodejs.org/dist/v16.17.0/node-v16.17.0-x64.msi\n      </a>\n      <br />\n      Mac:{\" \"}\n      <a\n        href=\"https://nodejs.org/dist/v16.17.0/node-v16.17.0.pkg\"\n        className=\"introduction-link\"\n      >\n        https://nodejs.org/dist/v16.17.0/node-v16.17.0.pkg\n      </a>\n      <br />\n      Linux:{\" \"}\n      <a\n        href=\"https://nodejs.org/dist/v16.17.0/node-v16.17.0-linux-x64.tar.xz\"\n        className=\"introduction-link\"\n      >\n        https://nodejs.org/dist/v16.17.0/node-v16.17.0-linux-x64.tar.xz\n      </a>\n      <br />\n      <div className=\"mt-4\" />\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/flutter-sdk\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Mobile App </p>\n        </Link>\n        <Link to=\"/foodyman-documentation/local-server\" className=\"btn  next\">\n          <p>Next</p>\n          <p className=\"link\"> Backend </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default LocalFront;\n", "import React from \"react\";\nimport { FcIdea } from \"react-icons/fc\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MobileApp = ({ pageTitle = \"Mobile App\" }) => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">{pageTitle}</h1>\n      • Android studio <br />\n      <div className=\"alert alert-success mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <FcIdea size={22} /> <strong> TIP</strong>\n        </div>\n        You can download android studio via this link: <br />\n        <a href=\"https://developer.android.com/studio?gclid=CjwKCAiAiKuOBhBQEiwAId_sK4X0PLQrES_2pG_S8nPflALgWSOCUEqRRAFpbS4AmR5mXmU6hIhvHxoCfBgQAvD_BwE&gclsrc=aw.ds\">\n          https://developer.android.com/studio?gclid=CjwKCAiAiKuOBhBQEiwAId_sK4X0PLQrES_2pG_S8nPflALgWSOCUEqRRAFpbS4AmR5mXmU6hIhvHxoCfBgQAvD_BwE&gclsrc=aw.ds\n        </a>\n      </div>\n      • Flutter SDK setup (version 3.24{\" \"}\n      <strong className=\"strong\">Stable</strong>) <br />\n      • Java setup (version 23) <br />\n      • Gradle (version 8.10) <br />\n      • JDK with path setup (only for vs code) <br />\n      • Xcode for IPA file build <br />\n      • State management -> riverpod <br />\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MobileApp;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst FlutterSdk = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Mobile app</h1>\n      <p className=\"inner-text\">\n        Please download and setup flutter from flutter.dev. You can find\n        documentation for your own device via the following links.\n      </p>\n      Windows:{\" \"}\n      <a\n        href=\"https://docs.flutter.dev/get-started/install/windows\"\n        className=\"introduction-link\"\n      >\n        https://docs.flutter.dev/get-started/install/windows\n      </a>\n      <br />\n      Mac:{\" \"}\n      <a\n        href=\"https://docs.flutter.dev/get-started/install/macos\"\n        className=\"introduction-link\"\n      >\n        https://docs.flutter.dev/get-started/install/macos\n      </a>\n      <br />\n      Linux:{\" \"}\n      <a\n        href=\"https://docs.flutter.dev/get-started/install/linux\"\n        className=\"introduction-link\"\n      >\n        https://docs.flutter.dev/get-started/install/linux\n      </a>\n      <br />\n      <div className=\"center-page-container\">\n        <Link to=\"/foodyman-documentation/mobile-app\" className=\"btn  previous\">\n          <p>Previous</p>\n          <p className=\"link\"> Mobile App </p>\n        </Link>\n        <Link to=\"/foodyman-documentation/local-front\" className=\"btn  next\">\n          <p>Next</p>\n          <p className=\"link\"> Frontend Local </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default FlutterSdk;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst LocalServer = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Backend</h1>\n      <p className=\"inner-text\">\n        {\" \"}\n        To run backend in your local machine, install one of these programs in\n        your computer:{\" \"}\n      </p>\n      <div className=\"mt-4\" />\n      <div className=\"iframe-wrapper\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/xy3qeGYQNtw\"\n          title=\"How to setup Docker on MacOS\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"mt-4\" />\n      <br />\n      <h3 className=\"introduction-contentTitle\"> Xampp:</h3>{\" \"}\n      <a\n        href=\"https://www.apachefriends.org/download.html\"\n        className=\"introduction-link\"\n      >\n        https://www.apachefriends.org/download.html\n      </a>\n      <br />\n      <h3 className=\"introduction-contentTitle\">WampServer:</h3>{\" \"}\n      <a href=\"https://www.wampserver.com/en/\" className=\"introduction-link\">\n        https://www.wampserver.com/en/\n      </a>\n      <br />\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/local-front\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\">Frontend</p>\n        </Link>\n        <Link\n          to=\"/foodyman-documentation/mandatory-setup-backend\"\n          className=\"btn  next\"\n        >\n          <p>Next</p>\n          <p className=\"link\"> Install on server </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default LocalServer;\n", "import React from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { <PERSON> } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst InstallOnServer = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Install on server</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Change title inside /public/index.html\n      </div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Thus to install admin_panel, we need subdomains like admin.xyz.com and\n        api.xyz.com.\n      </div>\n      <div className=\"alert alert-warning mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>Warning</strong>\n        </div>\n        You have to set SSL certificate for your admin website. Some functions\n        doesn't work if your website doesn't have SSL certificate.\n      </div>\n      • Download the code from codecayon <br />• Extract the zip files\n      <h3 className=\"introduction-contentTitle\"> Admin Panel front </h3>\n      • Open /public/index.html and change google map key with your google map\n      key\n      <br />\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/index-map-key.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/index-map-key.png\"\n                alt=\"admin config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      • Open /src/configs/app_global.js and change BASE_URL with your server url\n      <br />\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/adminConfig.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/adminConfig.png\"\n                alt=\"admin config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/configFirebase.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/configFirebase.png\"\n                alt=\"admin firebase\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      • Also select the main language\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/theme.png\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/theme.png\"\n                alt=\"theme config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p className=\"mb-0\">\n        • Create the <strong>build</strong> File\n      </p>\n      <p>\n        In your application's root directory, run <strong>yarn </strong>\n        to install the updated dependencies. Once this has finished, the next\n        command you'll run is <strong>yarn build</strong>\n      </p>\n      <p>\n        You'll notice this creates a new directory in your project called\n        <strong> build</strong>. The build folder is essentially a\n        super-compressed version of your program that has everything your\n        browser needs to identify and run your app.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/build.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/build.png\"\n                alt=\"admin config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p className=\"mb-0\">• Connect to cPanel</p>\n      <p>Your cPanel manager should look something like this:</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/c-pannel.webp\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/c-pannel.webp\"\n                alt=\"admin config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p className=\"mb-0\">\n        • Add the Build File Contents to <strong>public_html</strong>\n      </p>\n      <p>\n        Navigate to the build file in your app's root directory. Open it up and\n        select all the contents <strong>inside the build file.</strong> If you\n        upload the entire build file itself, the process will not work.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/build-file.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/build-file.png\"\n                alt=\"admin config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p>\n        Once you've copied all the contents inside the build file, upload them\n        into <strong>public_html.</strong>\n      </p>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p className=\"mb-0\">\n        • Create and Upload the <strong>.htaccess</strong> File\n      </p>\n      <p>\n        In order for the routes to work in your React app, you need to add a\n        <strong>.htaccess</strong> file. In the <strong>public_html</strong>\n        folder, at the same level as the <strong>build</strong> file contents,\n        add a new file and name it <strong>.htaccess.</strong>\n      </p>\n      <p>Edit the file and insert the following boilerplate information:</p>\n      <div className=\"introduction-code\">\n        <p>\n          {`<IfModule mod_rewrite.c>`}\n          <br />\n          {` RewriteEngine On`}\n          <br />\n          {` RewriteBase /`}\n          <br />\n          {`RewriteRule ^index\\.html$ - [L]`}\n          <br />\n          {`RewriteCond %{REQUEST_FILENAME} !-f`}\n          <br />\n          {`RewriteCond %{REQUEST_FILENAME} !-d`}\n          <br />\n          {`RewriteCond %{REQUEST_FILENAME} !-l`}\n          <br />\n          {`RewriteRule . /index.html [L]`}\n          <br />\n          {`</IfModule>`}\n        </p>\n      </div>\n      <p>Save the file.</p>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        If you meet this error, please, get recaptcha key using this tutorial.{\" \"}\n        <a href=\"https://contactform7.com/recaptcha-v2/\" target=\"_blank\">\n          <strong>Link</strong>{\" \"}\n        </a>\n      </div>\n      <div className=\"introduction-img-container mb-5\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/recaptcha.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/recaptcha.jpg\"\n                alt=\"admin install\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <iframe\n        width=\"100%\"\n        height=\"420\"\n        src=\"https://www.youtube.com/embed/HMUKllwbY2s\"\n        title=\"YouTube video player\"\n        frameBorder=\"0\"\n        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n        allowFullScreen\n      />\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default InstallOnServer;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst MandatorySetup = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Mandatory setup</h1>\n      <h5 className=\"introduction-contentTitle\"> Map Configuration</h5>A client\n      should get <strong className=\"strong\">Map API</strong> from Google in\n      order to enable the maps into the panels.\n      <h5 className=\"introduction-contentTitle\">\n        {\" \"}\n        Firebase Configuration (for notification)\n      </h5>\n      The Firebase Push Notification will send messages for every order status.\n      If admin turn on the status, customers, store, delivery man will get\n      status notification when order status changed and if he turned off that\n      then no one will get that message. To set up firebase notification go to\n      admin panel’s <strong className=\"strong\">\n        Notification settings\n      </strong>{\" \"}\n      menu.\n      <h5 className=\"introduction-contentTitle\"> Payment Configuration</h5>\n      Here, Admin will be introduced with the payment gateways. Cash on\n      delivery, Digital Payment like Razor pay, Paypal, Stripe, Paystack,\n      MercadoPago, Payment accept are available for payment gateways. Admin can\n      make the necessary setup to make the status active or inactive for\n      mentioned payment gateways.\n      <h5 className=\"introduction-contentTitle\"> SMS Module Configuration</h5>\n      SMS Module is utilized for SMS Gateways for OTP sending in the simplest\n      way of user verification. Customer will receive OTP when they create their\n      own account or it is used for password recovery.\n      <h5 className=\"introduction-contentTitle\"> Firebase configuration</h5>\n      <span className=\"youtube-blog\">\n        <iframe\n          src=\"https://www.youtube.com/embed/OLwNp_e5bxM\"\n          title=\"YouTube video player\"\n          frameBorder=\"11\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n        />\n      </span>\n      <h5 className=\"introduction-contentTitle\">\n        {\" \"}\n        Firebase auth configuration\n      </h5>\n      <span className=\"youtube-blog\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/5HzrGiY9cFo\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n          allowFullScreen\n        />\n      </span>\n      <h5 className=\"introduction-contentTitle\">\n        {\" \"}\n        Firebase chat configuration\n      </h5>\n      <span className=\"youtube-blog\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/29ARDVIXvXk\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n          allowFullScreen\n        />\n      </span>\n      <h5 className=\"introduction-contentTitle\">\n        {\" \"}\n        Firebase notification configuration\n      </h5>\n      <span className=\"youtube-blog\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/3E-kEe5X2bg\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n          allowFullScreen\n        />\n      </span>\n      <h5 className=\"introduction-contentTitle\">\n        {\" \"}\n        How to connect firebase to project\n      </h5>\n      <span className=\"youtube-blog\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/jCgZZiz1480\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n          allowFullScreen\n        />\n      </span>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/install-on-server\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Install on server </p>\n        </Link>\n        <Link to=\"/foodyman-documentation/customization\" className=\"btn  next\">\n          <p>Next</p>\n          <p className=\"link\"> Customization</p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default MandatorySetup;\n", "import React from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\n\nconst Customization = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Customization</h1>\n      <h4 className=\"introduction-contentTitle\"> Translate admin panel</h4>•\n      Translation admin panel is very easy. To translate admin panel, open{\" \"}\n      <br />\n      <span className=\"introduction-step-2\">\n        Settings{\">\"} Translations menu\n      </span>\n      and Translate all words into your language.\n      <br />\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            {/* <!--Simple Image--> */}\n            <a\n              href=\"./assets/img/doc/translation2.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/translation2.jpg\"\n                alt=\"image01\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/translation1.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/translation1.jpg\"\n                alt=\"image02\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/mandatory-setup\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Mandatory setup Admin Panel </p>\n        </Link>\n        <Link\n          to=\"/foodyman-documentation/troubleshooting-admin\"\n          className=\"btn  next\"\n        >\n          <p>Next</p>\n          <p className=\"link\"> Troubleshooting admin</p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default Customization;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { GiCampfire } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\n\nconst MandatorySetupMobile = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Mandatory setup</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        The same documentation for Delivery app\n      </div>\n      <h4 className=\"introduction-contentTitle\">\n        {\" \"}\n        Run an existing flutter project on IDE{\" \"}\n      </h4>\n      <h4 className=\"introduction-contentTitle\"> Change App Logo </h4>\n      You can generate app icon using this website\n      https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html <br />\n      • Then go to{\" \"}\n      <span className=\"introduction-step\"> /android/app/src/main/res</span>\n      and replace all mipmap folder with your /android folder <br />• Again go\n      to <span className=\"introduction-step\">/ios/Runner</span> and replace\n      Assets.xcassets with your generated Assets.xcassets folder <br />\n      <h4 className=\"introduction-contentTitle\"> Change App Name </h4>\n      <div className=\"mt-4 mb-3\">\n        1.Change the value of label from\n        <span className=\"introduction-step\">\n          /android/app/src/main/AndroidManifest.xml\n        </span>\n        <div className=\"introduction-code\">\n          <p>/android/app/src/main/AndroidManifest.xml</p>\n          <hr />\n          <div>\n            <span>android:label=\"My App\"</span>\n            <span\n              className={text === 2 ? \"bg-success copy\" : \"copy\"}\n              onClick={() => copyToClipBoard('android:label=\"My App\"’', 2)}\n            >\n              {text === 2 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <div className=\"mt-4 mb-3\">\n        2.Change the value of CFBundleName from\n        <span className=\"introduction-step\"> /iOS/Runner/info.plist</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/info.plist</p>\n          <hr />\n          <div>\n            <span>\n              {`<key>CFBundleName</key>`} <br />\n              {`<string>My App</string>`}\n            </span>\n            <span\n              className={text === 3 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"<key>CFBundleName</key><string>My App</string>\",\n                  3\n                )\n              }\n            >\n              {text === 3 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <h4 className=\"introduction-contentTitle\"> Change Base URL</h4>\n      Please do NOT put slash ( / ) at the end of your base url. Use your admin\n      url as base url. First you have to install your admin panel. For example:\n      If your admin url is{\" \"}\n      <span className=\"introduction-step\"> https://your_domain.com/admin</span>\n      then base url will be https://your_domain.com. Open{\" \"}\n      <span className=\"introduction-step\">\n        {\" \"}\n        /lib/src/core/constants/app_constants.dart\n      </span>\n      and replace baseUrl variable value with your own URL.\n      <div className=\"introduction-code\">\n        <p>/lib/src/core/constants/app_constants.dart</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            static const String baseUrl=\n            <span className=\"text-black\">'https://your_domain.com'</span>\n          </span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\"baseUrl=https://your_domain.com\", 4)\n            }\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/mobile-1.jpg\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/mobile-1.jpg\"\n                alt=\"image01\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Package</h4>\n      Firstly, find out the existing package name. You can find it out from top\n      of\n      <span className=\"introduction-step\">\n        /app/src/main/AndroidManifest.xml\n      </span>\n      file. Then right click on project folder from android studio and click on\n      <span className=\"introduction-step\">replace in path</span>\n      You will see a popup window with two input boxes. In first box you have to\n      put existing package name that you saw in{\" \"}\n      <span className=\"introduction-step\">AndroidManifest.xml</span>\n      file previously and then write down your preferred package name in second\n      box and then click on{\" \"}\n      <span className=\"introduction-step\">Replace All</span> button.\n      <h4 className=\"introduction-contentTitle\">\n        Setup Firebase for Push Notification\n      </h4>\n      • Firstly, change your package name. If you didn’t then go to this link{\" \"}\n      <br />• Create your own firebase project from{\" \"}\n      <strong className=\"strong\">https://console.firebase.google.com </strong>\n      and also add there an android app with your own package name and app name{\" \"}\n      <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do NOT create multiple projects if you have multiple app like\n        User App, Delivery App. Create only one project and add multiple apps\n        under one project.\n      </div>\n      • Click register app and download google-services.json file from there.{\" \"}\n      <br />• Copy that file and paste it under\n      <span className=\"introduction-step\"> /android/app/ folder</span>\n      <br />• Create a totally white png logo for notification icon. Paste it on{\" \"}\n      <span className=\"introduction-step\">\n        {\" \"}\n        /android/app/src/main/res/drawable/\n      </span>\n      and replace notification_icon.png with your whiter version logo. <br />•\n      For IOS again create an app under the same project and download{\" \"}\n      <span className=\"introduction-step\">GoogleService-Info.plist</span>\n      and paste it under\n      <span className=\"introduction-step\">/iOS/ folder</span> <br />\n      Also you are advised to follow this documentation for full setup for IOS:\n      <strong className=\"strong\">\n        {\" \"}\n        https://firebase.flutter.dev/docs/messaging/apple-integration{\" \"}\n      </strong>\n      <br />• Paste firebase server key into admin panel Notification Settings\n      section. You can receive server key from{\" \"}\n      <span className=\"introduction-step\">\n        Firebase project settings{\"->\"}Cloud Messaging{\"->\"} Server Key{\" \"}\n      </span>\n      <br />\n      After setup, please restart your IDE and uninstall your previously\n      installed app, and then run it. Also do NOT try to test it on emulator or\n      simulator. Emulator and simulators are unable to get push. Use real device\n      for this purpose.\n      <h4 className=\"introduction-contentTitle\"> Before upload app store mode set true</h4>\n      <img\n          src=\"./assets/img/uzmart-doc/appstoremode.jpg\"\n          alt=\"images\"\n          loading=\"lazy\"\n          className=\"img-responsive-full\"\n      />\n      <h4 className=\"introduction-contentTitle\"> Add Google Map API Key</h4>\n      • Please generate the google API key. You can visit this link -\n      https://developers.google.com/maps/documentation/embed/get-api-key <br />\n      • You need to enable mentioned APIs: Direction API, Distance Matrix API,\n      Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. <br />\n      • Then you have to enable billing account. Visit this URL for activation:\n      https://support.google.com/googleapi/answer/6158867?hl=en <br />\n      • After generating API key, you have to paste it on 3 different places:\n      Android, iOS and web. <br />\n      For android, open\n      <span className=\"introduction-step\">\n        /android/app/src/main/AndroidManifest.xml\n      </span>{\" \"}\n      and place the value of{\" \"}\n      <span className=\"introduction-step\">com.google.android.geo.API_KEY </span>\n      <div className=\"introduction-code\">\n        <p> /android/app/src/main/AndroidManifest.xml</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            {\n              '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>'\n            }\n          </span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>',\n                5\n              )\n            }\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"mt-4\">\n        For iOS: open{\" \"}\n        <span className=\"introduction-step\">/iOS/Runner/AppDelegate.swift</span>\n        and place the value of{\" \"}\n        <span className=\"introduction-step\">GMSServices.provideAPIKey</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/AppDelegate.swift</p>\n          <hr />\n          <div>\n            <span className=\"ps-0\">\n              GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  'GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")',\n                  6\n                )\n              }\n            >\n              {text === 6 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/build-code-and-setup-on-server\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Build code and setup -on-server </p>\n        </Link>\n        <Link\n          to=\"/foodyman-documentation/customization-mobile\"\n          className=\"btn  next\"\n        >\n          <p>Next</p>\n          <p className=\"link\"> Customization Mobile App </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default MandatorySetupMobile;\n", "import React from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { <PERSON> } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MandatorySetupBackend = () => {\n  return (\n    <div\n      className=\"introduction\"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Install on server backend</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Thus to install admin_backend, we need subdomains like admin.xyz.com and\n        api.xyz.com.\n      </div>\n      <div className=\"alert alert-danger mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>Warning</strong>\n        </div>\n        Don’t install the admin_backend in a sub directory (like:\n        yourdomain.com/folder)\n      </div>\n      <p>\n        • When you connect subdomain to backend code, set public folder as root\n        folder for this subdomains.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/domain.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/domain.png\"\n                alt=\"admin\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>\n        • Compress your Laravel project files into a ZIP file. If git,\n        node_modules folders exist in your Laravel project, be sure not to add\n        them in the zip file.\n      </p>\n      <p>• Login to your cPanel</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/c-panel.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/c-panel.png\"\n                alt=\"admin\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>\n        • Create a database & user. (Make sure to save the database name, user,\n        and password into a text file in a safe place.)\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/create-db.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/create-db.png\"\n                alt=\"admin\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>• Add new user</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/create-user.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/create-user.png\"\n                alt=\"admin\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>•Add User to Database</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/add-user.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/add-user.png\"\n                alt=\"admin\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>\n        • Open your File Manager in cPanel and upload the ZIP file of your\n        Laravel project into the public_html directory and extract the ZIP file.\n        The ZIP file should be uploaded and extracted directly in the\n        public_html folder and not inside any subfolder.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/backend-file.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/backend-file.png\"\n                alt=\"admin\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>\n        • Now update your database details into the config file by opening the\n        database.php file from the config folder and updating your database\n        name, username and password into the database.php file. Now save the\n        file. (Any sensitive credentials should not be uploaded in the env file\n        in shared hosting.)\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/config.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/config.png\"\n                alt=\"admin\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>\n        • To make your project secure and protect your .htaccess and .env files,\n        open the .htaccess file from the public_html folder and write the\n        following code to disable direct access and directory browsing:\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/config-2.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/config-2.png\"\n                alt=\"admin\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>• Open terminal. Go admin_backend folder.</p>\n      <div className=\"introduction-code\">\n        <p>composer install</p>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"1.75rem\" }}></div>\n      <SimpleReactLightbox>\n        <SRLWrapper>\n          <a\n            href=\"./assets/img/food-doc/composer.jpg\"\n            data-fancybox\n            rel=\"nofollow\"\n          >\n            <img\n              src=\"./assets/img/food-doc/composer.jpg\"\n              alt=\"admin\"\n              loading=\"lazy\"\n              className=\"img-responsive-full\"\n            />\n          </a>\n        </SRLWrapper>\n      </SimpleReactLightbox>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>\n        to install all php dependencies if you have not uploaded vendor folder.\n        Then change your environmental variables in the .env file Generate a new\n        application key\n      </p>\n      <div className=\"introduction-code\">\n        <p>php artisan key:generate</p>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"1.75rem\" }}></div>\n      <SimpleReactLightbox>\n        <SRLWrapper>\n          <a\n            href=\"./assets/img/food-doc/generate.jpg\"\n            data-fancybox\n            rel=\"nofollow\"\n          >\n            <img\n              src=\"./assets/img/food-doc/generate.jpg\"\n              alt=\"admin\"\n              loading=\"lazy\"\n              className=\"img-responsive-full\"\n            />\n          </a>\n        </SRLWrapper>\n      </SimpleReactLightbox>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Don’t forget to give a permission to write to storage, config and\n        bootstrap folders.\n      </div>\n      <ul>\n        <li>Create /config/init.php file with empty content</li>\n        <div className=\"introduction-img-container\">\n          <SimpleReactLightbox>\n            <SRLWrapper>\n              <a\n                href=\"./assets/img/doc/init-php.jpg\"\n                data-fancybox\n                rel=\"nofollow\"\n              >\n                <img\n                  src=\"./assets/img/doc/init-php.jpg\"\n                  alt=\"images\"\n                  loading=\"lazy\"\n                  className=\"img-responsive-full\"\n                />\n              </a>\n            </SRLWrapper>\n          </SimpleReactLightbox>\n        </div>\n        <li>\n          <strong>\n            Change lisence keys in /config/credential.php with your’s\n          </strong>\n        </li>\n      </ul>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/php_backend_food.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/php_backend_food.jpg\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./sertificate.png\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./sertificate.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        <ul>\n          <li>purchase_id = Item ID</li>\n          <li>purchase_code = Item Purchase Code</li>\n        </ul>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>\n        Migrate and seed your database as per you need.\n        <br /> 1. Migrate Tables: <strong>\n          `php artisan migrate`\n        </strong> and{\" \"}\n        <strong>\n          `php artisan migrate --path=database/migrations/booking`\n        </strong>\n        <br /> 2. Seed Database: <strong>`php artisan db:seed`</strong>\n        <br />\n        Set the correct folder permissions\n      </p>\n      <SimpleReactLightbox>\n        <SRLWrapper>\n          <a\n            href=\"./assets/img/food-doc/migrate.jpg\"\n            data-fancybox\n            rel=\"nofollow\"\n          >\n            <img\n              src=\"./assets/img/food-doc/migrate.jpg\"\n              alt=\"admin\"\n              loading=\"lazy\"\n              className=\"img-responsive-full\"\n            />\n          </a>\n        </SRLWrapper>\n      </SimpleReactLightbox>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"1.75rem\" }}></div>\n      <SimpleReactLightbox>\n        <SRLWrapper>\n          <a\n            href=\"./assets/img/food-doc/migrate-2.jpg\"\n            data-fancybox\n            rel=\"nofollow\"\n          >\n            <img\n              src=\"./assets/img/food-doc/migrate-2.jpg\"\n              alt=\"admin\"\n              loading=\"lazy\"\n              className=\"img-responsive-full\"\n            />\n          </a>\n        </SRLWrapper>\n      </SimpleReactLightbox>\n      <div className=\"introduction-code\">\n        <p>chmod -R 775 storage</p>\n      </div>\n      <p>Run storage link</p>\n      <div className=\"introduction-code\">\n        <p className=\"mb-0\">php artisan storage:link</p>\n        <p>php artisan optimize:clear</p>\n      </div>\n      <p>Your app should work now.</p>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>Usage Commands</strong>\n        </div>\n        1. Migrate Tables: `php artisan migrate` <br /> 2. Seed Database: `php\n        artisan db:seed` <br /> 3. Start Server: `php artisan serve` <br />\n        4. Watch Server: `yarn watch` or `npm run watch`\n      </div>\n      <h5>Other Commands:</h5>\n      <div className=\"introduction-code\">\n        <p className=\"mb-0\">\n          # Turn on maintenance mode <br /> php artisan down\n        </p>\n        <p className=\"mb-0\">\n          # Turn off maintenance mode <br /> php artisan up\n        </p>\n        <p className=\"mb-0\">\n          # Sync composer.local with composer.json <br /> composer update --lock\n        </p>\n        <p className=\"mb-0\">\n          # Create Model <br /> php artisan make:model Flight\n        </p>\n        <p className=\"mb-0\">\n          # Create Model & Migration <br />\n          php artisan make:model Flight -m\n        </p>\n        <p className=\"mb-0\">\n          # Create Migration <br /> php artisan make:migration\n          create_users_table --create=users\n        </p>\n        <p className=\"mb-0\">\n          # Update Existing Migration <br /> php artisan make:migration\n          add_votes_to_users_table --table=users\n        </p>\n        <p className=\"mb-0\">\n          # Create Controller <br /> php artisan make:controller PhotoController\n        </p>\n        <p className=\"mb-0\">\n          # Composer dump <br /> composer dump-autoload\n        </p>\n        <p className=\"mb-0\">\n          # Clear caches <br /> php artisan cache:clear\n        </p>\n        <p className=\"mb-0\">\n          # Clear and cache routes <br /> php artisan route:clear <br /> php\n          artisan route:cache\n        </p>\n        <p className=\"mb-0\">\n          # Clear and cache config <br /> php artisan config:clear <br /> php\n          artisan config:cache\n        </p>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>• How to setup foodyman project from cpanel - Backend</p>\n      <iframe\n        width=\"100%\"\n        height=\"420\"\n        src=\"https://www.youtube.com/embed/TKyjjO8yZY0\"\n        title=\"YouTube video player\"\n        frameBorder=\"0\"\n        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n        allowFullScreen\n      />\n      <br />\n      <br />\n      <p>\n        • To add the translation of the errors in the backend, clone the \"en\"\n        folder and put the language short code in the name of the folder.\n      </p>\n      <SimpleReactLightbox>\n        <SRLWrapper>\n          <a\n            href=\"./assets/img/doc/backend_error_lang1.jpg\"\n            data-fancybox\n            rel=\"nofollow\"\n          >\n            <img\n              src=\"./assets/img/doc/backend_error_lang1.jpg\"\n              alt=\"admin\"\n              loading=\"lazy\"\n              className=\"img-responsive-full\"\n            />\n          </a>\n        </SRLWrapper>\n      </SimpleReactLightbox>\n      <br />\n      <p>\n        • In the picture, for example, the messages in the Arabic language\n        errors.php file have been changed.\n      </p>\n      <br />\n      <SimpleReactLightbox>\n        <SRLWrapper>\n          <a\n            href=\"./assets/img/doc/backend_error_lang1.jpg\"\n            data-fancybox\n            rel=\"nofollow\"\n          >\n            <img\n              src=\"./assets/img/doc/backend_error_lang1.jpg\"\n              alt=\"admin\"\n              loading=\"lazy\"\n              className=\"img-responsive-full\"\n            />\n          </a>\n        </SRLWrapper>\n      </SimpleReactLightbox>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MandatorySetupBackend;\n", "import React from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport NavigationBtns from \"../Foodyman-doc/Navigation-btns\";\n\nconst PaymentInstallation = () => {\n  return (\n    <div\n      className=\"introduction\"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Install Payments</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        All other payment methods are done in a similar way through the admin\n        panel and adding a webhook on the payment system website\n      </div>\n      <div className=\"alert alert-danger mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>Warning</strong>\n        </div>\n        Webhook urls: <br />- stripe:{\" \"}\n        <b>https://your-api-url.com/api/v1/webhook/stripe/payment</b> <br />-\n        razorpay:{\" \"}\n        <b>https://your-api-url.com/api/v1/webhook/razorpay/payment</b> <br />-\n        paystack:{\" \"}\n        <b>https://your-api-url.com/api/v1/webhook/paystack/payment</b> <br />-\n        paytabs: <b>https://your-api-url.com/api/v1/webhook/paytabs/payment</b>{\" \"}\n        <br />- flutterwave:{\" \"}\n        <b>https://your-api-url.com/api/v1/webhook/flw/payment</b> <br />-\n        paypal: <b>https://your-api-url.com/api/v1/webhook/paypal/payment</b>{\" \"}\n        <br />- mercado-pago:{\" \"}\n        <b>https://your-api-url.com/api/v1/webhook/mercado-pago/payment</b>{\" \"}\n        <br />- moyasar:{\" \"}\n        <b>https://your-api-url.com/api/v1/webhook/moya-sar/payment</b> <br />-\n        mollie: <b>https://your-api-url.com/api/v1/webhook/mollie/payment</b>{\" \"}\n        <br />\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>• Stripe integration.</p>\n      <div className=\"introduction-img-container\">\n        <iframe\n          width=\"100%\"\n          height=\"400\"\n          src=\"https://www.youtube.com/embed/ZROHalq6kNg?si=uhK0fckee2R1Xyja\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          referrerPolicy=\"strict-origin-when-cross-origin\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>• Paystack integration.</p>\n      <div className=\"introduction-img-container\">\n        <iframe\n          width=\"100%\"\n          height=\"400\"\n          src=\"https://www.youtube.com/embed/nPFoKorY_cw?si=A_FXSJIklHJuAYsS\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          referrerPolicy=\"strict-origin-when-cross-origin\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p>• Razorpay integration.</p>\n      <div className=\"introduction-img-container\">\n        <iframe\n          width=\"100%\"\n          height=\"400\"\n          src=\"https://www.youtube.com/embed/ITjVuL8ub-Q?si=9qoDmJyz6HWEibRh\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          referrerPolicy=\"strict-origin-when-cross-origin\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default PaymentInstallation;\n", "import React from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { <PERSON> } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst ImageSettings = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Image Settings</h1>\n      <div className=\"alert alert-danger mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        You can upload images to server storage or aws s3 space.\n      </div>\n      <h4>\n        1. If you want to use server storage to upload, do following steps:\n      </h4>\n      <div className=\"introduction-img-container mb-5\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/img-settings.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/img-settings.png\"\n                alt=\"admin install\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <ol>\n        <li>\n          Open .env file and change <strong>example.com</strong> with your\n          backend website url\n        </li>\n        <li>Open public folder and remove storage</li>\n        <li>\n          Run following commands on terminal: \"php artisan storage link\" and\n          \"php artisan optimize:clear\n        </li>\n        <li>\n          Open admin website, go to General settings page and press permission\n          tab. Disable aws and save.\n        </li>\n      </ol>\n      <div className=\"introduction-img-container mb-5\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/img_settings.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/img_settings.jpg\"\n                alt=\"admin install\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n\n      <h4>\n        2. If you want to use aws s3 bucket to upload images, do following\n        steps:\n      </h4>\n      <ol>\n        <li>Configure your aws s3 bucket</li>\n        <li>\n          Open .env and put your aws bucket link and all necessary aws keys.\n          Then save.\n        </li>\n        <div className=\"introduction-img-container mb-5\">\n          <SimpleReactLightbox>\n            <SRLWrapper>\n              <a\n                href=\"./assets/img/doc/img-setting-storage.png\"\n                data-fancybox\n                rel=\"nofollow\"\n              >\n                <img\n                  src=\"./assets/img/doc/img-setting-storage.png\"\n                  alt=\"admin install\"\n                  loading=\"lazy\"\n                  className=\"img-responsive-full\"\n                />\n              </a>\n            </SRLWrapper>\n          </SimpleReactLightbox>\n        </div>\n        <li>run following command on terminal: \"php artisan optimize:clear\"</li>\n      </ol>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default ImageSettings;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { GiCampfire } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MandatorSetupVendor = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Mandatory setup</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        The same documentation for Delivery app\n      </div>\n      <h4 className=\"introduction-contentTitle\">\n        {\" \"}\n        Run an existing flutter project on IDE{\" \"}\n      </h4>\n      <h4 className=\"introduction-contentTitle\"> Change App Logo </h4>\n      You can generate app icon using this website\n      https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html <br />\n      • Then go to{\" \"}\n      <span className=\"introduction-step\"> /android/app/src/main/res</span>\n      and replace all mipmap folder with your /android folder <br />• Again go\n      to <span className=\"introduction-step\">/ios/Runner</span> and replace\n      Assets.xcassets with your generated Assets.xcassets folder <br />\n      <h4 className=\"introduction-contentTitle\"> Change App Name </h4>\n      <div className=\"mt-4 mb-3\">\n        1.Change the value of label from\n        <span className=\"introduction-step\">\n          /android/app/src/main/AndroidManifest.xml\n        </span>\n        <div className=\"introduction-code\">\n          <p>/android/app/src/main/AndroidManifest.xml</p>\n          <hr />\n          <div>\n            <span>android:label=\"My App\"</span>\n            <span\n              className={text === 2 ? \"bg-success copy\" : \"copy\"}\n              onClick={() => copyToClipBoard('android:label=\"My App\"’', 2)}\n            >\n              {text === 2 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <div className=\"mt-4 mb-3\">\n        2.Change the value of CFBundleName from\n        <span className=\"introduction-step\"> /iOS/Runner/info.plist</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/info.plist</p>\n          <hr />\n          <div>\n            <span>\n              {`<key>CFBundleName</key>`} <br />\n              {`<string>My App</string>`}\n            </span>\n            <span\n              className={text === 3 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"<key>CFBundleName</key><string>My App</string>\",\n                  3\n                )\n              }\n            >\n              {text === 3 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <h4 className=\"introduction-contentTitle\"> Change Base URL</h4>\n      Please do NOT put slash ( / ) at the end of your base url. Use your admin\n      url as base url. First you have to install your admin panel. For example:\n      If your admin url is{\" \"}\n      <span className=\"introduction-step\"> https://your_domain.com/admin</span>\n      then base url will be https://your_domain.com. Open{\" \"}\n      <span className=\"introduction-step\"> /lib/app_constants.dart</span>\n      and replace baseUrl variable value with your own URL.\n      <div className=\"introduction-code\">\n        <p>/lib/app_constants.dart</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            static const String baseUrl=\n            <span className=\"text-black\">'https://your_domain.com'</span>\n          </span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\"baseUrl=https://your_domain.com\", 4)\n            }\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/app_const.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/app_const.png\"\n                alt=\"image0\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Package</h4>\n      Firstly, find out the existing package name. You can find it out from top\n      of\n      <span className=\"introduction-step\">\n        /app/src/main/AndroidManifest.xml\n      </span>\n      file. Then right click on project folder from android studio and click on\n      <span className=\"introduction-step\">replace in path</span>\n      You will see a popup window with two input boxes. In first box you have to\n      put existing package name that you saw in{\" \"}\n      <span className=\"introduction-step\">AndroidManifest.xml</span>\n      file previously and then write down your preferred package name in second\n      box and then click on{\" \"}\n      <span className=\"introduction-step\">Replace All</span> button.\n      <h4 className=\"introduction-contentTitle\">Setup Firebase</h4>• Firstly,\n      change your package name. If you didn’t then go to this link <br />•\n      Create your own firebase project from{\" \"}\n      <strong className=\"strong\">https://console.firebase.google.com </strong>\n      and also add there an android app with your own package name and app name{\" \"}\n      <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do NOT create multiple projects if you have multiple app like\n        User App, Delivery App. Create only one project and add multiple apps\n        under one project.\n      </div>\n      • Click register app and download google-services.json file from there.{\" \"}\n      <br />• Copy that file and paste it under\n      <span className=\"introduction-step\"> /android/app/ folder</span>\n      <br />• Create a totally white png logo for notification icon. Paste it on{\" \"}\n      <span className=\"introduction-step\">\n        {\" \"}\n        /android/app/src/main/res/drawable/\n      </span>\n      and replace notification_icon.png with your whiter version logo. <br />•\n      For IOS again create an app under the same project and download{\" \"}\n      <span className=\"introduction-step\">GoogleService-Info.plist</span>\n      and paste it under\n      <span className=\"introduction-step\">/iOS/ folder</span> <br />\n      Also you are advised to follow this documentation for full setup for IOS:\n      <strong className=\"strong\">\n        {\" \"}\n        https://firebase.flutter.dev/docs/messaging/apple-integration{\" \"}\n      </strong>\n      <br />\n      After setup, please restart your IDE and uninstall your previously\n      installed app, and then run it. Also do NOT try to test it on emulator or\n      simulator. Emulator and simulators are unable to get push. Use real device\n      for this purpose.\n      <h4 className=\"introduction-contentTitle\"> Add Google Map API Key</h4>\n      • Please generate the google API key. You can visit this link -\n      https://developers.google.com/maps/documentation/embed/get-api-key <br />\n      • You need to enable mentioned APIs: Direction API, Distance Matrix API,\n      Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. <br />\n      • Then you have to enable billing account. Visit this URL for activation:\n      https://support.google.com/googleapi/answer/6158867?hl=en <br />\n      • After generating API key, you have to paste it on 3 different places:\n      Android, iOS and web. <br />\n      For android, open\n      <span className=\"introduction-step\">\n        /android/app/src/main/AndroidManifest.xml\n      </span>{\" \"}\n      and place the value of{\" \"}\n      <span className=\"introduction-step\">com.google.android.geo.API_KEY </span>\n      <div className=\"introduction-code\">\n        <p> /android/app/src/main/AndroidManifest.xml</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            {\n              '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>'\n            }\n          </span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>',\n                5\n              )\n            }\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"mt-4\">\n        For iOS: open{\" \"}\n        <span className=\"introduction-step\">/iOS/Runner/AppDelegate.swift</span>\n        and place the value of{\" \"}\n        <span className=\"introduction-step\">GMSServices.provideAPIKey</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/AppDelegate.swift</p>\n          <hr />\n          <div>\n            <span className=\"ps-0\">\n              GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  'GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")',\n                  6\n                )\n              }\n            >\n              {text === 6 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MandatorSetupVendor;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { GiCampfire } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MandatorySetupCustomer = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Mandatory setup</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        The same documentation for Delivery app\n      </div>\n      <h4 className=\"introduction-contentTitle\">\n        {\" \"}\n        Run an existing flutter project on IDE{\" \"}\n      </h4>\n      <h4 className=\"introduction-contentTitle\"> Change App Logo </h4>\n      You can generate app icon using this website\n      https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html <br />\n      • Then go to{\" \"}\n      <span className=\"introduction-step\"> /android/app/src/main/res</span>\n      and replace all mipmap folder with your /android folder <br />• Again go\n      to <span className=\"introduction-step\">/ios/Runner</span> and replace\n      Assets.xcassets with your generated Assets.xcassets folder <br />\n      <h4 className=\"introduction-contentTitle\"> Change App Name </h4>\n      <div className=\"mt-4 mb-3\">\n        1.Change the value of label from\n        <span className=\"introduction-step\">\n          /android/app/src/main/AndroidManifest.xml\n        </span>\n        <div className=\"introduction-code\">\n          <p>/android/app/src/main/AndroidManifest.xml</p>\n          <hr />\n          <div>\n            <span>android:label=\"My App\"</span>\n            <span\n              className={text === 2 ? \"bg-success copy\" : \"copy\"}\n              onClick={() => copyToClipBoard('android:label=\"My App\"’', 2)}\n            >\n              {text === 2 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <div className=\"mt-4 mb-3\">\n        2.Change the value of CFBundleName from\n        <span className=\"introduction-step\"> /iOS/Runner/info.plist</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/info.plist</p>\n          <hr />\n          <div>\n            <span>\n              {`<key>CFBundleName</key>`} <br />\n              {`<string>My App</string>`}\n            </span>\n            <span\n              className={text === 3 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"<key>CFBundleName</key><string>My App</string>\",\n                  3\n                )\n              }\n            >\n              {text === 3 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <h4 className=\"introduction-contentTitle\"> Change Base URL</h4>\n      Please do NOT put slash ( / ) at the end of your base url. Use your admin\n      url as base url. First you have to install your admin panel. For example:\n      If your admin url is{\" \"}\n      <span className=\"introduction-step\"> https://your_domain.com/admin</span>\n      then base url will be https://your_domain.com. Open{\" \"}\n      <span className=\"introduction-step\"> /lib/app_constants.dart</span>\n      and replace baseUrl variable value with your own URL.\n      <div className=\"introduction-code\">\n        <p>/lib/app_constants.dart</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            static const String baseUrl=\n            <span className=\"text-black\">'https://your_domain.com'</span>\n          </span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\"baseUrl=https://your_domain.com\", 4)\n            }\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/app_const.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/app_const.png\"\n                alt=\"image02\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Package</h4>\n      Firstly, find out the existing package name. You can find it out from top\n      of\n      <span className=\"introduction-step\">\n        /app/src/main/AndroidManifest.xml\n      </span>\n      file. Then right click on project folder from android studio and click on\n      <span className=\"introduction-step\">replace in path</span>\n      You will see a popup window with two input boxes. In first box you have to\n      put existing package name that you saw in{\" \"}\n      <span className=\"introduction-step\">AndroidManifest.xml</span>\n      file previously and then write down your preferred package name in second\n      box and then click on{\" \"}\n      <span className=\"introduction-step\">Replace All</span> button.\n      <h4 className=\"introduction-contentTitle\">Setup Firebase</h4>• Firstly,\n      change your package name. If you didn’t then go to this link <br />•\n      Create your own firebase project from{\" \"}\n      <strong className=\"strong\">https://console.firebase.google.com </strong>\n      and also add there an android app with your own package name and app name{\" \"}\n      <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do NOT create multiple projects if you have multiple app like\n        User App, Delivery App. Create only one project and add multiple apps\n        under one project.\n      </div>\n      • Click register app and download google-services.json file from there.{\" \"}\n      <br />• Copy that file and paste it under\n      <span className=\"introduction-step\"> /android/app/ folder</span>\n      <br />• Create a totally white png logo for notification icon. Paste it on{\" \"}\n      <span className=\"introduction-step\">\n        {\" \"}\n        /android/app/src/main/res/drawable/\n      </span>\n      and replace notification_icon.png with your whiter version logo. <br />•\n      For IOS again create an app under the same project and download{\" \"}\n      <span className=\"introduction-step\">GoogleService-Info.plist</span>\n      and paste it under\n      <span className=\"introduction-step\">/iOS/ folder</span> <br />\n      Also you are advised to follow this documentation for full setup for IOS:\n      <strong className=\"strong\">\n        {\" \"}\n        https://firebase.flutter.dev/docs/messaging/apple-integration{\" \"}\n      </strong>\n      <br />\n      After setup, please restart your IDE and uninstall your previously\n      installed app, and then run it. Also do NOT try to test it on emulator or\n      simulator. Emulator and simulators are unable to get push. Use real device\n      for this purpose.\n      <h4 className=\"introduction-contentTitle\"> Add Google Map API Key</h4>\n      • Please generate the google API key. You can visit this link -\n      https://developers.google.com/maps/documentation/embed/get-api-key <br />\n      • You need to enable mentioned APIs: Direction API, Distance Matrix API,\n      Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. <br />\n      • Then you have to enable billing account. Visit this URL for activation:\n      https://support.google.com/googleapi/answer/6158867?hl=en <br />\n      • After generating API key, you have to paste it on 3 different places:\n      Android, iOS and web. <br />\n      For android, open\n      <span className=\"introduction-step\">\n        /android/app/src/main/AndroidManifest.xml\n      </span>{\" \"}\n      and place the value of{\" \"}\n      <span className=\"introduction-step\">com.google.android.geo.API_KEY </span>\n      <div className=\"introduction-code\">\n        <p> /android/app/src/main/AndroidManifest.xml</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            {\n              '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>'\n            }\n          </span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>',\n                5\n              )\n            }\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"mt-4\">\n        For iOS: open{\" \"}\n        <span className=\"introduction-step\">/iOS/Runner/AppDelegate.swift</span>\n        and place the value of{\" \"}\n        <span className=\"introduction-step\">GMSServices.provideAPIKey</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/AppDelegate.swift</p>\n          <hr />\n          <div>\n            <span className=\"ps-0\">\n              GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  'GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")',\n                  6\n                )\n              }\n            >\n              {text === 6 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MandatorySetupCustomer;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { GiCampfire } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MandatorySetupPos = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> POS setup</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        The same documentation for Delivery app\n      </div>\n      <h4 className=\"introduction-contentTitle\">\n        {\" \"}\n        Run an existing flutter project on IDE{\" \"}\n      </h4>\n      <h4 className=\"introduction-contentTitle\"> Change App Logo </h4>\n      You can generate app icon using this website\n      https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html <br />\n      • Then go to{\" \"}\n      <span className=\"introduction-step\"> /android/app/src/main/res</span>\n      and replace all mipmap folder with your /android folder <br />• Again go\n      to <span className=\"introduction-step\">/ios/Runner</span> and replace\n      Assets.xcassets with your generated Assets.xcassets folder <br />\n      <h4 className=\"introduction-contentTitle\"> Change App Name </h4>\n      <div className=\"mt-4 mb-3\">\n        1.Change the value of label from\n        <span className=\"introduction-step\">\n          /android/app/src/main/AndroidManifest.xml\n        </span>\n        <div className=\"introduction-code\">\n          <p>/android/app/src/main/AndroidManifest.xml</p>\n          <hr />\n          <div>\n            <span>android:label=\"My App\"</span>\n            <span\n              className={text === 2 ? \"bg-success copy\" : \"copy\"}\n              onClick={() => copyToClipBoard('android:label=\"My App\"’', 2)}\n            >\n              {text === 2 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <div className=\"mt-4 mb-3\">\n        2.Change the value of CFBundleName from\n        <span className=\"introduction-step\"> /iOS/Runner/info.plist</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/info.plist</p>\n          <hr />\n          <div>\n            <span>\n              {`<key>CFBundleName</key>`} <br />\n              {`<string>My App</string>`}\n            </span>\n            <span\n              className={text === 3 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"<key>CFBundleName</key><string>My App</string>\",\n                  3\n                )\n              }\n            >\n              {text === 3 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <h4 className=\"introduction-contentTitle\"> Change Base URL</h4>\n      Please do NOT put slash ( / ) at the end of your base url. Use your admin\n      url as base url. First you have to install your admin panel. For example:\n      If your admin url is{\" \"}\n      <span className=\"introduction-step\"> https://your_domain.com/admin</span>\n      then base url will be https://your_domain.com. Open{\" \"}\n      <span className=\"introduction-step\">\n        {\" \"}\n        /lib/src/core/constants/secret_vars.dart\n      </span>\n      and replace baseUrl variable value with your own URL.\n      <div className=\"introduction-code\">\n        <p>/lib/src/core/constants/secret_vars.dart</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            static const String baseUrl=\n            <span className=\"text-black\">'https://your_domain.com'</span>\n          </span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\"baseUrl=https://your_domain.com\", 4)\n            }\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/pos-translation2.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/pos-translation2.jpg\"\n                alt=\"image02\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Package</h4>\n      Firstly, find out the existing package name. You can find it out from top\n      of\n      <span className=\"introduction-step\">\n        /app/src/main/AndroidManifest.xml\n      </span>\n      file. Then right click on project folder from android studio and click on\n      <span className=\"introduction-step\">replace in path</span>\n      You will see a popup window with two input boxes. In first box you have to\n      put existing package name that you saw in{\" \"}\n      <span className=\"introduction-step\">AndroidManifest.xml</span>\n      file previously and then write down your preferred package name in second\n      box and then click on{\" \"}\n      <span className=\"introduction-step\">Replace All</span> button.\n      <h4 className=\"introduction-contentTitle\">\n        Setup Firebase for Push Notification\n      </h4>\n      • Firstly, change your package name. If you didn’t then go to this link{\" \"}\n      <br />• Create your own firebase project from{\" \"}\n      <strong className=\"strong\">https://console.firebase.google.com </strong>\n      and also add there an android app with your own package name and app name{\" \"}\n      <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do NOT create multiple projects if you have multiple app like\n        User App, Delivery App. Create only one project and add multiple apps\n        under one project.\n      </div>\n      • Click register app and download google-services.json file from there.{\" \"}\n      <br />• Copy that file and paste it under\n      <span className=\"introduction-step\"> /android/app/ folder</span>\n      <br />• Create a totally white png logo for notification icon. Paste it on{\" \"}\n      <span className=\"introduction-step\">\n        {\" \"}\n        /android/app/src/main/res/drawable/\n      </span>\n      and replace notification_icon.png with your whiter version logo. <br />•\n      For IOS again create an app under the same project and download{\" \"}\n      <span className=\"introduction-step\">GoogleService-Info.plist</span>\n      and paste it under\n      <span className=\"introduction-step\">/iOS/ folder</span> <br />\n      Also you are advised to follow this documentation for full setup for IOS:\n      <strong className=\"strong\">\n        {\" \"}\n        https://firebase.flutter.dev/docs/messaging/apple-integration{\" \"}\n      </strong>\n      <br />• Paste firebase server key into admin panel Notification Settings\n      section. You can receive server key from{\" \"}\n      <span className=\"introduction-step\">\n        Firebase project settings{\"->\"}Cloud Messaging{\"->\"} Server Key{\" \"}\n      </span>\n      <br />\n      After setup, please restart your IDE and uninstall your previously\n      installed app, and then run it. Also do NOT try to test it on emulator or\n      simulator. Emulator and simulators are unable to get push. Use real device\n      for this purpose.\n      <h4 className=\"introduction-contentTitle\"> Add Google Map API Key</h4>\n      • Please generate the google API key. You can visit this link -\n      https://developers.google.com/maps/documentation/embed/get-api-key <br />\n      • You need to enable mentioned APIs: Direction API, Distance Matrix API,\n      Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. <br />\n      • Then you have to enable billing account. Visit this URL for activation:\n      https://support.google.com/googleapi/answer/6158867?hl=en <br />\n      • After generating API key, you have to paste it on 3 different places:\n      Android, iOS and web. <br />\n      For android, open\n      <span className=\"introduction-step\">\n        /android/app/src/main/AndroidManifest.xml\n      </span>{\" \"}\n      and place the value of{\" \"}\n      <span className=\"introduction-step\">com.google.android.geo.API_KEY </span>\n      <div className=\"introduction-code\">\n        <p> /android/app/src/main/AndroidManifest.xml</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            {\n              '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>'\n            }\n          </span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>',\n                5\n              )\n            }\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"mt-4\">\n        For iOS: open{\" \"}\n        <span className=\"introduction-step\">/iOS/Runner/AppDelegate.swift</span>\n        and place the value of{\" \"}\n        <span className=\"introduction-step\">GMSServices.provideAPIKey</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/AppDelegate.swift</p>\n          <hr />\n          <div>\n            <span className=\"ps-0\">\n              GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  'GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")',\n                  6\n                )\n              }\n            >\n              {text === 6 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MandatorySetupPos;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { GiCampfire } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MandatorySetupDeliveryboy = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Mandatory setup</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        The same documentation for Delivery app\n      </div>\n      <h4 className=\"introduction-contentTitle\">\n        {\" \"}\n        Run an existing flutter project on IDE{\" \"}\n      </h4>\n      <h4 className=\"introduction-contentTitle\"> Change App Logo </h4>\n      You can generate app icon using this website\n      https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html <br />\n      • Then go to{\" \"}\n      <span className=\"introduction-step\"> /android/app/src/main/res</span>\n      and replace all mipmap folder with your /android folder <br />• Again go\n      to <span className=\"introduction-step\">/ios/Runner</span> and replace\n      Assets.xcassets with your generated Assets.xcassets folder <br />\n      <h4 className=\"introduction-contentTitle\"> Change App Name </h4>\n      <div className=\"mt-4 mb-3\">\n        1.Change the value of label from\n        <span className=\"introduction-step\">\n          /android/app/src/main/AndroidManifest.xml\n        </span>\n        <div className=\"introduction-code\">\n          <p>/android/app/src/main/AndroidManifest.xml</p>\n          <hr />\n          <div>\n            <span>android:label=\"My App\"</span>\n            <span\n              className={text === 2 ? \"bg-success copy\" : \"copy\"}\n              onClick={() => copyToClipBoard('android:label=\"My App\"’', 2)}\n            >\n              {text === 2 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <div className=\"mt-4 mb-3\">\n        2.Change the value of CFBundleName from\n        <span className=\"introduction-step\"> /iOS/Runner/info.plist</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/info.plist</p>\n          <hr />\n          <div>\n            <span>\n              {`<key>CFBundleName</key>`} <br />\n              {`<string>My App</string>`}\n            </span>\n            <span\n              className={text === 3 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"<key>CFBundleName</key><string>My App</string>\",\n                  3\n                )\n              }\n            >\n              {text === 3 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <h4 className=\"introduction-contentTitle\"> Change Base URL</h4>\n      Please do NOT put slash ( / ) at the end of your base url. Use your admin\n      url as base url. First you have to install your admin panel. For example:\n      If your admin url is{\" \"}\n      <span className=\"introduction-step\"> https://your_domain.com/admin</span>\n      then base url will be https://your_domain.com. Open{\" \"}\n      <span className=\"introduction-step\"> /lib/app_constants.dart</span>\n      and replace baseUrl variable value with your own URL.\n      <div className=\"introduction-code\">\n        <p>/lib/app_constants.dart</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            static const String baseUrl=\n            <span className=\"text-black\">'https://your_domain.com'</span>\n          </span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\"baseUrl=https://your_domain.com\", 4)\n            }\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/app_const.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/app_const.png\"\n                alt=\"image05\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Package</h4>\n      Firstly, find out the existing package name. You can find it out from top\n      of\n      <span className=\"introduction-step\">\n        /app/src/main/AndroidManifest.xml\n      </span>\n      file. Then right click on project folder from android studio and click on\n      <span className=\"introduction-step\">replace in path</span>\n      You will see a popup window with two input boxes. In first box you have to\n      put existing package name that you saw in{\" \"}\n      <span className=\"introduction-step\">AndroidManifest.xml</span>\n      file previously and then write down your preferred package name in second\n      box and then click on{\" \"}\n      <span className=\"introduction-step\">Replace All</span> button.\n      <h4 className=\"introduction-contentTitle\">Setup Firebase</h4>• Firstly,\n      change your package name. If you didn’t then go to this link <br />•\n      Create your own firebase project from{\" \"}\n      <strong className=\"strong\">https://console.firebase.google.com </strong>\n      and also add there an android app with your own package name and app name{\" \"}\n      <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do NOT create multiple projects if you have multiple app like\n        User App, Delivery App. Create only one project and add multiple apps\n        under one project.\n      </div>\n      • Click register app and download google-services.json file from there.{\" \"}\n      <br />• Copy that file and paste it under\n      <span className=\"introduction-step\"> /android/app/ folder</span>\n      <br />• Create a totally white png logo for notification icon. Paste it on{\" \"}\n      <span className=\"introduction-step\">\n        {\" \"}\n        /android/app/src/main/res/drawable/\n      </span>\n      and replace notification_icon.png with your whiter version logo. <br />•\n      For IOS again create an app under the same project and download{\" \"}\n      <span className=\"introduction-step\">GoogleService-Info.plist</span>\n      and paste it under\n      <span className=\"introduction-step\">/iOS/ folder</span> <br />\n      Also you are advised to follow this documentation for full setup for IOS:\n      <strong className=\"strong\">\n        {\" \"}\n        https://firebase.flutter.dev/docs/messaging/apple-integration{\" \"}\n      </strong>\n      <br />\n      After setup, please restart your IDE and uninstall your previously\n      installed app, and then run it. Also do NOT try to test it on emulator or\n      simulator. Emulator and simulators are unable to get push. Use real device\n      for this purpose.\n      <h4 className=\"introduction-contentTitle\">\n        {\" \"}\n        Before upload app store mode set true\n      </h4>\n      <img\n        src=\"./assets/img/doc/app_const.png\"\n        alt=\"images\"\n        loading=\"lazy\"\n        className=\"img-responsive-full\"\n      />\n      <h4 className=\"introduction-contentTitle\"> Add Google Map API Key</h4>\n      • Please generate the google API key. You can visit this link -\n      https://developers.google.com/maps/documentation/embed/get-api-key <br />\n      • You need to enable mentioned APIs: Direction API, Distance Matrix API,\n      Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. <br />\n      • Then you have to enable billing account. Visit this URL for activation:\n      https://support.google.com/googleapi/answer/6158867?hl=en <br />\n      • After generating API key, you have to paste it on 3 different places:\n      Android, iOS and web. <br />\n      For android, open\n      <span className=\"introduction-step\">\n        /android/app/src/main/AndroidManifest.xml\n      </span>{\" \"}\n      and place the value of{\" \"}\n      <span className=\"introduction-step\">com.google.android.geo.API_KEY </span>\n      <div className=\"introduction-code\">\n        <p> /android/app/src/main/AndroidManifest.xml</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            {\n              '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>'\n            }\n          </span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>',\n                5\n              )\n            }\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"mt-4\">\n        For iOS: open{\" \"}\n        <span className=\"introduction-step\">/iOS/Runner/AppDelegate.swift</span>\n        and place the value of{\" \"}\n        <span className=\"introduction-step\">GMSServices.provideAPIKey</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/AppDelegate.swift</p>\n          <hr />\n          <div>\n            <span className=\"ps-0\">\n              GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  'GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")',\n                  6\n                )\n              }\n            >\n              {text === 6 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MandatorySetupDeliveryboy;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst CustomizationMobile = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Customization</h1>\n      <h4 className=\"introduction-contentTitle\">Translate mobile app</h4>\n      • Translation admin panel is very easy. To translate admin panel, go to\n      admin panel and open <br />\n      <span className=\"introduction-step-2\">\n        Settings {\">\"} Translations menu\n      </span>\n      and Translate all words into your language\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/translation2.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n        <img\n          src=\"./assets/img/doc/translation1.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Color</h4>• Open{\" \"}\n      <span className=\"introduction-step\">\n        /lib/presentation/styles/style.dart\n      </span>\n      file and change colors as you want. <br />\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/color.jpg\"\n          alt=\"img\"\n          className=\"img-responsive-full\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Font</h4>• in our\n      apps, google font package is installed. You may change app fonts easily by\n      selecting new fonts\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default CustomizationMobile;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst CustomizationMobileDelivery = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Customization</h1>\n      <h4 className=\"introduction-contentTitle\">Translate mobile app</h4>\n      • Translation admin panel is very easy. To translate admin panel, go to\n      admin panel and open <br />\n      <span className=\"introduction-step-2\">\n        Settings {\">\"} Translations menu\n      </span>\n      and Translate all words into your language\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/translation2.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n        <img\n          src=\"./assets/img/doc/translation1.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Color</h4>• Open{\" \"}\n      <span className=\"introduction-step\">\n        /lib/presentation/styles/style.dart\n      </span>\n      file and change colors as you want. <br />\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/color2.jpg\"\n          alt=\"img\"\n          className=\"img-responsive-full\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Font</h4>• in our\n      apps, google font package is installed. You may change app fonts easily by\n      selecting new fonts\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default CustomizationMobileDelivery;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst CustomizationMobileCustomer = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Customization</h1>\n      <h4 className=\"introduction-contentTitle\">Translate mobile app</h4>\n      • Translation admin panel is very easy. To translate admin panel, go to\n      admin panel and open <br />\n      <span className=\"introduction-step-2\">\n        Settings {\">\"} Translations menu\n      </span>\n      and Translate all words into your language\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/translation2.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n        <img\n          src=\"./assets/img/doc/translation1.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Color</h4>• Open{\" \"}\n      <span className=\"introduction-step\">\n        /lib/presentation/theme/app_style.dart\n      </span>\n      file and change colors as you want. <br />\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/color3.jpg\"\n          alt=\"img\"\n          className=\"img-responsive-full\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Font</h4>• in our\n      apps, google font package is installed. You may change app fonts easily by\n      selecting new fonts\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default CustomizationMobileCustomer;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst CustomizationMobileCustomer = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Customization</h1>\n      <h4 className=\"introduction-contentTitle\">Translate mobile app</h4>\n      • Translation admin panel is very easy. To translate admin panel, go to\n      admin panel and open <br />\n      <span className=\"introduction-step-2\">\n        Settings {\">\"} Translations menu\n      </span>\n      and Translate all words into your language\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/translation2.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n        <img\n          src=\"./assets/img/doc/translation1.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Color</h4>• Open{\" \"}\n      <span className=\"introduction-step\">\n        /lib/presentation/styles/style.dart\n      </span>\n      file and change colors as you want. <br />\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/demand24/presentation-style-dart.png\"\n          alt=\"img\"\n          className=\"img-responsive-full\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Font</h4>• in our\n      apps, google font package is installed. You may change app fonts easily by\n      selecting new fonts\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default CustomizationMobileCustomer;\n", "import React, { useState } from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst AppBuildReleaseCustomer = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">App build & release</h1>\n      <h3 className=\"introduction-title\">Build for Android</h3>\n      For debug build you can run command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">flutter build apk</span>\n          <span\n            className={text === 1 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"flutter build apk\", 1)}\n          >\n            {text === 1 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      You will get a larger merged apk with this. But you can split them with\n      this command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">\n            flutter build apk --target-platform\n            android-arm,android-arm64,android-x64 --split-per-abi\n          </span>\n          <span\n            className={text === 2 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                \"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi\",\n                2\n              )\n            }\n          >\n            {text === 2 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      Build file location:\n      <span className=\"introduction-step\"> /build/app/outputs/apk/</span>\n      For deploying it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/android\n      </strong>\n      <h4 className=\"introduction-contentTitle\">Build for iOS</h4>\n      There are no general way to generate app for iOS. Apple doesn’t allow to\n      install app like this debug way. If you want to install it on your iOS\n      device then you have to deploy it on TestFlight or AppStore. For deploying\n      it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/ios\n      </strong>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default AppBuildReleaseCustomer;\n", "import React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst AppBuildReleasePos = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Pos build & release</h1>\n      <h3 className=\"introduction-title\">Build for Android</h3>\n      For debug build you can run command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">flutter build apk</span>\n          <span\n            className={text === 1 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"flutter build apk\", 1)}\n          >\n            {text === 1 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      You will get a larger merged apk with this. But you can split them with\n      this command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">\n            flutter build apk --target-platform\n            android-arm,android-arm64,android-x64 --split-per-abi\n          </span>\n          <span\n            className={text === 2 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                \"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi\",\n                2\n              )\n            }\n          >\n            {text === 2 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      Build file location:\n      <span className=\"introduction-step\"> /build/app/outputs/apk/</span>\n      For deploying it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/android\n      </strong>\n      <h4 className=\"introduction-contentTitle\">Build for iOS</h4>\n      There are no general way to generate app for iOS. Apple doesn’t allow to\n      install app like this debug way. If you want to install it on your iOS\n      device then you have to deploy it on TestFlight or AppStore. For deploying\n      it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/ios\n      </strong>\n      <h4 className=\"introduction-contentTitle\">Build for Windows</h4>\n      Firebase must be committed when building to Windows. Otherwise you will\n      have problem with firebase on Windows.\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default AppBuildReleasePos;\n", "import React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst AppBuildReleaseVendor = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">App build & release</h1>\n      <h3 className=\"introduction-title\">Build for Android</h3>\n      For debug build you can run command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">flutter build apk</span>\n          <span\n            className={text === 1 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"flutter build apk\", 1)}\n          >\n            {text === 1 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      You will get a larger merged apk with this. But you can split them with\n      this command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">\n            flutter build apk --target-platform\n            android-arm,android-arm64,android-x64 --split-per-abi\n          </span>\n          <span\n            className={text === 2 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                \"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi\",\n                2\n              )\n            }\n          >\n            {text === 2 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      Build file location:\n      <span className=\"introduction-step\"> /build/app/outputs/apk/</span>\n      For deploying it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/android\n      </strong>\n      <h4 className=\"introduction-contentTitle\">Build for iOS</h4>\n      There are no general way to generate app for iOS. Apple doesn’t allow to\n      install app like this debug way. If you want to install it on your iOS\n      device then you have to deploy it on TestFlight or AppStore. For deploying\n      it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/ios\n      </strong>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default AppBuildReleaseVendor;\n", "import React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst AppBuildReleaseDelivery = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">App build & release</h1>\n      <h3 className=\"introduction-title\">Build for Android</h3>\n      For debug build you can run command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">flutter build apk</span>\n          <span\n            className={text === 1 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"flutter build apk\", 1)}\n          >\n            {text === 1 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      You will get a larger merged apk with this. But you can split them with\n      this command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">\n            flutter build apk --target-platform\n            android-arm,android-arm64,android-x64 --split-per-abi\n          </span>\n          <span\n            className={text === 2 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                \"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi\",\n                2\n              )\n            }\n          >\n            {text === 2 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      Build file location:\n      <span className=\"introduction-step\"> /build/app/outputs/apk/</span>\n      For deploying it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/android\n      </strong>\n      <h4 className=\"introduction-contentTitle\">Build for iOS</h4>\n      There are no general way to generate app for iOS. Apple doesn’t allow to\n      install app like this debug way. If you want to install it on your iOS\n      device then you have to deploy it on TestFlight or AppStore. For deploying\n      it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/ios\n      </strong>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default AppBuildReleaseDelivery;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MandatorySetupWeb = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Build code and setup on server</h1>\n      <h3 className=\"introduction-contentTitle\"> Frontend website </h3>\n      • Open /.env and change every single credential with your own\n      <br />\n      • If there is no .env file, please create the one in root folder and fill\n      as the same as in the example\n      <br />\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/foodyman-front.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/foodyman-front.jpg\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n            <a\n              href=\"./assets/img/firebase-messaging.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/firebase-messaging.jpg\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      For building web data for deployment, you have to run commands:\n      <br />\n      • Install required package <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn</span>\n          <span\n            className={text === 3 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn\", 3)}\n          >\n            {text === 3 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Build frontend using following command <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn build</span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn build\", 5)}\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Run project with pm2 <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">pm2 start \"yarn start\"</span>\n          <span\n            className={text === 6 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn start\", 6)}\n          >\n            {text === 6 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      <div className=\"iframe-wrapper mb-2\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/Fd1TXISmw-o\"\n          title=\"YouTube video player\"\n          frameBorder=\"0\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        />\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MandatorySetupWeb;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\n\nconst BuildCodeAndSetupOnServer = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Build code and setup on server</h1>\n      <h3 className=\"introduction-contentTitle\"> Frontend website </h3>\n      • Open /.env and change every single credential with your own\n      <br />\n      • If there is no .env file, please create the one in root folder and fill\n      as the same as in the example\n      <br />\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/foodyman-front.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/foodyman-front.jpg\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n            <a\n                href=\"./assets/img/firebase-messaging.jpg\"\n                data-fancybox\n                rel=\"nofollow\"\n            >\n              <img\n                  src=\"./assets/img/firebase-messaging.jpg\"\n                  alt=\"images\"\n                  loading=\"lazy\"\n                  className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      For building web data for deployment, you have to run commands:\n      <br />\n      • Install required package <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn</span>\n          <span\n            className={text === 3 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn\", 3)}\n          >\n            {text === 3 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <p className=\"mt-2\">or</p>\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">npm install</span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm install\", 4)}\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Build frontend using following command <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn build</span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn build\", 5)}\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <p className=\"mt-2\">or</p>\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">npm run build</span>\n          <span\n            className={text === 6 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm run build\", 6)}\n          >\n            {text === 6 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Run project with pm2 <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">pm2 start \"npm run start\"</span>\n          <span\n            className={text === 6 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm run start\", 6)}\n          >\n            {text === 6 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        You have to configure your server for front website. Front website runs\n        in port 3000. open server configuration file and add{\" \"}\n        <div className=\"introduction-code\">\n          <div>\n            <span className=\"ps-0\">\n              ProxyPass / http://localhost:3000 <br /> ProxyPassReverse /\n              http://localhost:3000\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"ProxyPass / http://localhost:3000  ProxyPassReverse /http://localhost:3000\",\n                  7\n                )\n              }\n            >\n              {text === 7 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>{\" \"}\n        in your domain configuration section. Rewrite mode should be enabled in\n        your server. After adding, restart your server\n      </div>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/mandatory-setup-web\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Mandatory setup web </p>\n        </Link>\n        <Link\n          to=\"/foodyman-documentation/mandatory-setup-customer\"\n          className=\"btn  next\"\n        >\n          <p>Next</p>\n          <p className=\"link\"> Mandatory setup customer </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default BuildCodeAndSetupOnServer;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\n\nconst BuildCodeAndSetupOnServerBackend = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Build code and setup on server</h1>\n      <h3 className=\"introduction-contentTitle\"> Admin Panel front </h3>\n      • Open /next.config.js and change BASE_URL with your server url\n      <br />\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/web-1.jpg\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/sundaymartWebSettings.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      For building web data for deployment, you have to run commands:\n      <br />\n      • Install required package <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn</span>\n          <span\n            className={text === 3 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn\", 3)}\n          >\n            {text === 3 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <p className=\"mt-2\">or</p>\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">npm install</span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm install\", 4)}\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Build frontend using following command <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn build</span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn build\", 5)}\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <p className=\"mt-2\">or</p>\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">npm run build</span>\n          <span\n            className={text === 6 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm run build\", 6)}\n          >\n            {text === 6 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Run project with pm2 <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">pm2 start \"npm run start\"</span>\n          <span\n            className={text === 6 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm run start\", 6)}\n          >\n            {text === 6 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        You have to configure your server for front website. Front website runs\n        in port 3000. open server configuration file and add{\" \"}\n        <div className=\"introduction-code\">\n          <div>\n            <span className=\"ps-0\">\n              ProxyPass / http://localhost:3000 <br /> ProxyPassReverse /\n              http://localhost:3000\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"ProxyPass / http://localhost:3000  ProxyPassReverse /http://localhost:3000\",\n                  7\n                )\n              }\n            >\n              {text === 7 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>{\" \"}\n        in your domain configuration section. Rewrite mode should be enabled in\n        your server. After adding, restart your server\n      </div>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/build-code-and-setup-on-server\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Build code and setup on server </p>\n        </Link>\n        <Link\n          to=\"/foodyman-documentation/mandatory-setup-customer\"\n          className=\"btn  next\"\n        >\n          <p>Next</p>\n          <p className=\"link\"> Mandatory setup customer </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default BuildCodeAndSetupOnServerBackend;\n", "import React from \"react\";\nimport { G<PERSON><PERSON><PERSON>fire } from \"react-icons/gi\";\nimport { <PERSON> } from \"react-router-dom\";\n\nconst UpdateAdminPanel = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Admin Panel</h1>\n      <h3 className=\"introduction-title\">Update</h3>\n      For update the admin panel just upload admin_front and admin_backend\n      folders in your project root folder and extract it. Build project.\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        Keep config/init.php and config/credential.php while updating\n      </div>\n      <div className=\"mt-4\" />\n      <div className=\"iframe-wrapper\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/I6bhGMcVR5E\"\n          title=\"How to update project\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        You have to be a developer in this case. If something goes wrong, we\n        won't be responsible for that.\n      </div>\n      <div className=\"center-page-container\">\n        <Link to=\"/foodyman-documentation/firebase\" className=\"btn  previous\">\n          <p>Previous</p>\n          <p className=\"link\"> Firebase </p>\n        </Link>\n        <Link to=\"/foodyman-documentation/update-app-web\" className=\"btn  next\">\n          <p>Next</p>\n          <p className=\"link\"> App & Web </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default UpdateAdminPanel;\n", "import React, { useState } from \"react\";\nimport { GiCamp<PERSON> } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\n\nconst UpdateAppWeb = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">App & Web</h1>\n      <h3 className=\"introduction-title\">Update mobile App</h3>\n      Download source code and replace it with old one and build.\n      <h3 className=\"introduction-title\">Update mobile Web</h3>\n      Upload front files to server and do following steps:\n      <br />\n      • Install required package <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn</span>\n          <span\n            className={text === 3 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn\", 3)}\n          >\n            {text === 3 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <p className=\"mt-2\">or</p>\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">npm install</span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm install\", 4)}\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Build frontend using following command <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">yarn next build</span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn build\", 5)}\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <p className=\"mt-2\">or</p>\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">npm next build</span>\n          <span\n            className={text === 6 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"npm run build\", 6)}\n          >\n            {text === 6 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <br />\n      • Restart project with pm2 <br />\n      <div className=\"introduction-code\">\n        <div>\n          <span className=\"ps-0\">pm2 stop 0 & pm2 start 0</span>\n          <span\n            className={text === 7 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"yarn build\", 7)}\n          >\n            {text === 7 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"mt-4\" />\n      <div className=\"iframe-wrapper\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/I6bhGMcVR5E\"\n          title=\"How to update project\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        You have to be a developer in this case. If something goes wrong, we\n        won't be responsible for that.\n      </div>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-documentation/update-admin-panel\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Admin Panel </p>\n        </Link>\n        <Link to=\"/foodyman-documentation/support-plan\" className=\"btn  next\">\n          <p>Next</p>\n          <p className=\"link\"> Support plan </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default UpdateAppWeb;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst Pricing = ({ data = [] }) => {\n  return (\n    <>\n      {data.map((val, i) => (\n        <div className=\"col-xl-4 col-lg-6 mb-5\" key={i}>\n          <div\n            className=\"ptf-animated-block h-100\"\n            data-aos=\"fade\"\n            data-aos-delay={i * 100}\n          >\n            <div className=\"ptf-pricing-table h-100\">\n              {i === 2 ? <div className=\"badge\">Popular</div> : \"\"}\n              <div className=\"ptf-pricing-table__header\">\n                <h4 className=\"ptf-pricing-table__title\">{val.title}</h4>\n              </div>\n              <div className=\"ptf-pricing-table__price\">\n                <span className=\"currency\">$</span>\n                <span className=\"price\">{val.price}</span>\n                <span className=\"period\">/ Month</span>\n              </div>\n              <div className=\"ptf-pricing-table__description\">Month</div>\n              <div\n                className=\"ptf-pricing-table__content\"\n                dangerouslySetInnerHTML={{ __html: val.description }}\n              />\n              <div className=\"ptf-pricing-table__action\">\n                <Link\n                  className=\"ptf-btn ptf-btn--primary ptf-btn--block\"\n                  to=\"/login\"\n                  onClick={(event) => event.preventDefault()}\n                >\n                  Purchase\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      ))}\n    </>\n  );\n};\n\nexport default Pricing;\n", "import React from \"react\";\nimport Pricing from \"../Pricing/Pricing\";\n\nconst PricePlan = ({ data }) => {\n  return (\n    <section>\n      <div\n        className=\"ptf-spacer\"\n        style={{ \"--ptf-xxl\": \"8.75rem\", \"--ptf-md\": \"4.375rem\" }}\n      />\n      <div className=\"container\">\n        <div className=\"ptf-animated-block\" data-aos=\"fade\" data-aos-delay=\"0\">\n          <h2 className=\"h2 large-heading text-white\">Pricing & Plan</h2>\n        </div>\n        <div\n          className=\"ptf-spacer\"\n          style={{ \"--ptf-xxl\": \"3.75rem\", \"--ptf-md\": \"1.875rem\" }}\n        />\n      </div>\n      <div className=\"container\">\n        <div className=\"row\" style={{ \"--bs-gutter-x\": \"2rem\" }}>\n          <Pricing data={data} />\n        </div>\n      </div>\n      <div\n        className=\"ptf-spacer\"\n        style={{ \"--ptf-xxl\": \"8.75rem\", \"--ptf-md\": \"4.375rem\" }}\n      />\n    </section>\n  );\n};\n\nexport default PricePlan;\n", "import React from \"react\";\nimport PricePlan from \"../../components/PricePlan\";\n\nconst pricing = [\n  {\n    title: \"Plan 0\",\n    price: \"150\",\n    description: \"Setup Admin and Store panel on your hosting.\",\n  },\n  {\n    title: \"Plan 1\",\n    price: \"400\",\n    description:\n      \"Setup Admin and Store panel on your hosting. Link user, POS system and deliveryman apps with your admin panel. Customize app name, logo, a color, package name, splash screen, etc. Setup OTP configuration.\",\n  },\n  {\n    title: \"Plan 2\",\n    price: \"600\",\n    description:\n      \"Plan 1 + Configure push notification. Setup payment gateways. Third party API setup (google map API setup included).\",\n  },\n  {\n    title: \"Plan 3\",\n    price: \"800\",\n    description:\n      \"Plan 2 + Translate all applications to your local language. Upload user, store and delivery man apps to Google Play Store. [Optional] Upload apps to Apple Store (+$200 per app for Apple store release).\",\n  },\n  {\n    title: \"Plan 4\",\n    price: \"1100\",\n    description:\n      \"Plan 3 + Upload All apps to the Apple Store. One revision before project handover.\",\n  },\n];\n\nconst SupportPlan = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Support</h1>\n      <h3 className=\"introduction-contentTitle\">\n        Licenses, activation and customization\n      </h3>\n      <div>\n        After you purchased our product and installed it, please reach out to us\n        via Telegram (**************) or WhatsApp (**************) to schedule\n        activation. Please note that once you have downloaded the item, we will\n        not issue a refund.\n      </div>\n      <h3 className=\"introduction-contentTitle\">\n        Holders of a regular license:\n      </h3>\n      <div>\n        We are happy to respond if you have one or two specific questions but\n        can’t provide extensive support and guidance for free. No support is\n        provided to set up your server or flutter environment in your local\n        device (iOS or Android) installation. To receive installation support,\n        you need to purchase one of our support plans.\n        <br />\n        Please check our documentation before you purchase the item if you want\n        to install it on your own. We are continually striving to improve\n        documentation, so if you are unhappy with the current state of\n        documentation, please check in later, as an updated version might suit\n        your installation needs better. Please note that once you have\n        downloaded the item, we will not issue a refund. Please also note that\n        some of the features such as subscription are only available under the\n        extended license.\n        <br />\n        Holders of an extended license\n        <br />\n        Please reach out to us if you have questions.\n      </div>\n      <h3 className=\"introduction-contentTitle\">Customization</h3>\n      <div>\n        Our team is available to help with customization. Please reach out to us\n        with detailed information so that our team can give you a quote.\n        <br />\n        If our team has not done the customization, we cannot help you with any\n        potential issues that may arise from your customization.\n      </div>\n\n      <PricePlan data={pricing} />\n    </div>\n  );\n};\n\nexport default SupportPlan;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst FirebaseSetup = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Firebase</h1>\n      <p className=\"inner-text\">\n        We use Firebase Authentication, Cloud Messaging and Firestore in\n        Foodyman. So, you have to setup Firebase in order to use Foodyman\n        properly.\n      </p>\n      <div className=\"mt-4\" />\n      <div className=\"iframe-wrapper\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/OLwNp_e5bxM\"\n          title=\"Firebase configuration\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"mt-4\" />\n      <div className=\"iframe-wrapper\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/jCgZZiz1480\"\n          title=\"How to run connect firebase to project\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        ></iframe>\n      </div>\n\n      <div className=\"mt-4\" />\n      <div className=\"iframe-wrapper\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/5HzrGiY9cFo\"\n          title=\"Firebase auth configuration\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"mt-4\" />\n      <div className=\"iframe-wrapper\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/cchc8T1QnCE?si=RvYlapwJ2S-oMd0h\"\n          title=\"Cloud messaging\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <div className=\"\">\n        <p style={{ margin: \"10px\" }}>\n          Add downloaded file to backend_folder/storage/app and run the command\n          php artisan storage:link if you haven’t run it before\n        </p>\n        <img\n          src={`../assets/img/google-service-json.jpg`}\n          alt=\"icon\"\n          style={{ width: \"100%\" }}\n        />\n      </div>\n      <div className=\"mt-4\" />\n      <div className=\"iframe-wrapper\">\n        <iframe\n          width=\"560\"\n          height=\"315\"\n          src=\"https://www.youtube.com/embed/29ARDVIXvXk\"\n          title=\"Firestore\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default FirebaseSetup;\n", "import React from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst TroubleshootingBackend = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Troubleshooting backend</h1>\n\n      <div class=\"accordion\" id=\"accordionExample\">\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingOne\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseOne\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseOne\"\n            >\n              Troubleshooting 1\n            </button>\n          </h2>\n          <div\n            id=\"collapseOne\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingOne\"\n            data-bs-parent=\"#accordionExample\"\n          >\n            <div class=\"accordion-body\">\n              <p>\n                if you meet your serialized closure might have been modified or\n                it's unsafe to be unserialized error you should use this code\n              </p>\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/food-doc/trouble.jpg\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/food-doc/trouble.jpg\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n              <div className=\"introduction-code\">\n                <p className=\"mb-0\">php artisan storage:link</p>\n              </div>\n              <div className=\"introduction-code\">\n                <p>php artisan optimize:clear</p>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingTwo\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseTwo\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseTwo\"\n            >\n              Troubleshooting 2\n            </button>\n          </h2>\n          <div\n            id=\"collapseTwo\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingTwo\"\n            data-bs-parent=\"#accordionExample1\"\n          >\n            <div class=\"accordion-body\">\n              <p>\n                If you meet this error, check db credentials entered correctly\n                in .env file. If everything is correct, run\n              </p>\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/food-doc/laravel-migrate.png\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/food-doc/laravel-migrate.png\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n              <div className=\"introduction-code\">\n                <p className=\"mb-0\">php artisan optimize:clear</p>\n              </div>\n              <div className=\"introduction-code\">\n                <p className=\"mb-0\">php artisan migrate</p>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingThree\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseThree\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseThree\"\n            >\n              Troubleshooting 3\n            </button>\n          </h2>\n          <div\n            id=\"collapseThree\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingThree\"\n            data-bs-parent=\"#accordionExample3\"\n          >\n            <div class=\"accordion-body\">\n              <p>\n                If you meet this error while installing, check following\n                extentions installed and enabled in your system:\n                <ul>\n                  <li>openssl</li>\n                  <li>fileinfo</li>\n                  <li>gd</li>\n                  <li>curl</li>\n                  <li>sodium</li>\n                  <li>zip</li>\n                </ul>\n              </p>\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/food-doc/trsh-3.png\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/food-doc/trsh-3.png\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingFive\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseFive\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseFive\"\n            >\n              Troubleshooting 4\n            </button>\n          </h2>\n          <div\n            id=\"collapseFive\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingFive\"\n            data-bs-parent=\"#accordionExample4\"\n          >\n            <div class=\"accordion-body\">\n              <h6 className=\"fw-normal\">\n                If you meet this error, please check /config/credentials.php\n                file. Are your lisence keys in this file ? If not, please, add\n                your lisence keys to this file. Then run “php artisan\n                optimize:clear”\n              </h6>{\" \"}\n              <br /> <br />\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/project/troubleshooting6.jpeg\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/project/troubleshooting6.jpeg\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingTwo\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseTwo\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseTwo\"\n            >\n              Troubleshooting 5\n            </button>\n          </h2>\n          <div\n            id=\"collapseTwo\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingTwo\"\n            data-bs-parent=\"#accordionExample1\"\n          >\n            <div class=\"accordion-body\">\n              <p>\n                \"message\": \"SQLSTATE[42000]: Syntax error or access violation:\n                1055 'elbarrio_foody_test_bd.u.firstname' isn't in GROUP BY\n                (SQL: select u.id as id, u.firstname as firstname, u.img as img,\n                u.lastname as lastname, u.phone as phone, sum(distinct\n                orders.total_price) as total_price, count(distinct orders.id) as\n                count from orders cross join users as u on orders.user_id = u.id\n                where date(`orders`.`created_at`) {\">\"} 2023-02-24 and\n                orders.status = delivered group by id having total_price {\">\"} 0\n                or count {\">\"} 0 order by total_price desc limit 6 offset 0) in\n                /home2/elbarrio/api.lavilla.pe/vendor/laravel/framework/src/Illuminate/Database/Connection.php:712\"{\" \"}\n                {\"}\"}\n              </p>\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/food-doc/troubleshooting-2.jpg\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/food-doc/troubleshooting-2.jpg\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n              <div className=\"introduction-code\">\n                <div>\n                  <span>Open configs/database.php and make \"strict\" false</span>\n                </div>\n                <div>\n                  <span>php artisan optimize:clear</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default TroubleshootingBackend;\n", "import React, { useState } from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst TroubleshootingAdmin = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Troubleshooting admin</h1>\n\n      <div class=\"accordion\" id=\"accordionExample\">\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingOne\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseOne\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseOne\"\n            >\n              Troubleshooting 1\n            </button>\n          </h2>\n          <div\n            id=\"collapseOne\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingOne\"\n            data-bs-parent=\"#accordionExample\"\n          >\n            <div class=\"accordion-body has-white-color\">\n              <p>\n                If you see above issue in your terminal, open package.json and\n                remove GENERATE_SOURCEMAP=false. Then try to rebuild\n              </p>\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/food-doc/troubleshooting-1.jpg\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/food-doc/troubleshooting-1.jpg\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n              <div className=\"introduction-code\">\n                <div>\n                  <span>GENERATE_SOURCEMAP=false</span>\n                  <span\n                    className={text === 1 ? \"bg-success copy\" : \"copy\"}\n                    onClick={() =>\n                      copyToClipBoard(\"GENERATE_SOURCEMAP=false\", 1)\n                    }\n                  >\n                    {text === 1 ? \"copied!\" : \"copy\"}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingThree\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseThree\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseThree\"\n            >\n              Troubleshooting 2\n            </button>\n          </h2>\n          <div\n            id=\"collapseThree\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingThree\"\n            data-bs-parent=\"#accordionExample3\"\n          >\n            <div class=\"accordion-body\">\n              If your website's design has style issur, do following steps\n              <p>\n                1. Before build, open package.json. <br />\n                2. Replace \"antd\": \"^4.20.6\", with \"antd\": \"4.20.6\". <br />\n                3. Then build and upload.\n              </p>\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/food-doc/antd.jpg\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/food-doc/antd.jpg\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingFour\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseFour\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseFour\"\n            >\n              Troubleshooting 3\n            </button>\n          </h2>\n          <div\n            id=\"collapseFour\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingFour\"\n            data-bs-parent=\"#accordionExample4\"\n          >\n            <div class=\"accordion-body\">\n              If you meet this error, build app locally in your machine and\n              upload to server. you meet this error, when you server is not\n              strong to build the project. <br /> <br />\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    FATAL ERROR: Reached heap limit Allocation failed -\n                    JavaScript heap out of memory 1: 00007FF61345168F\n                    v8:internal CodeObjectRegistry::~CodeObjectRegistry+122159\n                    2: 00007FF6133DB456 DSA_meth_get_flags+64118 3:\n                    00007FF6133DC4D2 DSA_meth_ get_flags+68338 4:\n                    00007FF613D13CB4 v8::Isolate::ReportExternalAllocation\n                    LimitReached+ 116 5: 00007FF613CFE27D\n                    v8::SharedArrayBuffer::Externalize+781 6: 00007FF613BA183C\n                    v8:internal::Heap::EphemeronKeyWriteBarrierFromCode+1468 7:\n                    00007FF613B9E954 v8::internal:: Heap::CollectGarbage+4244 8:\n                    00007FF613B9C2D0 v8:internal::\n                    Heap::AllocateExternalBackingStore+2000 9: 00007FF613BCOE56\n                    v8:internal:: Factory::NewFillerObject+214 10:\n                    00007FF6138F3565 v8.:internal::DateCache::Weekday+1797 11:\n                    00007FF613DA1991\n                    v8::internal::SetupisolateDelegate::SetupHeap+494417 12:\n                    00007FF613D3689E\n                    v8::internal::SetupisolateDelegate::SetupHeap+55902 13:\n                    00000298B2BE89B2\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingFive\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseFive\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseFive\"\n            >\n              Troubleshooting 4\n            </button>\n          </h2>\n          <div\n            id=\"collapseFive\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingFive\"\n            data-bs-parent=\"#accordionExample4\"\n          >\n            <div class=\"accordion-body\">\n              <h6 className=\"fw-normal\">\n                If you get this error, delete the env file or set\n                REACT_APP_IS_DEMO=false\n              </h6>{\" \"}\n              <br /> <br />\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/project/troubleshooting5-error.png\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/project/troubleshooting5-error.png\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                    <a\n                      href=\"./assets/img/project/troubleshooting5.png\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/project/troubleshooting5.png\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingFive\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseFive\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseFive\"\n            >\n              Troubleshooting 5\n            </button>\n          </h2>\n          <div\n            id=\"collapseFive\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingFive\"\n            data-bs-parent=\"#accordionExample4\"\n          >\n            <div class=\"accordion-body\">\n              <h6 className=\"fw-normal\">\n                If you meet this error, please, get recaptcha key using this\n                tutorial.{\" \"}\n                <strong>\n                  <a\n                    href=\"https://contactform7.com/recaptcha-v2/\"\n                    target=\"_blank\"\n                  >\n                    link\n                  </a>\n                </strong>\n              </h6>{\" \"}\n              <br /> <br />\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/doc/recaptcha.jpg\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/doc/recaptcha.jpg\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"accordion-item\">\n          <h2 class=\"accordion-header\" id=\"headingFive\">\n            <button\n              class=\"accordion-button\"\n              type=\"button\"\n              data-bs-toggle=\"collapse\"\n              data-bs-target=\"#collapseFive\"\n              aria-expanded=\"true\"\n              aria-controls=\"collapseFive\"\n            >\n              Troubleshooting 6\n            </button>\n          </h2>\n          <div\n            id=\"collapseFive\"\n            class=\"accordion-collapse collapse show\"\n            aria-labelledby=\"headingFive\"\n            data-bs-parent=\"#accordionExample4\"\n          >\n            <div class=\"accordion-body\">\n              <h6 className=\"fw-normal\">\n                We recommend to use yarn to manage packages. If you meet this\n                error, use yarn or run \"npm run —legacy-per-deps\" command.{\" \"}\n              </h6>{\" \"}\n              <br /> <br />\n              <div className=\"introduction-img-container mb-5\">\n                <SimpleReactLightbox>\n                  <SRLWrapper>\n                    <a\n                      href=\"./assets/img/troubleshooting-version.jpg\"\n                      data-fancybox\n                      rel=\"nofollow\"\n                    >\n                      <img\n                        src=\"./assets/img/troubleshooting-version.jpg\"\n                        alt=\"admin install\"\n                        loading=\"lazy\"\n                        className=\"img-responsive-full\"\n                      />\n                    </a>\n                  </SRLWrapper>\n                </SimpleReactLightbox>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default TroubleshootingAdmin;\n", "import React from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { GiCamp<PERSON> } from \"react-icons/gi\";\nimport { <PERSON> } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst UpdateFooyman = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">How do I update existing code with new release?</h1>\n      <h4 className=\"introduction-contentTitle\">Special Notes</h4>\n      <p>\n        As per Envato policy installation, setup and configurations or\n        modification are not included in free support. Free support is only for\n        any bug/ error in original code. we do not provide installation and\n        customization support in FREE SUPPORT. <br /> Still, We are providing\n        steps for How to update future release code to existing source code for\n        your knowledge purpose.\n      </p>\n      <h4 className=\"introduction-contentTitle\">\n        How to update future release code to existing source code\n      </h4>\n      <p>\n        If you want which file changes are on the latest updated version then\n        you have to manage the git repository by yourself.\n      </p>\n      <p>Here we have provided steps on how to update existing source code.</p>\n      <h1 className=\"title\">\n        For First time: Initial project downloaded from codecanyon server. Step\n      </h1>\n      <h4 className=\"introduction-contentTitle\">\n        Step 1: Create or login with github\n      </h4>\n      <p>\n        Login or register your account with github:\n        <a href=\"https://github.com/login\">https://github.com/login</a>\n      </p>\n      <h4 className=\"introduction-contentTitle\">\n        Step 2: Create a new project in Github\n      </h4>\n      <p>\n        In your dashboard, click the green New button. This opens the New\n        project page.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/c-repo.jpg\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/c-repo.jpg\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/c_repo-2.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/c_repo-2.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-contentTitle\">\n        Step 3: Clone your project to your local system\n      </h4>\n      <p>\n        Once project is created on your github server. You have to clone the\n        project to your local system. You can clone it with the command line.\n      </p>\n      <p>\n        <strong>For ex:</strong> git clone\n        https://github.com/YOURUSERNAME/your-repository-name.git\n      </p>\n      <p>Copy your project url and clone into your exiting system</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/c_repo-3.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/c_repo-3.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/clone-repo.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/clone-repo.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p>\n        Once successfully clone then system will create a folder on your system\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/local-repo.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/local-repo.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-img-container\">\n        Step 4: Download project from codecanyon server <br />\n        Step 5: Copy/paste your initial downloaded project to clone directories.\n      </h4>\n      <p>\n        Once successfully downloaded project from codecanyon, copy/paste your\n        downloaded project into clone directories\n      </p>\n      <p>\n        <strong>Note: </strong>\n      </p>\n      <p>Only orignal source code is put here.</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/local-file.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/local-file.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-img-container\">\n        Step 6: Commit and push to github server\n      </h4>\n      <p>\n        Onces copy/paste your changes to clone directres, you have to push all\n        files to your github server. For that use the following commands.\n      </p>\n      <p>After That follow below steps</p>\n      <ul>\n        <li>– Goto inside your clone directory project</li>\n      </ul>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/go-repository.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/go-repository.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <ul>\n        <li>– Add your all files with “git add .” command</li>\n        <li>– Now commit your changes with below command</li>\n        <li>git commit -m ‘initial commit’</li>\n      </ul>\n      <p>\n        <storng>Note:</storng>\n      </p>\n      <p>Write your latest version message instead of “initial commit”.</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/git-add.png\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/git-add.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/git-commit.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/git-commit.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <ul>\n        <li>– Push your changes to server with below command</li>\n        <li>“git push” And provide you github credential details</li>\n      </ul>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/git-push.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/git-push.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <ul>\n        <li>– Check your all changes to github server</li>\n      </ul>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/repo.png\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/repo.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h1 className=\"title mt-4\">\n        For Update existing code (If Already have old version of project)\n      </h1>\n      <p>\n        <strong>NOTE: </strong>If you remove the project from the local system\n        then clone the project again from your github server. Follow the same\n        above Step 3: Clone your project to your local system\n      </p>\n      <h4 className=\"introduction-contentTitle\">\n        Steps 1: Download the latest version from codecanyon server.\n      </h4>\n      <p>\n        Once you will received mail for updates. Just download latest code from\n        codecanyon server.\n      </p>\n      <h4 className=\"introduction-contentTitle\">\n        Steps 2: Copy/paste your initial downloaded project to clone\n        directories.\n      </h4>\n      <p>\n        Once successfully downloaded project from codecanyon, copy/paste your\n        downloaded project into clone directories\n      </p>\n      <p>\n        <strong>Note:</strong>\n      </p>\n      <p>Only orignal source code is put here.</p>\n      <h4 className=\"introduction-contentTitle\">\n        Steps 3: Commit and push to github server\n      </h4>\n      <p>Follow same Step 6: Commit and push to github server</p>\n      <h4 className=\"introduction-contentTitle\">\n        Steps 4: Check updated files\n      </h4>\n      <p>\n        After committing your latest changes. Goto github project dashboard and\n        click on commit link.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/commits.png\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/commits.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p>Click on “XX changed file” to see which file has been changed.</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/changed.png\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/changed.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p>Connect to server and open project directory</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/root.png\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/root.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p>Run “git init” command to initialize git</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a href=\"./assets/img/doc/init.png\" data-fancybox rel=\"nofollow\">\n              <img\n                src=\"./assets/img/doc/init.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p>\n        Run following commant to connect your server with github repository.\n        Don’t forget to put your oauth key and github repository url.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/git-remote.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/git-remote.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p>Pull changes using following command</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/git-pull.png\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/git-pull.png\"\n                alt=\"images\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        if you have any confilicts while pulling, use google to find a solution\n      </div>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Do above steps to update admin website, customer website and backend.\n      </div>\n      <NavigationBtns />\n\n      {/* <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/translation2.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n        <img\n          src=\"./assets/img/doc/translation1.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Color</h4>• Open{\" \"}\n      <span className=\"introduction-step\">\n        /lib/presentation/styles/style.dart\n      </span>\n      file and change colors as you want. <br />\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/color.jpg\"\n          alt=\"img\"\n          className=\"img-responsive-full\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Font</h4>• in our\n      apps, google font package is installed. You may change app fonts easily by\n      selecting new fonts */}\n    </div>\n  );\n};\n\nexport default UpdateFooyman;\n", "import React, { useState } from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { GiCampfire } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst MandatorySetupKiosk = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> KIOSK setup</h1>\n      <h4 className=\"introduction-contentTitle\">\n        {\" \"}\n        Run an existing flutter project on IDE{\" \"}\n      </h4>\n      <h4 className=\"introduction-contentTitle\"> Change App Logo </h4>\n      You can generate app icon using this website\n      https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html <br />\n      • Then go to{\" \"}\n      <span className=\"introduction-step\"> /android/app/src/main/res</span>\n      and replace all mipmap folder with your /android folder <br />• Again go\n      to <span className=\"introduction-step\">/ios/Runner</span> and replace\n      Assets.xcassets with your generated Assets.xcassets folder <br />\n      <h4 className=\"introduction-contentTitle\"> Change App Name </h4>\n      <div className=\"mt-4 mb-3\">\n        1.Change the value of label from\n        <span className=\"introduction-step\">\n          /android/app/src/main/AndroidManifest.xml\n        </span>\n        <div className=\"introduction-code\">\n          <p>/android/app/src/main/AndroidManifest.xml</p>\n          <hr />\n          <div>\n            <span>android:label=\"My App\"</span>\n            <span\n              className={text === 2 ? \"bg-success copy\" : \"copy\"}\n              onClick={() => copyToClipBoard('android:label=\"My App\"’', 2)}\n            >\n              {text === 2 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <div className=\"mt-4 mb-3\">\n        2.Change the value of CFBundleName from\n        <span className=\"introduction-step\"> /iOS/Runner/info.plist</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/info.plist</p>\n          <hr />\n          <div>\n            <span>\n              {`<key>CFBundleName</key>`} <br />\n              {`<string>My App</string>`}\n            </span>\n            <span\n              className={text === 3 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  \"<key>CFBundleName</key><string>My App</string>\",\n                  3\n                )\n              }\n            >\n              {text === 3 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div>\n      <h4 className=\"introduction-contentTitle\"> Change Base URL</h4>\n      Please do NOT put slash ( / ) at the end of your base url. Use your admin\n      url as base url. First you have to install your admin panel. For example:\n      If your admin url is{\" \"}\n      <span className=\"introduction-step\"> https://your_domain.com/admin</span>\n      then base url will be https://your_domain.com. Open{\" \"}\n      <span className=\"introduction-step\"> /lib/app_constants.dart</span>\n      and replace baseUrl variable value with your own URL.\n      <div className=\"introduction-code\">\n        <p>/lib/src/core/constants/secret_vars.dart</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            static const String baseUrl=\n            <span className=\"text-black\">'https://your_domain.com'</span>\n          </span>\n          <span\n            className={text === 4 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\"baseUrl=https://your_domain.com\", 4)\n            }\n          >\n            {text === 4 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/doc/kiosk-translation2.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/doc/kiosk-translation2.jpg\"\n                alt=\"image02\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Package</h4>\n      Firstly, find out the existing package name. You can find it out from top\n      of\n      <span className=\"introduction-step\">\n        /app/src/main/AndroidManifest.xml\n      </span>\n      file. Then right click on project folder from android studio and click on\n      <span className=\"introduction-step\">replace in path</span>\n      You will see a popup window with two input boxes. In first box you have to\n      put existing package name that you saw in{\" \"}\n      <span className=\"introduction-step\">AndroidManifest.xml</span>\n      file previously and then write down your preferred package name in second\n      box and then click on{\" \"}\n      <span className=\"introduction-step\">Replace All</span> button.\n      <h4 className=\"introduction-contentTitle\">Setup Firebase</h4>\n      IOS Firebase :{\" \"}\n      <a\n        className=\"strong\"\n        href=\"https://firebase.google.com/docs/ios/setup?authuser=0\"\n        target=\"_blank\"\n      >\n        ios/setup\n      </a>\n      <br />\n      Android Firebase :{\" \"}\n      <a\n        className=\"strong\"\n        href=\"https://firebase.google.com/docs/android/setup?authuser=0\"\n        target=\"_blank\"\n      >\n        android/setup\n      </a>\n      <br />\n      {/* <h4 className=\"introduction-contentTitle\"> Add Google Map API Key</h4>\n      • Please generate the google API key. You can visit this link -\n      https://developers.google.com/maps/documentation/embed/get-api-key <br />\n      • You need to enable mentioned APIs: Direction API, Distance Matrix API,\n      Geocoding API, Maps SDK for Android, Maps SDK for iOS, Place API. <br />\n      • Then you have to enable billing account. Visit this URL for activation:\n      https://support.google.com/googleapi/answer/6158867?hl=en <br />\n      • After generating API key, you have to paste it on 3 different places:\n      Android, iOS and web. <br />\n      For android, open\n      <span className=\"introduction-step\">\n        /android/app/src/main/AndroidManifest.xml\n      </span>{\" \"}\n      and place the value of{\" \"}\n      <span className=\"introduction-step\">com.google.android.geo.API_KEY </span>\n      <div className=\"introduction-code\">\n        <p> /android/app/src/main/AndroidManifest.xml</p>\n        <hr />\n        <div>\n          <span className=\"ps-0\">\n            {\n              '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>'\n            }\n          </span>\n          <span\n            className={text === 5 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                '<meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=“YOUR_MAP_API_KEY_HERE”/>',\n                5\n              )\n            }\n          >\n            {text === 5 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      <div className=\"mt-4\">\n        For iOS: open{\" \"}\n        <span className=\"introduction-step\">/iOS/Runner/AppDelegate.swift</span>\n        and place the value of{\" \"}\n        <span className=\"introduction-step\">GMSServices.provideAPIKey</span>\n        <div className=\"introduction-code\">\n          <p>/iOS/Runner/AppDelegate.swift</p>\n          <hr />\n          <div>\n            <span className=\"ps-0\">\n              GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")\n            </span>\n            <span\n              className={text === 6 ? \"bg-success copy\" : \"copy\"}\n              onClick={() =>\n                copyToClipBoard(\n                  'GMSServices.provideAPIKey(“YOUR_MAP_API_KEY_HERE\")',\n                  6\n                )\n              }\n            >\n              {text === 6 ? \"copied!\" : \"copy\"}\n            </span>\n          </div>\n        </div>\n      </div> */}\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default MandatorySetupKiosk;\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst CustomizationMobileKiosk = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Customization</h1>\n      <h4 className=\"introduction-contentTitle\">Translate mobile app</h4>\n      • Translation admin panel is very easy. To translate admin panel, go to\n      admin panel and open <br />\n      <span className=\"introduction-step-2\">\n        Settings {\">\"} Translations menu\n      </span>\n      and Translate all words into your language\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/translation2.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n        <img\n          src=\"./assets/img/doc/translation1.jpg\"\n          alt=\"img\"\n          className=\"img-responsive\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Color</h4>• Open{\" \"}\n      <span className=\"introduction-step\">\n        /lib/src/presentation/theme/app_style.dart\n      </span>\n      file and change colors as you want. <br />\n      <div className=\"introduction-img-container\">\n        <img\n          src=\"./assets/img/doc/kiosk-color3.jpg\"\n          alt=\"img\"\n          className=\"img-responsive-full\"\n        />\n      </div>\n      <h4 className=\"introduction-contentTitle\">Change App Font</h4>• in our\n      apps, google font package is installed. You may change app fonts easily by\n      selecting new fonts\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default CustomizationMobileKiosk;\n", "import React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport NavigationBtns from \"./Navigation-btns\";\n\nconst AppBuildReleaseSingleKiosk = () => {\n  const [text, setText] = useState(null);\n\n  const copyToClipBoard = async (copyMe, id) => {\n    try {\n      await navigator.clipboard.writeText(copyMe);\n      setText(id);\n    } catch (err) {\n      setText(\"Failed to copy!\");\n    }\n  };\n\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">Kiosk build & release</h1>\n      <h3 className=\"introduction-title\">Build for Android</h3>\n      For debug build you can run command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">flutter build apk</span>\n          <span\n            className={text === 1 ? \"bg-success copy\" : \"copy\"}\n            onClick={() => copyToClipBoard(\"flutter build apk\", 1)}\n          >\n            {text === 1 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      You will get a larger merged apk with this. But you can split them with\n      this command:\n      <div className=\"introduction-code mb-4\">\n        <div>\n          <span className=\"ps-0\">\n            flutter build apk --target-platform\n            android-arm,android-arm64,android-x64 --split-per-abi\n          </span>\n          <span\n            className={text === 2 ? \"bg-success copy\" : \"copy\"}\n            onClick={() =>\n              copyToClipBoard(\n                \"flutter build apk --target-platform android-arm,android-arm64,android-x64 --split-per-abi\",\n                2\n              )\n            }\n          >\n            {text === 2 ? \"copied!\" : \"copy\"}\n          </span>\n        </div>\n      </div>\n      Build file location:\n      <span className=\"introduction-step\"> /build/app/outputs/apk/</span>\n      For deploying it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/android\n      </strong>\n      <h4 className=\"introduction-contentTitle\">Build for iOS</h4>\n      There are no general way to generate app for iOS. Apple doesn’t allow to\n      install app like this debug way. If you want to install it on your iOS\n      device then you have to deploy it on TestFlight or AppStore. For deploying\n      it please follow this documentation:\n      <strong className=\"strong\">\n        https://docs.flutter.dev/deployment/ios\n      </strong>\n      <NavigationBtns />\n    </div>\n  );\n};\n\nexport default AppBuildReleaseSingleKiosk;\n", "import React from \"react\";\nimport { G<PERSON><PERSON>amp<PERSON> } from \"react-icons/gi\";\nimport { Link } from \"react-router-dom\";\n\nconst QRCode = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\">QR code requirements</h1>\n      • NodeJS v14+\n      <br />\n      • React js v18+ <br />\n      <div className=\"mt-4\" />\n      <div className=\"mt-4\" />\n      <h3 className=\"introduction-contentTitle\">Tools</h3>\n      • For Frontend development: Nodejs , Visual Studio Code or WebStorm <br />\n      <h3 className=\"introduction-contentTitle\">Knowledge</h3>• For Frontend\n      development: React Js <br />\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        We would like to notify that the Envato price doesn’t include any kind\n        of installation and app publishing support. We kindly ask you to follow\n        the documentation step by step for installation, setup and other\n        branding related changes. Please note that, we bear no responsibility\n        for your mistake. You are fully in charge for any kind of customizations\n        made by your own.\n      </div>\n      <div className=\"alert alert-danger mt-4\" role=\"alert\">\n        <div className=\"mb-2\">\n          <GiCampfire size={22} /> <strong>WARNING</strong>\n        </div>\n        Please do this very carefully. We bear no responsibility for your\n        mistake.\n      </div>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-single-documentation/pos-app-build-release\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\"> Pos build & release</p>\n        </Link>\n        <Link to=\"/foodyman-single-documentation\" className=\"btn  next\">\n          <p>Next</p>\n          <p className=\"link\"> Introduction </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default QRCode;\n", "import React from \"react\";\nimport { AiOutlineInfoCircle } from \"react-icons/ai\";\nimport { Link } from \"react-router-dom\";\nimport SimpleReactLightbox, { SRLWrapper } from \"simple-react-lightbox\";\n\nconst InstallOnServer = () => {\n  return (\n    <div\n      className=\"introduction \"\n      data-aos=\"fade-right\"\n      data-aos-delay=\"300\"\n      data-aos-duration=\"1000\"\n    >\n      <h1 className=\"title\"> Install QR code</h1>\n      <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Change title inside /public/index.html\n      </div>\n      {/* <div className=\"alert alert-primary mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>INFO</strong>\n        </div>\n        Thus to install admin_panel, we need subdomains like admin.xyz.com and\n        api.xyz.com.\n      </div> */}\n      {/* <div className=\"alert alert-warning mt-3\" role=\"alert\">\n        <div className=\"mb-2\">\n          <AiOutlineInfoCircle size={22} /> <strong>Warning</strong>\n        </div>\n        You have to set SSL certificate for your admin website. Some functions\n        doesn't work if your website doesn't have SSL certificate.\n      </div> */}\n      • Download the code from codecayon <br />• Extract the zip files\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p className=\"mb-0\">\n        • Create the <strong>dist</strong> File\n      </p>\n      <p>\n        In your application's root directory, run <strong>yarn </strong>\n        to install the updated dependencies. Once this has finished, the next\n        command you'll run is <strong>yarn build</strong> (\n        <strong>npm install</strong> and <strong>npm build</strong> work, too).\n      </p>\n      <p>\n        You'll notice this creates a new directory in your project called\n        <strong> dist</strong>. The dist folder is essentially a\n        super-compressed version of your program that has everything your\n        browser needs to identify and run your app.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/project/qrcode-build.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/project/qrcode-build.jpg\"\n                alt=\"admin config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p className=\"mb-0\">• Connect to cPanel</p>\n      <p>Your cPanel manager should look something like this:</p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/food-doc/c-pannel.webp\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/food-doc/c-pannel.webp\"\n                alt=\"admin config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <div className=\"ptf-spacer\" style={{ \"--ptf-xxl\": \"3.75rem\" }}></div>\n      <p className=\"mb-0\">\n        • Add the Build File Contents to <strong>public_html</strong>\n      </p>\n      <p>\n        Navigate to the dist file in your app's root directory. Open it up and\n        select all the contents <strong>inside the dist file.</strong> If you\n        upload the entire dist file itself, the process will not work.\n      </p>\n      <div className=\"introduction-img-container\">\n        <SimpleReactLightbox>\n          <SRLWrapper>\n            <a\n              href=\"./assets/img/project/view-qr-build.jpg\"\n              data-fancybox\n              rel=\"nofollow\"\n            >\n              <img\n                src=\"./assets/img/project/view-qr-build.jpg\"\n                alt=\"admin config\"\n                loading=\"lazy\"\n                className=\"img-responsive-full\"\n              />\n            </a>\n          </SRLWrapper>\n        </SimpleReactLightbox>\n      </div>\n      <p>\n        Once you've copied all the contents inside the dist file, upload them\n        into <strong>public_html.</strong>\n      </p>\n      <div className=\"center-page-container\">\n        <Link\n          to=\"/foodyman-single-documentation/qr-app\"\n          className=\"btn  previous\"\n        >\n          <p>Previous</p>\n          <p className=\"link\">Requirements </p>\n        </Link>\n        <Link\n          to=\"/foodyman-single-documentation/firebase\"\n          className=\"btn  next\"\n        >\n          <p>Next</p>\n          <p className=\"link\"> Firebase setup </p>\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default InstallOnServer;\n", "import React from \"react\";\nimport { Route } from \"react-router-dom\";\nimport FoodyDoc from \"../views/Foodyman-doc\";\nimport FoodyIntroduction from \"../views/Foodyman-doc/Introduction\";\nimport FoodyBasic from \"../views/Foodyman-doc/Basic\";\nimport FoodyServer from \"../views/Foodyman-doc/Server\";\nimport FoodyFront from \"../views/Foodyman-doc/front\";\nimport FoodyFrontQr from \"../views/Foodyman-doc/front-qr\";\nimport FoodyMandatorySetupWebQr from \"../views/Foodyman-doc/Mandatory-setup-web-qr\";\nimport FoodyAdmin from \"../views/Foodyman-doc/admin\";\nimport Recommendations from \"../views/Foodyman-doc/recommendations\";\nimport FoodyLocalFront from \"../views/Foodyman-doc/local-front\";\nimport FoodyMobileApp from \"../views/Foodyman-doc/Mobile-app\";\nimport FoodyFlutterSDK from \"../views/Foodyman-doc/Flutter-SDK\";\nimport FoodyLocalServer from \"../views/Foodyman-doc/Local-server\";\nimport FoodyInstallOnServer from \"../views/Foodyman-doc/Install-on-server\";\nimport FoodyMandatorySetup from \"../views/Foodyman-doc/Mandatory-setup\";\nimport FoodyCustomization from \"../views/Foodyman-doc/Customization\";\nimport FoodyMandatorySetupMobile from \"../views/Foodyman-doc/Mandatory-setup-mobile\";\nimport FoodyMandatorySetupBackend from \"../views/Foodyman-doc/Mandatory-setup-backend\";\nimport PaymentInstallation from \"../views/Uzmart-doc/Payment-Installation\";\nimport FoodyImageSettings from \"../views/Foodyman-doc/image-settings\";\nimport FoodyMandatorySetupVendor from \"../views/Foodyman-doc/Moderator-setup-vendor\";\nimport FoodyMandatorySetupCustomer from \"../views/Foodyman-doc/Moderator-setup-customer\";\nimport FoodyMandatorySetupPos from \"../views/Foodyman-doc/Moderator-setup-pos\";\nimport FoodyMandatorySetupDeliveryboy from \"../views/Foodyman-doc/Moderator-setup-deliveryboy\";\nimport FoodyCustomizationMobile from \"../views/Foodyman-doc/Customization-mobile\";\nimport CustomizationMobileDelivery from \"../views/Foodyman-doc/Customization-mobile-delivery\";\nimport CustomizationMobileCustomer from \"../views/Foodyman-doc/Customization-mobile-customer\";\nimport CustomizationMobilePos from \"../views/Foodyman-doc/Customization-mobile-pos\";\nimport AppBuildReleaseCustomer from \"../views/Foodyman-doc/App-build-release-customer\";\nimport AppBuildReleasePos from \"../views/Foodyman-doc/App-build-release-pos\";\nimport AppBuildReleaseVendor from \"../views/Foodyman-doc/App-build-release-vendor\";\nimport AppBuildReleaseDelivery from \"../views/Foodyman-doc/App-build-release-deliveryboy\";\nimport FoodyMandatorySetupWeb from \"../views/Foodyman-doc/Mandatory-setup-web\";\nimport FoodyBuildCodeAndSetupOnServer from \"../views/Foodyman-doc/Build-code-and-setup-on-server\";\nimport FoodyBuildCodeAndSetupOnServerBackend from \"../views/Foodyman-doc/Build-code-and-setup-on-server-backend\";\nimport FoodyUpdateAdminPanel from \"../views/Foodyman-doc/Update-admin-panel\";\nimport FoodyUpdateAppWeb from \"../views/Foodyman-doc/Update-app-web\";\nimport FoodySupportPlan from \"../views/Foodyman-doc/supportPlan\";\nimport FoodyFirebaseSetup from \"../views/Foodyman-doc/firebase-setup\";\nimport TroubleshootingBackend from \"../views/Foodyman-doc/Troubleshooting-backend\";\nimport TroubleshootingAdmin from \"../views/Foodyman-doc/Troubleshooting-admin\";\nimport UpdateFooyman from \"../views/Foodyman-doc/update\";\nimport MandatorySetupKiosk from \"../views/Foodyman-doc/Moderator-setup-kiosk\";\nimport CustomizationMobileKiosk from \"../views/Foodyman-doc/Customization-mobile-kiosk\";\nimport AppBuildReleaseSingleKiosk from \"../views/Foodyman-doc/App-build-release-kiosk\";\nimport FoodyQRApp from \"../views/Foodyman-doc/qr-code\";\nimport FoodyInstallQRcode from \"../views/Foodyman-doc/install-qrcode\";\n\nconst Foodyman = () => {\n  return (\n    <Route path=\"/\" element={<FoodyDoc />}>\n      <Route path=\"/\" index element={<FoodyIntroduction />} />\n      <Route\n        path=\"/foodyman-documentation/introduction\"\n        element={<FoodyIntroduction />}\n      />\n      <Route path=\"/foodyman-documentation/basic\" element={<FoodyBasic />} />\n      <Route path=\"/foodyman-documentation/server\" element={<FoodyServer />} />\n      <Route path=\"/foodyman-documentation/front\" element={<FoodyFront />} />\n      <Route\n        path=\"/foodyman-documentation/front-qr\"\n        element={<FoodyFrontQr />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-web-qr\"\n        element={<FoodyMandatorySetupWebQr />}\n      />\n      <Route path=\"/foodyman-documentation/admin\" element={<FoodyAdmin />} />\n      <Route\n        path=\"/foodyman-documentation/recommendations\"\n        element={<Recommendations />}\n      />\n      <Route\n        path=\"/foodyman-documentation/local-front\"\n        element={<FoodyLocalFront />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mobile-app\"\n        element={<FoodyMobileApp pageTitle=\"Customer App\" />}\n      />\n      <Route\n        path=\"/foodyman-documentation/vendor-app\"\n        element={<FoodyMobileApp pageTitle=\"Vendor App\" />}\n      />\n      <Route\n        path=\"/foodyman-documentation/driver-app\"\n        element={<FoodyMobileApp pageTitle=\"Driver App\" />}\n      />\n      <Route\n        path=\"/foodyman-documentation/pos-app\"\n        element={<FoodyMobileApp pageTitle=\"Pos App\" />}\n      />\n      <Route\n        path=\"/foodyman-documentation/flutter-sdk\"\n        element={<FoodyFlutterSDK />}\n      />\n      <Route\n        path=\"/foodyman-documentation/local-server\"\n        element={<FoodyLocalServer />}\n      />\n      <Route\n        path=\"/foodyman-documentation/install-on-server\"\n        element={<FoodyInstallOnServer />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup\"\n        element={<FoodyMandatorySetup />}\n      />\n      <Route\n        path=\"/foodyman-documentation/customization\"\n        element={<FoodyCustomization />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-mobile\"\n        element={<FoodyMandatorySetupMobile />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-backend\"\n        element={<FoodyMandatorySetupBackend />}\n      />\n      <Route\n        path=\"/foodyman-documentation/payment-installation\"\n        element={<PaymentInstallation />}\n      />\n      <Route\n        path=\"/foodyman-documentation/image-settings\"\n        element={<FoodyImageSettings />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-vendor\"\n        element={<FoodyMandatorySetupVendor />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-customer\"\n        element={<FoodyMandatorySetupCustomer />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-pos\"\n        element={<FoodyMandatorySetupPos />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-deliveryboy\"\n        element={<FoodyMandatorySetupDeliveryboy />}\n      />\n      <Route\n        path=\"/foodyman-documentation/customization-vendor\"\n        element={<FoodyCustomizationMobile />}\n      />\n      <Route\n        path=\"/foodyman-documentation/customization-deliveryboy\"\n        element={<CustomizationMobileDelivery />}\n      />\n      <Route\n        path=\"/foodyman-documentation/customization-customer\"\n        element={<CustomizationMobileCustomer />}\n      />\n      <Route\n        path=\"/foodyman-documentation/customization-pos\"\n        element={<CustomizationMobilePos />}\n      />\n      <Route\n        path=\"/foodyman-documentation/customer-app-build-release\"\n        element={<AppBuildReleaseCustomer />}\n      />\n      <Route\n        path=\"/foodyman-documentation/pos-app-build-release\"\n        element={<AppBuildReleasePos />}\n      />\n      <Route\n        path=\"/foodyman-documentation/vendor-app-build-release\"\n        element={<AppBuildReleaseVendor />}\n      />\n      <Route\n        path=\"/foodyman-documentation/deliveryboy-app-build-release\"\n        element={<AppBuildReleaseDelivery />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-web\"\n        element={<FoodyMandatorySetupWeb />}\n      />\n      <Route\n        path=\"/foodyman-documentation/build-code-and-setup-on-server\"\n        element={<FoodyBuildCodeAndSetupOnServer />}\n      />\n      <Route\n        path=\"/foodyman-documentation/build-code-and-setup-on-server-backend\"\n        element={<FoodyBuildCodeAndSetupOnServerBackend />}\n      />\n      <Route\n        path=\"/foodyman-documentation/update-admin-panel\"\n        element={<FoodyUpdateAdminPanel />}\n      />\n      <Route\n        path=\"/foodyman-documentation/update-app-web\"\n        element={<FoodyUpdateAppWeb />}\n      />\n      <Route\n        path=\"/foodyman-documentation/support-plan\"\n        element={<FoodySupportPlan />}\n      />\n      <Route\n        path=\"/foodyman-documentation/firebase\"\n        element={<FoodyFirebaseSetup />}\n      />\n\n      <Route\n        path=\"/foodyman-documentation/troubleshooting-backend\"\n        element={<TroubleshootingBackend />}\n      />\n      <Route\n        path=\"/foodyman-documentation/troubleshooting-admin\"\n        element={<TroubleshootingAdmin />}\n      />\n      <Route\n        path=\"/foodyman-documentation/update\"\n        element={<UpdateFooyman />}\n      />\n      <Route\n        path=\"/foodyman-documentation/kiosk-app\"\n        element={<FoodyMobileApp pageTitle=\"Kiosk App\" />}\n      />\n      <Route\n        path=\"/foodyman-documentation/mandatory-setup-kiosk\"\n        element={<MandatorySetupKiosk />}\n      />\n      <Route\n        path=\"/foodyman-documentation/customization-kiosk\"\n        element={<CustomizationMobileKiosk />}\n      />\n      <Route\n        path=\"/foodyman-documentation/kiosk-app-build-release\"\n        element={<AppBuildReleaseSingleKiosk />}\n      />\n      <Route path=\"/foodyman-documentation/qr-app\" element={<FoodyQRApp />} />\n      <Route\n        path=\"/foodyman-documentation/install-qrcode\"\n        element={<FoodyInstallQRcode />}\n      />\n    </Route>\n  );\n};\n\nexport default Foodyman;\n", "import React from \"react\";\nimport NotFound from \"../views/NotFound\";\nimport { HashRouter, Route, Routes } from \"react-router-dom\";\nimport ScrollTopBehaviour from \"../components/ScrollTopBehaviour\";\nimport Foodyman from \"./Routing-foodyman\";\n\nconst Routing = () => {\n  return (\n    <>\n      <HashRouter>\n        <ScrollTopBehaviour />\n        <Routes>\n          {Foodyman()}\n          {/*page notfound*/}\n          <Route path=\"*\" element={<NotFound />} />\n        </Routes>\n      </HashRouter>\n    </>\n  );\n};\n\nexport default Routing;\n", "import React, { useEffect } from \"react\";\nimport ScrollToTop from \"./components/ScrollToTop\";\nimport AOS from \"aos\";\nimport \"slick-carousel/slick/slick.css\";\nimport \"slick-carousel/slick/slick-theme.css\";\nimport \"aos/dist/aos.css\";\nimport { Helmet } from \"react-helmet\";\nimport { AuthProvider } from \"./context/AuthContext\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { ToastContainer } from \"react-toastify\";\nimport Routing from \"./router/Routing\";\n\nconst App = () => {\n  useEffect(() => {\n    AOS.init({\n      duration: 1200,\n    });\n  }, []);\n\n  return (\n    <AuthProvider>\n      <>\n        <Helmet>\n          <title>GitHubit</title>\n          <meta name=\"description\" content=\"Githubit\" />\n          <meta\n            name=\"keywords\"\n            content=\"agency, business, clean, corporate, creative, fullpage, minimal, modern, multipurpose, parallax, personal, photography, portfolio, showcase\"\n          />\n        </Helmet>\n        <ScrollToTop />\n        <ToastContainer\n          position=\"top-right\"\n          autoClose={3000}\n          hideProgressBar\n          closeOnClick\n          pauseOnHover\n          draggable\n        />\n        <Routing />\n      </>\n    </AuthProvider>\n  );\n};\n\nexport default App;\n", "const reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n", "import auth from './slices/auth';\n\nconst rootReducer = {\n  auth,\n};\n\nexport default rootReducer;\n", "import { configureStore, combineReducers } from '@reduxjs/toolkit';\nimport {\n  persistStore,\n  persistReducer,\n  FLUSH,\n  REHYDRATE,\n  PAUSE,\n  PERSIST,\n  PURGE,\n  REGISTER,\n} from 'redux-persist';\nimport storage from 'redux-persist/lib/storage';\n\nimport rootReducer from './rootReducer';\n\nconst authPersistConfig = {\n  key: 'auth',\n  storage,\n  whitelist: ['user'],\n};\n\n\nconst persistedReducer = combineReducers({\n  ...rootReducer,\n  auth: persistReducer(authPersistConfig, rootReducer.auth),\n});\n\nexport const store = configureStore({\n  reducer: persistedReducer,\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],\n      },\n    }),\n});\n\nexport const persistor = persistStore(store);\n", "import React from \"react\";\nimport { render } from \"react-dom\";\nimport App from \"./App\";\nimport reportWebVitals from \"./reportWebVitals\";\nimport \"./assets/scss/main.scss\";\n\nimport { store } from \"./redux/store\";\nimport { Provider } from \"react-redux\";\nimport \"bootstrap/dist/js/bootstrap.min\";\nimport \"rc-pagination/assets/index.css\";\nimport \"react-modal-video/scss/modal-video.scss\";\n\nconst Root = () => (\n  <Provider store={store}>\n    <App />\n  </Provider>\n);\n\nrender(<Root />, document.getElementById(\"root\"));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "sourceRoot": ""}