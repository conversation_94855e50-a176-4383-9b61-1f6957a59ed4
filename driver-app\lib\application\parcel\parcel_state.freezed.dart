// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'parcel_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ParcelState {
  bool get isActiveLoading => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isAvailableLoading => throw _privateConstructorUsedError;
  bool get isHistoryLoading => throw _privateConstructorUsedError;
  bool get paymentType => throw _privateConstructorUsedError;
  ParcelOrder? get order => throw _privateConstructorUsedError;
  List<ParcelOrder> get activeOrders => throw _privateConstructorUsedError;
  List<ParcelOrder> get availableOrders => throw _privateConstructorUsedError;
  List<ParcelOrder> get historyOrders => throw _privateConstructorUsedError;
  num get totalActiveOrder => throw _privateConstructorUsedError;
  int get deliveryTime => throw _privateConstructorUsedError;
  int get deliveryType => throw _privateConstructorUsedError;

  /// Create a copy of ParcelState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ParcelStateCopyWith<ParcelState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ParcelStateCopyWith<$Res> {
  factory $ParcelStateCopyWith(
          ParcelState value, $Res Function(ParcelState) then) =
      _$ParcelStateCopyWithImpl<$Res, ParcelState>;
  @useResult
  $Res call(
      {bool isActiveLoading,
      bool isLoading,
      bool isAvailableLoading,
      bool isHistoryLoading,
      bool paymentType,
      ParcelOrder? order,
      List<ParcelOrder> activeOrders,
      List<ParcelOrder> availableOrders,
      List<ParcelOrder> historyOrders,
      num totalActiveOrder,
      int deliveryTime,
      int deliveryType});
}

/// @nodoc
class _$ParcelStateCopyWithImpl<$Res, $Val extends ParcelState>
    implements $ParcelStateCopyWith<$Res> {
  _$ParcelStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ParcelState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActiveLoading = null,
    Object? isLoading = null,
    Object? isAvailableLoading = null,
    Object? isHistoryLoading = null,
    Object? paymentType = null,
    Object? order = freezed,
    Object? activeOrders = null,
    Object? availableOrders = null,
    Object? historyOrders = null,
    Object? totalActiveOrder = null,
    Object? deliveryTime = null,
    Object? deliveryType = null,
  }) {
    return _then(_value.copyWith(
      isActiveLoading: null == isActiveLoading
          ? _value.isActiveLoading
          : isActiveLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isAvailableLoading: null == isAvailableLoading
          ? _value.isAvailableLoading
          : isAvailableLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isHistoryLoading: null == isHistoryLoading
          ? _value.isHistoryLoading
          : isHistoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      paymentType: null == paymentType
          ? _value.paymentType
          : paymentType // ignore: cast_nullable_to_non_nullable
              as bool,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as ParcelOrder?,
      activeOrders: null == activeOrders
          ? _value.activeOrders
          : activeOrders // ignore: cast_nullable_to_non_nullable
              as List<ParcelOrder>,
      availableOrders: null == availableOrders
          ? _value.availableOrders
          : availableOrders // ignore: cast_nullable_to_non_nullable
              as List<ParcelOrder>,
      historyOrders: null == historyOrders
          ? _value.historyOrders
          : historyOrders // ignore: cast_nullable_to_non_nullable
              as List<ParcelOrder>,
      totalActiveOrder: null == totalActiveOrder
          ? _value.totalActiveOrder
          : totalActiveOrder // ignore: cast_nullable_to_non_nullable
              as num,
      deliveryTime: null == deliveryTime
          ? _value.deliveryTime
          : deliveryTime // ignore: cast_nullable_to_non_nullable
              as int,
      deliveryType: null == deliveryType
          ? _value.deliveryType
          : deliveryType // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ParcelStateImplCopyWith<$Res>
    implements $ParcelStateCopyWith<$Res> {
  factory _$$ParcelStateImplCopyWith(
          _$ParcelStateImpl value, $Res Function(_$ParcelStateImpl) then) =
      __$$ParcelStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isActiveLoading,
      bool isLoading,
      bool isAvailableLoading,
      bool isHistoryLoading,
      bool paymentType,
      ParcelOrder? order,
      List<ParcelOrder> activeOrders,
      List<ParcelOrder> availableOrders,
      List<ParcelOrder> historyOrders,
      num totalActiveOrder,
      int deliveryTime,
      int deliveryType});
}

/// @nodoc
class __$$ParcelStateImplCopyWithImpl<$Res>
    extends _$ParcelStateCopyWithImpl<$Res, _$ParcelStateImpl>
    implements _$$ParcelStateImplCopyWith<$Res> {
  __$$ParcelStateImplCopyWithImpl(
      _$ParcelStateImpl _value, $Res Function(_$ParcelStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ParcelState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActiveLoading = null,
    Object? isLoading = null,
    Object? isAvailableLoading = null,
    Object? isHistoryLoading = null,
    Object? paymentType = null,
    Object? order = freezed,
    Object? activeOrders = null,
    Object? availableOrders = null,
    Object? historyOrders = null,
    Object? totalActiveOrder = null,
    Object? deliveryTime = null,
    Object? deliveryType = null,
  }) {
    return _then(_$ParcelStateImpl(
      isActiveLoading: null == isActiveLoading
          ? _value.isActiveLoading
          : isActiveLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isAvailableLoading: null == isAvailableLoading
          ? _value.isAvailableLoading
          : isAvailableLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isHistoryLoading: null == isHistoryLoading
          ? _value.isHistoryLoading
          : isHistoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      paymentType: null == paymentType
          ? _value.paymentType
          : paymentType // ignore: cast_nullable_to_non_nullable
              as bool,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as ParcelOrder?,
      activeOrders: null == activeOrders
          ? _value._activeOrders
          : activeOrders // ignore: cast_nullable_to_non_nullable
              as List<ParcelOrder>,
      availableOrders: null == availableOrders
          ? _value._availableOrders
          : availableOrders // ignore: cast_nullable_to_non_nullable
              as List<ParcelOrder>,
      historyOrders: null == historyOrders
          ? _value._historyOrders
          : historyOrders // ignore: cast_nullable_to_non_nullable
              as List<ParcelOrder>,
      totalActiveOrder: null == totalActiveOrder
          ? _value.totalActiveOrder
          : totalActiveOrder // ignore: cast_nullable_to_non_nullable
              as num,
      deliveryTime: null == deliveryTime
          ? _value.deliveryTime
          : deliveryTime // ignore: cast_nullable_to_non_nullable
              as int,
      deliveryType: null == deliveryType
          ? _value.deliveryType
          : deliveryType // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ParcelStateImpl extends _ParcelState {
  const _$ParcelStateImpl(
      {this.isActiveLoading = false,
      this.isLoading = false,
      this.isAvailableLoading = false,
      this.isHistoryLoading = false,
      this.paymentType = false,
      this.order = null,
      final List<ParcelOrder> activeOrders = const [],
      final List<ParcelOrder> availableOrders = const [],
      final List<ParcelOrder> historyOrders = const [],
      this.totalActiveOrder = 0,
      this.deliveryTime = 0,
      this.deliveryType = 0})
      : _activeOrders = activeOrders,
        _availableOrders = availableOrders,
        _historyOrders = historyOrders,
        super._();

  @override
  @JsonKey()
  final bool isActiveLoading;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isAvailableLoading;
  @override
  @JsonKey()
  final bool isHistoryLoading;
  @override
  @JsonKey()
  final bool paymentType;
  @override
  @JsonKey()
  final ParcelOrder? order;
  final List<ParcelOrder> _activeOrders;
  @override
  @JsonKey()
  List<ParcelOrder> get activeOrders {
    if (_activeOrders is EqualUnmodifiableListView) return _activeOrders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_activeOrders);
  }

  final List<ParcelOrder> _availableOrders;
  @override
  @JsonKey()
  List<ParcelOrder> get availableOrders {
    if (_availableOrders is EqualUnmodifiableListView) return _availableOrders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableOrders);
  }

  final List<ParcelOrder> _historyOrders;
  @override
  @JsonKey()
  List<ParcelOrder> get historyOrders {
    if (_historyOrders is EqualUnmodifiableListView) return _historyOrders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_historyOrders);
  }

  @override
  @JsonKey()
  final num totalActiveOrder;
  @override
  @JsonKey()
  final int deliveryTime;
  @override
  @JsonKey()
  final int deliveryType;

  @override
  String toString() {
    return 'ParcelState(isActiveLoading: $isActiveLoading, isLoading: $isLoading, isAvailableLoading: $isAvailableLoading, isHistoryLoading: $isHistoryLoading, paymentType: $paymentType, order: $order, activeOrders: $activeOrders, availableOrders: $availableOrders, historyOrders: $historyOrders, totalActiveOrder: $totalActiveOrder, deliveryTime: $deliveryTime, deliveryType: $deliveryType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ParcelStateImpl &&
            (identical(other.isActiveLoading, isActiveLoading) ||
                other.isActiveLoading == isActiveLoading) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isAvailableLoading, isAvailableLoading) ||
                other.isAvailableLoading == isAvailableLoading) &&
            (identical(other.isHistoryLoading, isHistoryLoading) ||
                other.isHistoryLoading == isHistoryLoading) &&
            (identical(other.paymentType, paymentType) ||
                other.paymentType == paymentType) &&
            (identical(other.order, order) || other.order == order) &&
            const DeepCollectionEquality()
                .equals(other._activeOrders, _activeOrders) &&
            const DeepCollectionEquality()
                .equals(other._availableOrders, _availableOrders) &&
            const DeepCollectionEquality()
                .equals(other._historyOrders, _historyOrders) &&
            (identical(other.totalActiveOrder, totalActiveOrder) ||
                other.totalActiveOrder == totalActiveOrder) &&
            (identical(other.deliveryTime, deliveryTime) ||
                other.deliveryTime == deliveryTime) &&
            (identical(other.deliveryType, deliveryType) ||
                other.deliveryType == deliveryType));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isActiveLoading,
      isLoading,
      isAvailableLoading,
      isHistoryLoading,
      paymentType,
      order,
      const DeepCollectionEquality().hash(_activeOrders),
      const DeepCollectionEquality().hash(_availableOrders),
      const DeepCollectionEquality().hash(_historyOrders),
      totalActiveOrder,
      deliveryTime,
      deliveryType);

  /// Create a copy of ParcelState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ParcelStateImplCopyWith<_$ParcelStateImpl> get copyWith =>
      __$$ParcelStateImplCopyWithImpl<_$ParcelStateImpl>(this, _$identity);
}

abstract class _ParcelState extends ParcelState {
  const factory _ParcelState(
      {final bool isActiveLoading,
      final bool isLoading,
      final bool isAvailableLoading,
      final bool isHistoryLoading,
      final bool paymentType,
      final ParcelOrder? order,
      final List<ParcelOrder> activeOrders,
      final List<ParcelOrder> availableOrders,
      final List<ParcelOrder> historyOrders,
      final num totalActiveOrder,
      final int deliveryTime,
      final int deliveryType}) = _$ParcelStateImpl;
  const _ParcelState._() : super._();

  @override
  bool get isActiveLoading;
  @override
  bool get isLoading;
  @override
  bool get isAvailableLoading;
  @override
  bool get isHistoryLoading;
  @override
  bool get paymentType;
  @override
  ParcelOrder? get order;
  @override
  List<ParcelOrder> get activeOrders;
  @override
  List<ParcelOrder> get availableOrders;
  @override
  List<ParcelOrder> get historyOrders;
  @override
  num get totalActiveOrder;
  @override
  int get deliveryTime;
  @override
  int get deliveryType;

  /// Create a copy of ParcelState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ParcelStateImplCopyWith<_$ParcelStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
