<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Driver App</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>driver_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.723986947199-p9q94golmggfqunhlvet7ambhv56uepc</string>
				<string>fb3416398941970171</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>15</string>
	<key>FacebookAppID</key>
	<string>3416398941970171</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Foodyman</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>sms</string>
		<string>tel</string>
		<string>comgooglemaps</string>
		<string>baidumap</string>
		<string>iosamap</string>
		<string>waze</string>
		<string>yandexmaps</string>
		<string>yandexnavi</string>
		<string>citymapper</string>
		<string>mapswithme</string>
		<string>osmandmaps</string>
		<string>dgis</string>
		<string>qqmap</string>
		<string>here-location</string>
		<string>tomtomgo</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Allowing the access to your camera helps us to show you the media files to upload to Shop app</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>To see your position on the map, get directions, estimate travel times and search nearby places</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>To see your position on the map, get directions, estimate travel times and search nearby places</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires access to the Microphone.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allowing the access to your photo library helps us to show you the media files to upload to Shop app</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
