<?php

use App\Enums\BookingStatusType;
use App\Enums\ResidenceStatusType;

return [

    /*
    |--------------------------------------------------------------------------
    | Pagination Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used by the paginator library to build
    | the simple pagination links. You are free to change them to anything
    | you want to customize your views to better match your application.
    |
    */


    ResidenceStatusType::Created->name => 'Criado',
    ResidenceStatusType::Edited->name => 'Editado',
    ResidenceStatusType::Approved->name => 'Aprovado',
    ResidenceStatusType::Rejected->name => 'Rejeitado',
    BookingStatusType::Canceled->name => 'Cancelado',
    BookingStatusType::Paid->name => 'Pago',

    'booking_successfully_created' => 'Reserva realizada com sucesso',
    'booking_successfully_updated' => 'Reserva atualizada com sucesso',
    'booking_found' => 'Reserva encontrada',

    'residence_successfully_created' => 'Residência criada com sucesso',
    'residence_successfully_updated' => 'Residência atualizada com sucesso',
    'residence_found' => 'Residência encontrada',

    'user_details_successfully_updated' => 'Detalhes do usuário atualizados com sucesso',

    'guide_successfully_created' => 'Guia criado com sucesso',
    'guide_successfully_updated' => 'Guia atualizado com sucesso',
    'guide_found' => 'Guia encontrado',

    'transport_successfully_created' => 'Transporte criado com sucesso',
    'transport_successfully_updated' => 'Transporte atualizado com sucesso',
    'transport_found' => 'Transporte encontrado',
];
