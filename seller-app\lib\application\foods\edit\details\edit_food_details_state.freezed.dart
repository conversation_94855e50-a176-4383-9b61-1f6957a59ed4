// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_food_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EditFoodDetailsState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get active => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get interval => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get minQty => throw _privateConstructorUsedError;
  String get maxQty => throw _privateConstructorUsedError;
  String get tax => throw _privateConstructorUsedError;
  String get barcode => throw _privateConstructorUsedError;
  ProductData? get product => throw _privateConstructorUsedError;
  List<String> get images => throw _privateConstructorUsedError;
  List<Galleries> get listOfUrls => throw _privateConstructorUsedError;
  Map<String, List<String>> get mapOfDesc => throw _privateConstructorUsedError;
  LanguageData? get language => throw _privateConstructorUsedError;

  /// Create a copy of EditFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditFoodDetailsStateCopyWith<EditFoodDetailsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditFoodDetailsStateCopyWith<$Res> {
  factory $EditFoodDetailsStateCopyWith(EditFoodDetailsState value,
          $Res Function(EditFoodDetailsState) then) =
      _$EditFoodDetailsStateCopyWithImpl<$Res, EditFoodDetailsState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool active,
      String title,
      String interval,
      String description,
      String minQty,
      String maxQty,
      String tax,
      String barcode,
      ProductData? product,
      List<String> images,
      List<Galleries> listOfUrls,
      Map<String, List<String>> mapOfDesc,
      LanguageData? language});
}

/// @nodoc
class _$EditFoodDetailsStateCopyWithImpl<$Res,
        $Val extends EditFoodDetailsState>
    implements $EditFoodDetailsStateCopyWith<$Res> {
  _$EditFoodDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? active = null,
    Object? title = null,
    Object? interval = null,
    Object? description = null,
    Object? minQty = null,
    Object? maxQty = null,
    Object? tax = null,
    Object? barcode = null,
    Object? product = freezed,
    Object? images = null,
    Object? listOfUrls = null,
    Object? mapOfDesc = null,
    Object? language = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      active: null == active
          ? _value.active
          : active // ignore: cast_nullable_to_non_nullable
              as bool,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      interval: null == interval
          ? _value.interval
          : interval // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      minQty: null == minQty
          ? _value.minQty
          : minQty // ignore: cast_nullable_to_non_nullable
              as String,
      maxQty: null == maxQty
          ? _value.maxQty
          : maxQty // ignore: cast_nullable_to_non_nullable
              as String,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as String,
      barcode: null == barcode
          ? _value.barcode
          : barcode // ignore: cast_nullable_to_non_nullable
              as String,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductData?,
      images: null == images
          ? _value.images
          : images // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listOfUrls: null == listOfUrls
          ? _value.listOfUrls
          : listOfUrls // ignore: cast_nullable_to_non_nullable
              as List<Galleries>,
      mapOfDesc: null == mapOfDesc
          ? _value.mapOfDesc
          : mapOfDesc // ignore: cast_nullable_to_non_nullable
              as Map<String, List<String>>,
      language: freezed == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as LanguageData?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditFoodDetailsStateImplCopyWith<$Res>
    implements $EditFoodDetailsStateCopyWith<$Res> {
  factory _$$EditFoodDetailsStateImplCopyWith(_$EditFoodDetailsStateImpl value,
          $Res Function(_$EditFoodDetailsStateImpl) then) =
      __$$EditFoodDetailsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool active,
      String title,
      String interval,
      String description,
      String minQty,
      String maxQty,
      String tax,
      String barcode,
      ProductData? product,
      List<String> images,
      List<Galleries> listOfUrls,
      Map<String, List<String>> mapOfDesc,
      LanguageData? language});
}

/// @nodoc
class __$$EditFoodDetailsStateImplCopyWithImpl<$Res>
    extends _$EditFoodDetailsStateCopyWithImpl<$Res, _$EditFoodDetailsStateImpl>
    implements _$$EditFoodDetailsStateImplCopyWith<$Res> {
  __$$EditFoodDetailsStateImplCopyWithImpl(_$EditFoodDetailsStateImpl _value,
      $Res Function(_$EditFoodDetailsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? active = null,
    Object? title = null,
    Object? interval = null,
    Object? description = null,
    Object? minQty = null,
    Object? maxQty = null,
    Object? tax = null,
    Object? barcode = null,
    Object? product = freezed,
    Object? images = null,
    Object? listOfUrls = null,
    Object? mapOfDesc = null,
    Object? language = freezed,
  }) {
    return _then(_$EditFoodDetailsStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      active: null == active
          ? _value.active
          : active // ignore: cast_nullable_to_non_nullable
              as bool,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      interval: null == interval
          ? _value.interval
          : interval // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      minQty: null == minQty
          ? _value.minQty
          : minQty // ignore: cast_nullable_to_non_nullable
              as String,
      maxQty: null == maxQty
          ? _value.maxQty
          : maxQty // ignore: cast_nullable_to_non_nullable
              as String,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as String,
      barcode: null == barcode
          ? _value.barcode
          : barcode // ignore: cast_nullable_to_non_nullable
              as String,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductData?,
      images: null == images
          ? _value._images
          : images // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listOfUrls: null == listOfUrls
          ? _value._listOfUrls
          : listOfUrls // ignore: cast_nullable_to_non_nullable
              as List<Galleries>,
      mapOfDesc: null == mapOfDesc
          ? _value._mapOfDesc
          : mapOfDesc // ignore: cast_nullable_to_non_nullable
              as Map<String, List<String>>,
      language: freezed == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as LanguageData?,
    ));
  }
}

/// @nodoc

class _$EditFoodDetailsStateImpl extends _EditFoodDetailsState {
  const _$EditFoodDetailsStateImpl(
      {this.isLoading = false,
      this.active = false,
      this.title = '',
      this.interval = '',
      this.description = '',
      this.minQty = '',
      this.maxQty = '',
      this.tax = '',
      this.barcode = '',
      this.product,
      final List<String> images = const [],
      final List<Galleries> listOfUrls = const [],
      final Map<String, List<String>> mapOfDesc = const {},
      this.language})
      : _images = images,
        _listOfUrls = listOfUrls,
        _mapOfDesc = mapOfDesc,
        super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool active;
  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final String interval;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final String minQty;
  @override
  @JsonKey()
  final String maxQty;
  @override
  @JsonKey()
  final String tax;
  @override
  @JsonKey()
  final String barcode;
  @override
  final ProductData? product;
  final List<String> _images;
  @override
  @JsonKey()
  List<String> get images {
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_images);
  }

  final List<Galleries> _listOfUrls;
  @override
  @JsonKey()
  List<Galleries> get listOfUrls {
    if (_listOfUrls is EqualUnmodifiableListView) return _listOfUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listOfUrls);
  }

  final Map<String, List<String>> _mapOfDesc;
  @override
  @JsonKey()
  Map<String, List<String>> get mapOfDesc {
    if (_mapOfDesc is EqualUnmodifiableMapView) return _mapOfDesc;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_mapOfDesc);
  }

  @override
  final LanguageData? language;

  @override
  String toString() {
    return 'EditFoodDetailsState(isLoading: $isLoading, active: $active, title: $title, interval: $interval, description: $description, minQty: $minQty, maxQty: $maxQty, tax: $tax, barcode: $barcode, product: $product, images: $images, listOfUrls: $listOfUrls, mapOfDesc: $mapOfDesc, language: $language)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditFoodDetailsStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.active, active) || other.active == active) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.interval, interval) ||
                other.interval == interval) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.minQty, minQty) || other.minQty == minQty) &&
            (identical(other.maxQty, maxQty) || other.maxQty == maxQty) &&
            (identical(other.tax, tax) || other.tax == tax) &&
            (identical(other.barcode, barcode) || other.barcode == barcode) &&
            (identical(other.product, product) || other.product == product) &&
            const DeepCollectionEquality().equals(other._images, _images) &&
            const DeepCollectionEquality()
                .equals(other._listOfUrls, _listOfUrls) &&
            const DeepCollectionEquality()
                .equals(other._mapOfDesc, _mapOfDesc) &&
            (identical(other.language, language) ||
                other.language == language));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      active,
      title,
      interval,
      description,
      minQty,
      maxQty,
      tax,
      barcode,
      product,
      const DeepCollectionEquality().hash(_images),
      const DeepCollectionEquality().hash(_listOfUrls),
      const DeepCollectionEquality().hash(_mapOfDesc),
      language);

  /// Create a copy of EditFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditFoodDetailsStateImplCopyWith<_$EditFoodDetailsStateImpl>
      get copyWith =>
          __$$EditFoodDetailsStateImplCopyWithImpl<_$EditFoodDetailsStateImpl>(
              this, _$identity);
}

abstract class _EditFoodDetailsState extends EditFoodDetailsState {
  const factory _EditFoodDetailsState(
      {final bool isLoading,
      final bool active,
      final String title,
      final String interval,
      final String description,
      final String minQty,
      final String maxQty,
      final String tax,
      final String barcode,
      final ProductData? product,
      final List<String> images,
      final List<Galleries> listOfUrls,
      final Map<String, List<String>> mapOfDesc,
      final LanguageData? language}) = _$EditFoodDetailsStateImpl;
  const _EditFoodDetailsState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get active;
  @override
  String get title;
  @override
  String get interval;
  @override
  String get description;
  @override
  String get minQty;
  @override
  String get maxQty;
  @override
  String get tax;
  @override
  String get barcode;
  @override
  ProductData? get product;
  @override
  List<String> get images;
  @override
  List<Galleries> get listOfUrls;
  @override
  Map<String, List<String>> get mapOfDesc;
  @override
  LanguageData? get language;

  /// Create a copy of EditFoodDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditFoodDetailsStateImplCopyWith<_$EditFoodDetailsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
