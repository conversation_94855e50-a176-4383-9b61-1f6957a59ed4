enum OrderStatus {
  newOrder,
  accepted,
  ready,
  onAWay,
  delivered,
  canceled,
}

enum SnackBarType { success, info, error }

enum ExtrasType { color, text, image }

enum UploadType {
  extras,
  brands,
  categories,
  shopsLogo,
  shopsBack,
  products,
  reviews,
  users,
}

enum ProductStatus { published, pending, unpublished }

enum WeekDays {
  monday,
  tuesday,
  wednesday,
  thursday,
  friday,
  saturday,
  sunday,
}
